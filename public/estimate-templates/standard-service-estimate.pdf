
%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
  /Font <<
    /F1 5 0 R
    /F2 6 0 R
  >>
>>
>>
endobj

4 0 obj
<<
/Length 2800
>>
stream
BT
/F1 18 Tf
50 750 Td
(STANDARD SERVICE ESTIMATE) Tj
0 -30 Td
/F2 12 Tf
(Your Business Name: _________________________________) Tj
0 -20 Td
(Address: ___________________________________________) Tj
0 -20 Td
(Phone: _________________ Email: ____________________) Tj
0 -20 Td
(License #: _________________ Date: __________________) Tj

0 -40 Td
/F1 14 Tf
(CUSTOMER INFORMATION) Tj
0 -25 Td
/F2 12 Tf
(Name: _____________________________________________) Tj
0 -20 Td
(Address: ___________________________________________) Tj
0 -20 Td
(Phone: _________________ Email: ____________________) Tj

0 -40 Td
/F1 14 Tf
(SERVICE DETAILS) Tj
0 -25 Td
/F2 12 Tf
(Description of Work:) Tj
0 -20 Td
(_________________________________________________) Tj
0 -20 Td
(_________________________________________________) Tj
0 -20 Td
(_________________________________________________) Tj

0 -30 Td
/F1 14 Tf
(COST BREAKDOWN) Tj
0 -25 Td
/F2 12 Tf
(Labor:                              $__________) Tj
0 -20 Td
(Materials:                          $__________) Tj
0 -20 Td
(Equipment:                          $__________) Tj
0 -20 Td
(Permits/Fees:                       $__________) Tj
0 -20 Td
(Tax:                                $__________) Tj
0 -25 Td
/F1 12 Tf
(TOTAL ESTIMATE:                     $__________) Tj

0 -40 Td
/F2 10 Tf
(This estimate is valid for 30 days from the date above.) Tj
0 -15 Td
(All work will be completed according to local codes and regulations.) Tj
0 -15 Td
(Customer signature required before work begins.) Tj

0 -30 Td
(Customer Signature: ________________________ Date: ________) Tj
0 -20 Td
(Contractor Signature: ______________________ Date: ________) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj

6 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 7
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000245 00000 n 
0000003098 00000 n 
0000003175 00000 n 
trailer
<<
/Size 7
/Root 1 0 R
>>
startxref
3249
%%EOF
