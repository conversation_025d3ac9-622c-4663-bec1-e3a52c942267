import { defineConfig } from "vite";
import type { ConfigEnv, UserConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { sitemapPlugin } from "./scripts/vite-plugin-sitemap.js";

// https://vitejs.dev/config/
export default defineConfig(({ mode }: ConfigEnv): UserConfig => ({
  server: {
    host: "::",
    port: 8080,
    proxy: {
      '/api': {
        target: 'https://dash.jobon.app',
        changeOrigin: true,
        secure: true,
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (_proxyReq, req, _res) => {
            // Log proxy requests for debugging
            console.log('Proxying:', req.method, req.url);
          });
        },
        // Handle CORS preflight requests
        headers: {
          'Access-Control-Allow-Origin': 'http://localhost:8080',
          'Access-Control-Allow-Credentials': 'true',
        }
      }
    },
  },
  plugins: [
    react(),
    // @ts-ignore - The sitemap plugin has a string 'build' for apply property
    sitemapPlugin(), // Add sitemap generator plugin
    ...(mode === 'development' ? [componentTagger()] : []),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  }
}))
