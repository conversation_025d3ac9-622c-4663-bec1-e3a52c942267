// @ts-ignore - This is used because of a potential issue with @types/mapbox__mapbox-sdk not correctly declaring the module for services/geocoding.
// This allows the application to compile while the type definition issue can be investigated separately.
import MapboxClient from '@mapbox/mapbox-sdk/services/geocoding';

const mapboxToken = import.meta.env.VITE_MAPBOX_TOKEN;

if (!mapboxToken) {
  // For Vite projects, environment variables exposed to the client-side code (import.meta.env)
  // must be prefixed with VITE_. Ensure your .env file has VITE_MAPBOX_TOKEN=your_actual_token.
  console.error('Mapbox token is not set. Please set VITE_MAPBOX_TOKEN in your .env file.');
}

// The MapboxClient is instantiated here. If mapboxToken is undefined (e.g., not set in .env),
// an empty string is passed as accessToken, which will likely cause runtime errors when making API calls.
// The console error above should alert the developer.
const mapboxClient = MapboxClient({ accessToken: mapboxToken || '' });

export default mapboxClient;
