
import { v4 as uuidv4 } from 'uuid';
import { Provider } from '@/components/ProviderCard';

// Storage keys
const ADMIN_MESSAGES_KEY = 'admin_messages';
const ADMIN_CONVERSATIONS_KEY = 'admin_conversations';
const USER_INFO_KEY = 'user_info';
const PROVIDER_TO_MESSAGE_KEY = 'provider_to_message';

// User Related Functions
export const isUserRegistered = (): boolean => {
  return localStorage.getItem(USER_INFO_KEY) !== null;
};

export const saveUserInfo = (userInfo: any): void => {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
};

export const getUserInfo = (): any => {
  const userInfo = localStorage.getItem(USER_INFO_KEY);
  return userInfo ? JSON.parse(userInfo) : null;
};

// Provider Message Functions
export const saveProviderToMessage = (provider: Provider): void => {
  localStorage.setItem(PROVIDER_TO_MESSAGE_KEY, JSON.stringify(provider));
};

export const getProviderToMessage = (): Provider | null => {
  const provider = localStorage.getItem(PROVIDER_TO_MESSAGE_KEY);
  return provider ? JSON.parse(provider) : null;
};

export const clearProviderToMessage = (): void => {
  localStorage.removeItem(PROVIDER_TO_MESSAGE_KEY);
};

// Admin message types
export interface AdminMessage {
  id: string;
  recipientId: string;
  recipientType: 'customer' | 'provider';
  recipientName?: string;
  content: string;
  timestamp: string;
  isFromAdmin: boolean;
  read: boolean;
}

export interface AdminRecipient {
  id: string;
  type: 'customer' | 'provider';
  name: string;
  avatar?: string;
}

export interface AdminConversation {
  id: string;
  recipientId: string;
  recipientType: 'customer' | 'provider';
  recipientName: string;
  recipientAvatar?: string;
  lastMessage: string;
  timestamp: string; // Using this as lastMessageTimestamp
  unread: boolean;
  flagged: boolean;
  pinned?: boolean; // Added pinned property
}

// Initialize mock data
export const initializeAdminMessagingData = (): void => {
  if (!localStorage.getItem(ADMIN_MESSAGES_KEY)) {
    // Helper function to ensure all timestamps are in valid ISO format
    const createValidTimestamp = (offsetInMs: number): string => {
      return new Date(Date.now() - offsetInMs).toISOString();
    };
    
    const mockMessages: AdminMessage[] = [
      {
        id: uuidv4(),
        recipientId: 'customer1',
        recipientType: 'customer',
        recipientName: 'John Doe',
        content: 'Hello John, I noticed you had some questions about your recent booking?',
        timestamp: createValidTimestamp(86400000), // 1 day ago
        isFromAdmin: true,
        read: true
      },
      {
        id: uuidv4(),
        recipientId: 'customer1',
        recipientType: 'customer',
        recipientName: 'John Doe',
        content: 'Yes, I was wondering if I could change the appointment time?',
        timestamp: createValidTimestamp(43200000), // 12 hours ago
        isFromAdmin: false,
        read: true
      },
      {
        id: uuidv4(),
        recipientId: 'provider1',
        recipientType: 'provider',
        recipientName: 'Jane Smith',
        content: 'Hi Jane, just checking if you received the updated guidelines?',
        timestamp: createValidTimestamp(3600000), // 1 hour ago
        isFromAdmin: true,
        read: true
      },
      {
        id: uuidv4(),
        recipientId: 'provider1',
        recipientType: 'provider',
        recipientName: 'Jane Smith',
        content: "Yes, I've reviewed them. I have a question about section 3.",
        timestamp: createValidTimestamp(1800000), // 30 minutes ago
        isFromAdmin: false,
        read: false
      },
      {
        id: uuidv4(),
        recipientId: 'customer2',
        recipientType: 'customer',
        recipientName: 'Sarah Wilson',
        content: 'Hello Sarah, we received your support request. How can we help?',
        timestamp: createValidTimestamp(0), // now
        isFromAdmin: true,
        read: false
      }
    ];

    const mockConversations: AdminConversation[] = [
      {
        id: uuidv4(),
        recipientId: 'customer1',
        recipientType: 'customer',
        recipientName: 'John Doe',
        recipientAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop',
        lastMessage: 'Yes, I was wondering if I could change the appointment time?',
        timestamp: createValidTimestamp(43200000),
        unread: false,
        flagged: false,
        pinned: false
      },
      {
        id: uuidv4(),
        recipientId: 'provider1',
        recipientType: 'provider',
        recipientName: 'Jane Smith',
        recipientAvatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=50&h=50&fit=crop',
        lastMessage: "Yes, I've reviewed them. I have a question about section 3.",
        timestamp: createValidTimestamp(1800000),
        unread: true,
        flagged: false,
        pinned: true
      },
      {
        id: uuidv4(),
        recipientId: 'customer2',
        recipientType: 'customer',
        recipientName: 'Sarah Wilson',
        recipientAvatar: '',
        lastMessage: 'Hello Sarah, we received your support request. How can we help?',
        timestamp: createValidTimestamp(0),
        unread: true,
        flagged: true,
        pinned: false
      },
      {
        id: uuidv4(),
        recipientId: 'provider2',
        recipientType: 'provider',
        recipientName: 'Michael Johnson',
        recipientAvatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=50&h=50&fit=crop',
        lastMessage: 'Thanks for the update on the verification process.',
        timestamp: createValidTimestamp(172800000), // 2 days ago
        unread: false,
        flagged: false,
        pinned: false
      },
      {
        id: uuidv4(),
        recipientId: 'customer3',
        recipientType: 'customer',
        recipientName: 'Emily Chen',
        recipientAvatar: '',
        lastMessage: 'Your refund has been processed and should appear in 3-5 business days.',
        timestamp: createValidTimestamp(259200000), // 3 days ago
        unread: false,
        flagged: false,
        pinned: false
      }
    ];

    localStorage.setItem(ADMIN_MESSAGES_KEY, JSON.stringify(mockMessages));
    localStorage.setItem(ADMIN_CONVERSATIONS_KEY, JSON.stringify(mockConversations));
  }
};

// Add validation when retrieving conversations
export const getAdminConversations = (): AdminConversation[] => {
  initializeAdminMessagingData(); // Ensure data is initialized
  
  try {
    const conversations = JSON.parse(localStorage.getItem(ADMIN_CONVERSATIONS_KEY) || '[]') as AdminConversation[];
    
    // Validate timestamps and filter out invalid conversations
    const validConversations = conversations.filter(conv => {
      try {
        // Attempt to create a Date object to validate the timestamp
        const date = new Date(conv.timestamp);
        // Check if date is valid (not Invalid Date)
        return !isNaN(date.getTime());
      } catch (error) {
        console.error(`Invalid conversation timestamp: ${conv.timestamp}`, error);
        return false;
      }
    });
    
    return validConversations.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  } catch (error) {
    console.error("Error getting admin conversations:", error);
    return [];
  }
};

// Get all admin messages
export const getAdminMessages = (recipientId: string, recipientType: string): AdminMessage[] => {
  initializeAdminMessagingData(); // Ensure data is initialized
  
  const messages = JSON.parse(localStorage.getItem(ADMIN_MESSAGES_KEY) || '[]') as AdminMessage[];
  
  // Filter messages for the specific conversation
  return messages.filter(message => message.recipientId === recipientId && message.recipientType === recipientType)
    .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
};

// Add a message from admin to a recipient
export const addAdminMessage = (message: AdminMessage): void => {
  initializeAdminMessagingData(); // Ensure data is initialized
  
  const messages = JSON.parse(localStorage.getItem(ADMIN_MESSAGES_KEY) || '[]') as AdminMessage[];
  const conversations = JSON.parse(localStorage.getItem(ADMIN_CONVERSATIONS_KEY) || '[]') as AdminConversation[];
  
  // Add message to messages
  messages.push(message);
  
  // Update or create conversation
  const existingConversation = conversations.find(
    conv => conv.recipientId === message.recipientId && conv.recipientType === message.recipientType
  );
  
  if (existingConversation) {
    existingConversation.lastMessage = message.content;
    existingConversation.timestamp = message.timestamp;
  } else {
    // Create new conversation
    const newConversation: AdminConversation = {
      id: uuidv4(),
      recipientId: message.recipientId,
      recipientType: message.recipientType,
      recipientName: message.recipientName || 'Unknown',
      lastMessage: message.content,
      timestamp: message.timestamp,
      unread: false,
      flagged: false,
      pinned: false
    };
    conversations.push(newConversation);
  }
  
  // Save updated data
  localStorage.setItem(ADMIN_MESSAGES_KEY, JSON.stringify(messages));
  localStorage.setItem(ADMIN_CONVERSATIONS_KEY, JSON.stringify(conversations));
};

// Mark a conversation as read
export const markConversationAsRead = (recipientId: string, recipientType: string): void => {
  const conversations = JSON.parse(localStorage.getItem(ADMIN_CONVERSATIONS_KEY) || '[]') as AdminConversation[];
  const messages = JSON.parse(localStorage.getItem(ADMIN_MESSAGES_KEY) || '[]') as AdminMessage[];
  
  // Update conversation
  const conversation = conversations.find(
    conv => conv.recipientId === recipientId && conv.recipientType === recipientType
  );
  
  if (conversation) {
    conversation.unread = false;
  }
  
  // Update messages
  messages.forEach(message => {
    if (message.recipientId === recipientId && message.recipientType === recipientType && !message.isFromAdmin) {
      message.read = true;
    }
  });
  
  // Save updated data
  localStorage.setItem(ADMIN_CONVERSATIONS_KEY, JSON.stringify(conversations));
  localStorage.setItem(ADMIN_MESSAGES_KEY, JSON.stringify(messages));
};

// Flag or unflag a conversation
export const toggleConversationFlag = (conversationId: string): void => {
  const conversations = JSON.parse(localStorage.getItem(ADMIN_CONVERSATIONS_KEY) || '[]') as AdminConversation[];
  
  // Update conversation
  const conversation = conversations.find(conv => conv.id === conversationId);
  
  if (conversation) {
    conversation.flagged = !conversation.flagged;
  }
  
  // Save updated data
  localStorage.setItem(ADMIN_CONVERSATIONS_KEY, JSON.stringify(conversations));
};

// Pin or unpin a conversation
export const toggleConversationPin = (conversationId: string): void => {
  const conversations = JSON.parse(localStorage.getItem(ADMIN_CONVERSATIONS_KEY) || '[]') as AdminConversation[];
  
  // Update conversation
  const conversation = conversations.find(conv => conv.id === conversationId);
  
  if (conversation) {
    conversation.pinned = !conversation.pinned;
  }
  
  // Save updated data
  localStorage.setItem(ADMIN_CONVERSATIONS_KEY, JSON.stringify(conversations));
};
