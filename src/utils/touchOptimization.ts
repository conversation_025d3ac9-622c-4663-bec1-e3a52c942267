
/**
 * Touch optimization utilities for mobile-first design
 */

// Minimum touch target size constants
export const TOUCH_TARGET = {
  MINIMUM: 44, // Apple iOS HIG minimum
  RECOMMENDED: 48, // Material Design recommendation
  COMFORTABLE: 56, // Comfortable size for most users
} as const;

// Touch feedback utilities
export const touchFeedback = {
  scale: 'active:scale-95 transition-transform',
  opacity: 'active:opacity-80 transition-opacity',
  background: 'active:bg-gray-100 dark:active:bg-gray-800 transition-colors',
  combined: 'active:scale-95 active:opacity-90 transition-all',
} as const;

// Responsive hover utilities (hover only on non-touch devices)
export const responsiveHover = {
  bg: 'md:hover:bg-gray-100 dark:md:hover:bg-gray-800',
  scale: 'md:hover:scale-105',
  opacity: 'md:hover:opacity-80',
  shadow: 'md:hover:shadow-md',
} as const;

// Touch-optimized spacing
export const touchSpacing = {
  minGap: 'gap-2', // 8px minimum between touch targets
  comfortableGap: 'gap-3', // 12px comfortable spacing
  padding: 'p-3', // 12px internal padding for touch comfort
} as const;

/**
 * Validates if an element meets minimum touch target requirements
 */
export function validateTouchTarget(width: number, height: number): boolean {
  return width >= TOUCH_TARGET.MINIMUM && height >= TOUCH_TARGET.MINIMUM;
}

/**
 * Gets touch-optimized button classes based on size
 */
export function getTouchButtonClasses(size: 'sm' | 'default' | 'lg' = 'default') {
  const baseClasses = 'transition-all active:scale-95';
  
  switch (size) {
    case 'sm':
      return `${baseClasses} min-h-[44px] min-w-[44px]`;
    case 'lg':
      return `${baseClasses} min-h-[56px] min-w-[56px]`;
    default:
      return `${baseClasses} min-h-[48px] min-w-[48px]`;
  }
}

/**
 * Gets touch-optimized navigation item classes
 */
export function getTouchNavClasses() {
  return 'min-h-[48px] min-w-[48px] flex items-center justify-center transition-all active:scale-95';
}
