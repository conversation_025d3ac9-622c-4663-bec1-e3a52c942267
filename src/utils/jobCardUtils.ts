
// Job card utility functions for color accents and view tracking

const ACCENT_COLORS = [
  { 
    border: 'border-l-blue-400', 
    bg: 'bg-blue-50 dark:bg-blue-950/30', 
    icon: 'bg-blue-100 dark:bg-blue-900/50' 
  },
  { 
    border: 'border-l-green-400', 
    bg: 'bg-green-50 dark:bg-green-950/30', 
    icon: 'bg-green-100 dark:bg-green-900/50' 
  },
  { 
    border: 'border-l-purple-400', 
    bg: 'bg-purple-50 dark:bg-purple-950/30', 
    icon: 'bg-purple-100 dark:bg-purple-900/50' 
  },
  { 
    border: 'border-l-orange-400', 
    bg: 'bg-orange-50 dark:bg-orange-950/30', 
    icon: 'bg-orange-100 dark:bg-orange-900/50' 
  },
  { 
    border: 'border-l-indigo-400', 
    bg: 'bg-indigo-50 dark:bg-indigo-950/30', 
    icon: 'bg-indigo-100 dark:bg-indigo-900/50' 
  },
];

// Generate consistent color based on job ID
export const getJobAccentColor = (jobId: string) => {
  const hash = jobId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return ACCENT_COLORS[hash % ACCENT_COLORS.length];
};

// View tracking functions
const VIEWED_JOBS_KEY = 'jobon_viewed_jobs';
const VIEW_EXPIRY_DAYS = 7;

export const markJobAsViewed = (jobId: string) => {
  try {
    const viewed = getViewedJobs();
    viewed[jobId] = Date.now();
    localStorage.setItem(VIEWED_JOBS_KEY, JSON.stringify(viewed));
  } catch (error) {
    console.warn('Failed to save viewed job:', error);
  }
};

export const isJobViewed = (jobId: string): boolean => {
  try {
    const viewed = getViewedJobs();
    const viewedTime = viewed[jobId];
    if (!viewedTime) return false;
    
    // Check if view is still valid (within expiry period)
    const daysSinceViewed = (Date.now() - viewedTime) / (1000 * 60 * 60 * 24);
    return daysSinceViewed <= VIEW_EXPIRY_DAYS;
  } catch (error) {
    return false;
  }
};

const getViewedJobs = (): Record<string, number> => {
  try {
    const stored = localStorage.getItem(VIEWED_JOBS_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    return {};
  }
};

// Clean up expired viewed jobs
export const cleanupExpiredViews = () => {
  try {
    const viewed = getViewedJobs();
    const now = Date.now();
    const filtered: Record<string, number> = {};
    
    Object.entries(viewed).forEach(([jobId, timestamp]) => {
      const daysSince = (now - timestamp) / (1000 * 60 * 60 * 24);
      if (daysSince <= VIEW_EXPIRY_DAYS) {
        filtered[jobId] = timestamp;
      }
    });
    
    localStorage.setItem(VIEWED_JOBS_KEY, JSON.stringify(filtered));
  } catch (error) {
    console.warn('Failed to cleanup expired views:', error);
  }
};

// Format timeline from job data
export const formatJobTimeline = (schedule: any, createdAt: string): string => {
  if (schedule?.date) {
    const scheduleDate = new Date(schedule.date);
    const now = new Date();
    const diffTime = scheduleDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays > 0) return `In ${diffDays} days`;
    return 'Overdue';
  }
  
  // Fallback to creation date
  const created = new Date(createdAt);
  const now = new Date();
  const diffTime = now.getTime() - created.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) return 'Posted today';
  if (diffDays === 1) return 'Posted yesterday';
  return `Posted ${diffDays} days ago`;
};

// Get service-specific placeholder image
export const getServicePlaceholder = (category: string): string => {
  const placeholders: Record<string, string> = {
    'plumbing': 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=200&h=200&fit=crop',
    'electrical': 'https://images.unsplash.com/photo-1518770660439-4636190af475?w=200&h=200&fit=crop',
    'cleaning': 'https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=200&h=200&fit=crop',
    'landscaping': 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=200&h=200&fit=crop',
    'hvac': 'https://images.unsplash.com/photo-1581092795360-fd1ca04f0952?w=200&h=200&fit=crop',
    default: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=200&h=200&fit=crop'
  };
  
  return placeholders[category.toLowerCase()] || placeholders.default;
};
