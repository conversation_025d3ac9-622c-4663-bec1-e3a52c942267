interface Review {
    date: string;
    text: string;
    author: string;
    rating: string;
}

interface BusinessHours {
    monday: string;
    tuesday: string;
    wednesday: string;
    thursday: string;
    friday: string;
    saturday: string;
    sunday: string;
}

export interface DataType {
    businessId: string;
    name: string;
    category: string;
    location: string;
    address: string;
    phone: string;
    website: string;
    email: string | null;
    hours: BusinessHours;
    photos: string[];
    services: [];
    reviews: Review[];
    createdAt: string;
    updatedAt: string;
    badges: string[];
    distance? : string;
    rating?: number;
    reviewCount?: number;
    responseTime?: string;
    specialty?: string;
    fullAddress?: string;
    available? : boolean;
    isFeatured?:boolean
}





interface Review {
    text: string;
    rating: string;
    author: string;
    date: string;
}

export interface ProviderData {
    businessId: string;
    name: string;
    category: string;
    location: string;
    address: string;
    phone: string;
    website: string;
    email: string;
    hours: string[];
    photos: string[];
    services: never[];
    reviews: Review[];
    createdAt: string;
    updatedAt: string;
    isFeatured: boolean;
    key: string;
    badges: string[];
}