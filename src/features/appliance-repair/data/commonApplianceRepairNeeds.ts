import React, { ReactElement } from 'react';
import { <PERSON>frigera<PERSON>, Package2, <PERSON>otateCc<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>bul<PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';

export interface ApplianceRepairNeed {
  icon: ReactElement;
  label: string;
}

export const commonApplianceRepairNeeds: ApplianceRepairNeed[] = [
  {
    icon: React.createElement(Refrigerator, { className: "h-8 w-8 text-primary" }),
    label: "Refrigerator Repair"
  },
  {
    icon: React.createElement(Package2, { className: "h-8 w-8 text-primary" }),
    label: "Washer Repair"
  },
  {
    icon: React.createElement(RotateCcw, { className: "h-8 w-8 text-primary" }),
    label: "Dryer Repair"
  },
  {
    icon: React.createElement(Wrench, { className: "h-8 w-8 text-primary" }),
    label: "Dishwasher Repair"
  },
  {
    icon: React.createElement(Wren<PERSON>, { className: "h-8 w-8 text-primary" }),
    label: "Oven/Range Repair"
  },
  {
    icon: React.createElement(Wren<PERSON>, { className: "h-8 w-8 text-primary" }),
    label: "Microwave Repair"
  },
  {
    icon: React.createElement(Fan, { className: "h-8 w-8 text-primary" }),
    label: "HVAC Repair"
  },
  {
    icon: React.createElement(Lightbulb, { className: "h-8 w-8 text-primary" }),
    label: "Commercial Equipment"
  },
  {
    icon: React.createElement(Plug, { className: "h-8 w-8 text-primary" }),
    label: "Smart Appliance Setup"
  },
  {
    icon: React.createElement(Hammer, { className: "h-8 w-8 text-primary" }),
    label: "Appliance Installation"
  }
];

export default commonApplianceRepairNeeds;
