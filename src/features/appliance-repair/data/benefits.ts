import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Award, Handshake, Users, Wrench } from 'lucide-react';
import React, { ReactElement } from 'react';

export interface Benefit {
  title: string;
  description: string;
  icon: ReactElement;
}

export const benefits: Benefit[] = [
  {
    title: "Certified Appliance Technicians",
    description: "Our appliance repair technicians are fully certified, insured, and trained on all major brands and models for residential and commercial equipment.",
    icon: React.createElement(ShieldCheck, { className: "h-6 w-6 text-primary" })
  },
  {
    title: "Same-Day Service Available",
    description: "Don't wait days with a broken appliance. We offer same-day service options for critical appliance failures in homes, offices, and commercial facilities.",
    icon: React.createElement(Clock, { className: "h-6 w-6 text-primary" })
  },
  {
    title: "Genuine Replacement Parts",
    description: "We use only genuine manufacturer or high-quality OEM parts for all appliance repairs and replacements, whether for home or commercial use.",
    icon: React.createElement(Award, { className: "h-6 w-6 text-primary" })
  },
  {
    title: "Satisfaction Guaranteed",
    description: "If you're not completely satisfied with our appliance repair service for your home or business, we'll make it right – guaranteed.",
    icon: React.createElement(Handshake, { className: "h-6 w-6 text-primary" })
  },
  {
    title: "Experienced Repair Specialists",
    description: "Our technicians have extensive experience diagnosing and repairing all types of household, office, and commercial appliances.",
    icon: React.createElement(Users, { className: "h-6 w-6 text-primary" })
  },
  {
    title: "Upfront, Transparent Pricing",
    description: "Get clear pricing before any work begins. No hidden fees or surprise charges after the repair, for both residential and commercial clients.",
    icon: React.createElement(Wrench, { className: "h-6 w-6 text-primary" })
  }
];

export default benefits;
