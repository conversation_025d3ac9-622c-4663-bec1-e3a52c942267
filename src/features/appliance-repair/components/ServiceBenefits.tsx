import React from 'react';
import { Card, CardContent } from "@/components/ui/card";

interface Benefit {
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface ServiceBenefitsProps {
  benefits: Benefit[];
}

const ServiceBenefits: React.FC<ServiceBenefitsProps> = ({ benefits }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {benefits.map((benefit, index) => (
        <Card key={index} className="border-none shadow-md hover:shadow-lg transition-all">
          <CardContent className="p-6">
            <div className="flex flex-col h-full">
              <div className="mb-4 p-3 bg-primary/10 dark:bg-primary/20 rounded-full w-fit">
                {benefit.icon}
              </div>
              <h3 className="text-xl font-bold mb-2 dark:text-white">{benefit.title}</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm flex-grow">{benefit.description}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ServiceBenefits;
