import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Shield, Award, Clock, Check, ArrowRight } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
const HeroSection: React.FC = () => {
  const isMobile = useIsMobile();
  return <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-background pt-16 pb-6">
      <div className="container mx-auto px-4 lg:px-8">
        {isMobile ? <div className="w-full">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-3">
              <div className="relative h-52 overflow-hidden">
                <img src="https://images.unsplash.com/photo-1556911220-e15b29be8c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Professional technician performing appliance repair" className="w-full h-full object-cover object-center" style={{
                objectPosition: "center 30%"
              }} />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent flex items-end">
                  <div className="p-3 text-white w-full">
                    <h1 className="text-2xl font-bold mb-0.5 text-left">
                      Appliance Repair Pros
                    </h1>
                    <h2 className="text-lg font-medium text-blue-300 mb-1 text-left">
                      <span className="block">Fast, Reliable Service</span>
                      <span className="block">All Major Brands</span>
                    </h2>
                    <div className="flex items-center space-x-2">
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((_, i) => <svg key={i} className="w-3 h-3 text-amber-400 fill-amber-400" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>)}
                      </div>
                      <span className="text-xs font-medium text-white">4.9/5 · 1,789 reviews</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-3">
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Get professional appliance repair services for your home, office, or business
                </p>

                <div className="flex space-x-2">
                  <Link to="/create-job" className="flex-1">
                    <Button variant="default" size="default" className="w-full rounded-md font-semibold text-sm py-1.5">Post a Job</Button>
                  </Link>
                  <Link to="/professionals/appliance-repair" className="flex-1">
                    <Button variant="outline" size="default" className="w-full rounded-md font-semibold text-sm py-1.5 text-gray-800 dark:text-white border-gray-400">
                      Browse Technicians
                    </Button>
                  </Link>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-2 mb-3 flex items-center justify-between">
              <div className="flex items-center">
                <Shield className="h-5 w-5 text-green-500 mr-1" />
                <div className="text-left">
                  <span className="block text-xs text-gray-500 dark:text-gray-400">
                    Verified
                  </span>
                  <span className="font-bold text-xs">
                    Technicians
                  </span>
                </div>
              </div>
              <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-blue-500 mr-1" />
                <div className="text-left">
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Response</span>
                  <span className="font-bold text-xs">&#60; 1hr</span>
                </div>
              </div>
              <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
              <div className="flex items-center">
                <Award className="h-5 w-5 text-purple-500 mr-1" />
                <div className="text-left">
                  <span className="block text-xs text-gray-500 dark:text-gray-400">
                    Warranty
                  </span>
                  <span className="font-bold text-xs">90-Day</span>
                </div>
              </div>
            </div>
          </div> : <div className="flex flex-col lg:flex-row gap-12 items-center">
            <div className="w-full lg:w-1/2 space-y-8">
              <div className="space-y-3">
                <span className="inline-block bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-hover px-4 py-1.5 rounded-full font-medium text-sm">Professional Appliance Repair</span>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark-heading leading-tight">
                  Expert Repairs,
                  <br className="hidden md:inline" />
                  <span className="text-primary mt-2 block dark:text-primary-hover dark:dark-mode-text-shadow">
                    For All Your Appliances.
                  </span>
                </h1>
                <div className="text-xl font-semibold text-gray-700 dark-subheading">
                  <div className="flex flex-col space-y-3 text-left">
                    <div className="flex items-center gap-2">
                      <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-gray-900 dark:text-white">Home, Office & Commercial Repair</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-gray-900 dark:text-white">Certified Technicians</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-gray-900 dark:text-white">90-Day Warranty</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-gray-900 dark:text-white">All Major Brands Serviced</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Link to="/create-job" className="w-full sm:w-auto">
                  <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                    Post a Project
                    <ArrowRight size={20} className="ml-2" />
                  </Button>
                </Link>
                <Link to="/professionals/appliance-repair" className="w-full sm:w-auto">
                  <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                    Browse Technicians
                  </Button>
                </Link>
              </div>

              <div className="pt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                  <Check className="h-5 w-5 text-green-500" />
                  <span>Same-Day Service</span>
                </div>
                <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                  <Check className="h-5 w-5 text-green-500" />
                  <span>Certified Technicians</span>
                </div>
                <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                  <Check className="h-5 w-5 text-green-500" />
                  <span>90-Day Warranty</span>
                </div>
                <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                  <Check className="h-5 w-5 text-green-500" />
                  <span>Genuine Parts</span>
                </div>
                <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                  <Check className="h-5 w-5 text-green-500" />
                  <span>Transparent Pricing</span>
                </div>
                <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                  <Check className="h-5 w-5 text-green-500" />
                  <span>Commercial Service</span>
                </div>
              </div>
            </div>

            <div className="w-full lg:w-1/2 relative">
              <div className="absolute inset-0 bg-primary/10 dark:bg-primary/5 rounded-xl filter blur-xl opacity-70"></div>
              <div className="relative bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="grid grid-cols-2 gap-3">
                  <div className="aspect-[4/3] rounded-lg overflow-hidden">
                    <img src="/public/lovable-uploads/d9c9e9b1-41db-4f7c-9ca2-42b56eec6eb2.png" alt="Chef cooking with professional kitchen appliances" className="w-full h-full object-cover" />
                  </div>
                  <div className="aspect-[4/3] rounded-lg overflow-hidden">
                    <img src="/public/lovable-uploads/9b451807-7dd5-4476-aee1-c09ac9ae44ce.png" alt="Chef organizing commercial kitchen with refrigerator" className="w-full h-full object-cover" />
                  </div>
                  <div className="col-span-2 aspect-[16/9] rounded-lg overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1556911220-e15b29be8c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Professional technician performing appliance repair" className="w-full h-full object-cover" />
                  </div>
                </div>

                <div className="mt-6 text-center">
                  <div className="flex justify-center mb-2">
                    <div className="flex text-amber-400">
                      <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                    </div>
                    <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">4.9/5 (1,789 reviews)</span>
                  </div>
                  <h3 className="font-bold text-xl mb-1 dark:text-white">Find Top Local Repair Technicians</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Enter your ZIP code to get matched with professional appliance repair technicians for your home, office, or business</p>

                  <div className="flex shadow-lg rounded-xl overflow-hidden">
                    <div className="flex-1 flex items-center bg-gray-50 dark:bg-gray-700 p-4">
                      <input type="text" placeholder="Enter your ZIP code" className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-300 text-lg" />
                    </div>
                    <Button type="submit" className="px-8 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all">
                      <span>Search</span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>}
      </div>
    </section>;
};
export default HeroSection;
