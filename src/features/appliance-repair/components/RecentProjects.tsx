import React from 'react';

const RecentProjects: React.FC = () => {
  return (
    <section className="py-8 md:py-16 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-3 text-black dark:text-white">Recently Completed Appliance Repair Projects</h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Browse through recently completed appliance repair jobs by our certified technicians
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
            <img 
              src="https://images.unsplash.com/photo-1584949514490-73fc2b3e4355?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80" 
              alt="Refrigerator repair" 
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-3 py-1 rounded-full">Residential</span>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">2 days ago</span>
              </div>
              <h3 className="font-bold text-lg mb-2 dark:text-white">Refrigerator Compressor Replacement</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Complete diagnosis and replacement of a faulty refrigerator compressor with genuine manufacturer parts.</p>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300 font-medium">San Francisco, CA</span>
                <span className="font-bold text-lg dark:text-white">$425</span>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
            <img 
              src="https://images.unsplash.com/photo-1593784991095-a205069470b6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
              alt="Washing machine repair" 
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-3 py-1 rounded-full">Office</span>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">1 week ago</span>
              </div>
              <h3 className="font-bold text-lg mb-2 dark:text-white">Office Coffee Machine Repair</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Full service repair of commercial coffee machine in a busy office environment with minimal disruption.</p>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300 font-medium">Oakland, CA</span>
                <span className="font-bold text-lg dark:text-white">$285</span>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
            <img 
              src="https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
              alt="Oven repair" 
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-3 py-1 rounded-full">Commercial</span>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">2 weeks ago</span>
              </div>
              <h3 className="font-bold text-lg mb-2 dark:text-white">Restaurant Oven Control System</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Complete replacement of control system in high-capacity commercial restaurant oven with temperature calibration.</p>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300 font-medium">San Jose, CA</span>
                <span className="font-bold text-lg dark:text-white">$765</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RecentProjects;
