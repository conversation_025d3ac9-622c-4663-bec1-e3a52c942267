import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';
import {BASE_API_URL} from "@/lib/constants.ts";
import useIsAuthenticated from 'react-auth-kit/hooks/useIsAuthenticated';

// Define the Role interface as specified in the requirements
interface Role {
  id: number;
  name: string;
  guard_name: string;
}

// Define the context interface
interface RoleContextType {
  roles: Role[];
  loading: boolean;
  error: string | null;
  hasRole: (roleName: string) => boolean;
  isProvider: () => boolean;
  isCustomer: () => boolean;
  getDashboardRoute: (role?: Role) => string;
  refreshRoles: () => Promise<void>;
}

// Create the context with a default value
const RoleContext = createContext<RoleContextType | undefined>(undefined);

// Define props for the provider component
interface RoleProviderProps {
  children: ReactNode;
}

// Create the provider component
export const RoleProvider: React.FC<RoleProviderProps> = ({ children }) => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const isAuthenticated = useIsAuthenticated();

  // Function to fetch roles from the API
  const fetchRoles = async (): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      // Always attempt to fetch roles from the API
      try {
        const response = await axios.get(BASE_API_URL+'/api/roles-public');
        // Update state with the fetched roles
        setRoles(response.data.data);
      } catch (apiErr) {
        console.error('Error fetching roles from API, using mock data:', apiErr);
        // Provide mock roles for development/demo purposes
        setRoles([
          { id: 1, name: 'user', guard_name: 'web' },
          { id: 2, name: 'admin', guard_name: 'web' },
          { id: 3, name: 'provider', guard_name: 'web' },
          { id: 4, name: 'Supreme Admin', guard_name: 'web' }
        ]);
      }
    } catch (err) {
      console.error('Error in fetchRoles:', err);
      setError('Failed to fetch user roles');
      // Provide mock roles as fallback
      setRoles([
        { id: 1, name: 'user', guard_name: 'web' },
        { id: 2, name: 'admin', guard_name: 'web' },
        { id: 3, name: 'provider', guard_name: 'web' },
        { id: 4, name: 'Supreme Admin', guard_name: 'web' }
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh roles
  const refreshRoles = async (): Promise<void> => {
    await fetchRoles();
  };

  // Check if user has a specific role
  const hasRole = (roleName: string): boolean => {
    return roles.some(role => role.name === roleName);
  };

  // Check if user is a provider
  const isProvider = (): boolean => {
    return hasRole('provider');
  };

  // Check if user is a customer
  const isCustomer = (): boolean => {
    return hasRole('user');
  };

  const isAdmin = (): boolean => {
    return hasRole('admin');
  }

  const isSupremeAdmin = (): boolean => {
    return hasRole('Supreme Admin');
  }


  // Get the appropriate dashboard route based on user roles
  const getDashboardRoute = (role?: Role): string => {
    if(role) {
      if (role?.name === 'user') {
        return '/customer/dashboard';
      } else if (role?.name === 'provider') {
        return '/provider/dashboard';
      } else if (role?.name === 'admin' || role.name === 'Supreme Admin') {
        return '/admin';
      }
      return '/';
    }else{
      if (isProvider()) {
        return '/provider/dashboard';
      } else if (isCustomer()) {
        return '/customer/dashboard';
      } else if(isAdmin() || isSupremeAdmin()){
        return '/admin';
      }
      else {
        return '/';
      }
    }
  };

  // Fetch roles on initial render and when authentication state changes
  useEffect(() => {
    // Always fetch fresh roles from API for security reasons
    fetchRoles();
  }, []);

  // Additional effect to refresh roles when a user is already authenticated (after page reload)
  useEffect(() => {
    if (isAuthenticated) {
      // If user is already authenticated (e.g., after page reload), refresh roles
      refreshRoles();
    }
  }, [isAuthenticated]);

  // Provide the context value
  const contextValue: RoleContextType = {
    roles,
    loading,
    error,
    hasRole,
    isProvider,
    isCustomer,
    getDashboardRoute,
    refreshRoles
  };

  return (
    <RoleContext.Provider value={contextValue}>
      {children}
    </RoleContext.Provider>
  );
};

// Custom hook to use the role context
export const useRoles = (): RoleContextType => {
  const context = useContext(RoleContext);

  if (context === undefined) {
    throw new Error('useRoles must be used within a RoleProvider');
  }

  return context;
};

export default RoleContext;
