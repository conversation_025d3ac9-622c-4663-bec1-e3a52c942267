import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from './useAuth';
import { useRoles } from '../context/RoleContext';
import { useEffect, useState } from 'react';
import { 
  loginSchema, 
  signupSchema, 
  LoginFormValues, 
  SignupFormValues 
} from '../utils/auth-schemas';

export function useAuthForms(redirectUrl?: string) {
  const { loginWithEmail, signupWithEmail, isLoading } = useAuth();
  const { roles } = useRoles();
  const [customerRoleId, setCustomerRoleId] = useState<string>('');
  const [providerRoleId, setProviderRoleId] = useState<string>('');

  // Fetch role IDs when roles are loaded
  useEffect(() => {
    if (roles?.length > 0) {
      const customerRole = roles.find(role => role.name === 'user');
      const providerRole = roles.find(role => role.name === 'provider');

      if (customerRole) {
        setCustomerRoleId(customerRole.id.toString());
      }

      if (providerRole) {
        setProviderRoleId(providerRole.id.toString());
      }
    }
  }, [roles]);

  // Login form
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  // Signup form
  const signupForm = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      accountType: 'customer',
      roleId: '',
      invitationCode: '',
      agreeTerms: false,
    },
  });

  // Update roleId when accountType changes
  useEffect(() => {
    const subscription = signupForm.watch((value, { name }) => {
      if (name === 'accountType' || !name) {
        const accountType = value.accountType;
        if (accountType === 'customer' && customerRoleId) {
          signupForm.setValue('roleId', customerRoleId);
        } else if (accountType === 'professional' && providerRoleId) {
          signupForm.setValue('roleId', providerRoleId);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [signupForm, customerRoleId, providerRoleId]);

  // Set initial roleId based on default accountType
  useEffect(() => {
    if (customerRoleId && signupForm.getValues('accountType') === 'customer') {
      signupForm.setValue('roleId', customerRoleId);
    }
    if (providerRoleId && signupForm.getValues('accountType') === 'professional') {
      signupForm.setValue('roleId', providerRoleId);
    }
  }, [customerRoleId, signupForm]);

  // Login form submission handler
  const onLoginSubmit = async (data: LoginFormValues) => {
    return await loginWithEmail(data, redirectUrl);
  };

  // Signup form submission handler
  const onSignupSubmit = async (data: SignupFormValues) => {
    return await signupWithEmail(data, redirectUrl);
  };

  return {
    loginForm,
    signupForm,
    onLoginSubmit,
    onSignupSubmit,
    isLoading,
  };
}
