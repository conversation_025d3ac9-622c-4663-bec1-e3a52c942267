
/**
 * Touch optimization CSS utilities
 */

/* Touch target size utilities */
.touch-target-sm {
  min-height: 44px;
  min-width: 44px;
}

.touch-target {
  min-height: 48px;
  min-width: 48px;
}

.touch-target-lg {
  min-height: 56px;
  min-width: 56px;
}

/* Touch feedback utilities */
.touch-feedback {
  transition: all 0.15s ease;
}

.touch-feedback:active {
  transform: scale(0.95);
}

/* Touch-friendly spacing */
.touch-spacing {
  gap: 8px;
}

.touch-spacing-comfortable {
  gap: 12px;
}

/* Responsive hover (only on non-touch devices) */
@media (hover: hover) and (pointer: fine) {
  .hover-desktop:hover {
    opacity: 0.8;
  }
  
  .hover-scale-desktop:hover {
    transform: scale(1.05);
  }
  
  .hover-bg-desktop:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

/* Touch-safe button styles */
.btn-touch {
  min-height: 48px;
  min-width: 48px;
  padding: 12px 16px;
  transition: all 0.15s ease;
}

.btn-touch:active {
  transform: scale(0.95);
}

/* Navigation touch targets */
.nav-touch {
  min-height: 48px;
  min-width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
}

.nav-touch:active {
  transform: scale(0.95);
}

/* Card touch areas */
.card-touch {
  padding: 16px;
  transition: all 0.15s ease;
}

.card-touch:active {
  transform: scale(0.98);
}

/* Form input touch optimization */
.input-touch {
  min-height: 48px;
  padding: 12px 16px;
  font-size: 16px; /* Prevents zoom on iOS */
}

/* Touch-friendly list items */
.list-item-touch {
  min-height: 48px;
  padding: 12px 16px;
  transition: all 0.15s ease;
}

.list-item-touch:active {
  background-color: rgba(0, 0, 0, 0.05);
}
