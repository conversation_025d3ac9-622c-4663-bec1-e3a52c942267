/* Hide scrollbar but keep functionality */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Custom card styles for providers */
.provider-card {
  position: relative;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  background-color: white;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.dark .provider-card {
  background-color: #1f2937;
  border-color: #374151;
}

.provider-card-featured {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
}

.featured-badge {
  display: flex;
  align-items: center;
  background-color: #3b82f6;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.provider-avatar {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 0.5rem;
  object-fit: cover;
}

.provider-stat {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.dark .provider-stat {
  color: #9ca3af;
}

.provider-action-btn {
  transition: all 0.15s ease;
}

.provider-action-btn-primary {
  background-color: #2563eb;
  color: white;
}

.provider-action-btn-primary:hover {
  background-color: #1d4ed8;
}

.provider-action-btn-secondary {
  border-color: #e2e8f0;
}

.dark .provider-action-btn-secondary {
  border-color: #374151;
}

/* Bid request button in mobile view */
@media (max-width: 640px) {
  .provider-card .provider-action-btn-secondary {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
}

/* Animation for job selection */
@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out forwards;
}

/* Compact mobile-specific styling */
@media (max-width: 640px) {
  .mobile-provider-section {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
  
  .mobile-section-title {
    margin-bottom: 0.5rem;
    font-size: 1.125rem;
  }
  
  .mobile-card-container {
    margin-bottom: 0.75rem;
  }
}

/* Mobile messaging specific styles */
.message-bubble-sent {
  border-radius: 18px 18px 4px 18px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
}

.message-bubble-received {
  border-radius: 18px 18px 18px 4px;
  background-color: #f3f4f6;
  color: #1f2937;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
}

.dark .message-bubble-received {
  background-color: #374151;
  color: #f9fafb;
}

.message-status {
  font-size: 0.65rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: rgba(255, 255, 255, 0.7);
}

.message-time {
  font-size: 0.65rem;
  color: #6b7280;
  margin-top: 2px;
}

.conversation-card {
  transition: transform 0.15s ease-in-out, border-color 0.15s ease, box-shadow 0.15s ease;
}

.conversation-card:active {
  transform: scale(0.98);
}

.conversation-card-unread {
  border-left: 3px solid #3b82f6;
}

/* Quick reply chips styling */
.quick-reply-chip {
  white-space: nowrap;
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-size: 0.75rem;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  transition: all 0.15s ease;
}

.dark .quick-reply-chip {
  background-color: #374151;
  border-color: #4b5563;
}

.quick-reply-chip:hover {
  background-color: #e5e7eb;
  border-color: #d1d5db;
}

.dark .quick-reply-chip:hover {
  background-color: #4b5563;
  border-color: #6b7280;
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Smooth slide-in animation for conversation detail */
.slide-in-right {
  animation: slide-in-right 0.3s forwards;
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-out-right {
  animation: slide-out-right 0.3s forwards;
}

@keyframes slide-out-right {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Message typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  margin: 5px 0;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background-color: #6b7280;
  border-radius: 50%;
  display: inline-block;
  margin: 0 1px;
  opacity: 0.6;
}

.typing-indicator span:nth-child(1) {
  animation: bounce 1.2s infinite 0.1s;
}

.typing-indicator span:nth-child(2) {
  animation: bounce 1.2s infinite 0.3s;
}

.typing-indicator span:nth-child(3) {
  animation: bounce 1.2s infinite 0.5s;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

/* Hamburger menu specific styles */
.mobile-header {
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 1rem;
  background-color: white;
  border-bottom: 1px solid #e2e8f0;
}

.dark .mobile-header {
  background-color: #1f2937;
  border-color: #374151;
}

.mobile-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 40;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mobile-sidebar-overlay.open {
  opacity: 1;
}

/* Mobile nav item animation */
.mobile-nav-item {
  transform: translateX(-10px);
  opacity: 0;
  animation: slideIn 0.3s forwards;
}

@keyframes slideIn {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Add staggered delay for each nav item */
.mobile-nav-item:nth-child(1) { animation-delay: 0.05s; }
.mobile-nav-item:nth-child(2) { animation-delay: 0.1s; }
.mobile-nav-item:nth-child(3) { animation-delay: 0.15s; }
.mobile-nav-item:nth-child(4) { animation-delay: 0.2s; }
.mobile-nav-item:nth-child(5) { animation-delay: 0.25s; }
.mobile-nav-item:nth-child(6) { animation-delay: 0.3s; }
.mobile-nav-item:nth-child(7) { animation-delay: 0.35s; }
.mobile-nav-item:nth-child(8) { animation-delay: 0.4s; }
.mobile-nav-item:nth-child(9) { animation-delay: 0.45s; }
.mobile-nav-item:nth-child(10) { animation-delay: 0.5s; }
