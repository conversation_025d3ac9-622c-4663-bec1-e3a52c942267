
// src/pages/RoofingProfessionals.tsx
import React, { useEffect } from 'react';
import ProfessionalsPage from "@/components/ProfessionalsPage/ProfessionalsPage.tsx";
import { useGeolocation } from '@/hooks/use-geolocation';
import { useSearchParams, useNavigate } from 'react-router-dom';

const RoofingProfessionals = () => {
  const { zipCode: detectedZipCode, loading } = useGeolocation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // If we have a detected zipCode and there's no zip in the URL, add it
  useEffect(() => {
    if (detectedZipCode && !loading && !searchParams.has('zip')) {
      console.log("Auto-applying detected zipcode to roofing search:", detectedZipCode);
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('zip', detectedZipCode);
      navigate(`?${newSearchParams.toString()}`, { replace: true });
    }
  }, [detectedZipCode, loading, searchParams, navigate]);

  return (
    <ProfessionalsPage
      serviceId="roofing"
      serviceName="Roofing Contractors"
      pageTitle="Licensed Roofing Contractors Near You | Compare Bids"
      pageDescription="From repairs to full roof replacements, JobON connects you with licensed roofing professionals near you. Get multiple bids and choose with confidence."
      gradientBackground="linear-gradient(to bottom, #fff7ed 0%, #ffffff 100%)"
    />
  );
};

export default RoofingProfessionals;
