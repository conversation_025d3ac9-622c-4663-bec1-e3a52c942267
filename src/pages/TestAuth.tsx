import React, { useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const TestAuth = () => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Log authentication state for debugging
    console.log('Authentication state:', {
      isAuthenticated,
      user
    });
  }, [isAuthenticated, user]);

  return (
    <Layout>
      <div className="container mx-auto py-20">
        <h1 className="text-2xl font-bold mb-4">Authentication Test Page</h1>

        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-md mb-4">
          <h2 className="text-xl font-semibold mb-2">Authentication Status:</h2>
          <p>Is Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
          {user && (
            <div className="mt-2">
              <p>User: {user.name}</p>
              <p>Email: {user.email}</p>
              <p>Role: {user.role?.name}</p>
            </div>
          )}
        </div>

        <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
          <Button onClick={() => navigate('/')}>Go to Home</Button>
          <Button onClick={() => navigate('/find-pros')}>Go to Find Pros</Button>
          <Button onClick={() => navigate('/auth')}>Go to Auth Page</Button>
        </div>
      </div>
    </Layout>
  );
};

export default TestAuth;
