
// src/pages/CleaningProfessionals.tsx
import React, { useEffect } from 'react';
import ProfessionalsPage from "@/components/ProfessionalsPage/ProfessionalsPage.tsx";
import { useGeolocation } from '@/hooks/use-geolocation';
import { useSearchParams, useNavigate } from 'react-router-dom';

const CleaningProfessionals = () => {
  const { zipCode: detectedZipCode, loading } = useGeolocation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // If we have a detected zipCode and there's no zip in the URL, add it
  useEffect(() => {
    if (detectedZipCode && !loading && !searchParams.has('zip')) {
      console.log("Auto-applying detected zipcode to cleaning search:", detectedZipCode);
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('zip', detectedZipCode);
      navigate(`?${newSearchParams.toString()}`, { replace: true });
    }
  }, [detectedZipCode, loading, searchParams, navigate]);

  return (
      <ProfessionalsPage
          serviceId="cleaning"
          serviceName="Cleaning Professionals"
          pageTitle="Top-Rated Cleaning Services Near You | Book Professional Cleaners"
          pageDescription="Find reliable and thorough cleaning professionals for your home or business. Compare services, read reviews, and hire the best cleaners in your area."
          gradientBackground="linear-gradient(to bottom, #ecfdf5 0%, #ffffff 100%)"
      />
  );
};

export default CleaningProfessionals;
