
import React, {useEffect, useState} from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {<PERSON>, CardContent, CardHeader, CardTitle} from "@/components/ui/card.tsx";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table.tsx";
import {Badge} from "@/components/ui/badge.tsx";
import {useAuth} from "@/features/auth/hooks/useAuth.ts";
import {getMockProviders} from "@/data/mockProviders.ts";
import {Pagination} from "@/components/ui/pagination.tsx";
import BookingDetailsDialog from "@/components/BookingDetailsDialog";
import {Calendar, Filter, Search, Send, X} from "lucide-react";
import {apiService} from "@/services/api.ts";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { BookingItem } from "@/utils/typeHelpers";
import { Input } from "@/components/ui/input";

interface ResponseType {
    success: boolean;
    message?: string;
    data: Array<{
        uuid: string;
        jobId: string;
        service: {
            tasks: string[];
            category: string;
        };
        contact: {
            fullName: string;
            avatar?: string;
            initials?: string;
        };
        status: string;
        schedule: {
            date: string;
        };
        location: {
            zipCode: string;
        };
        description: string;
        [key: string]: unknown;
    }>;
    pagination: {
        current_page: number;
        per_page: number;
        total: number;
        last_page: number;
    };
}

export interface DataProviderType {
    businessId: string;
    name: string;
    category: string;
    location: string;
    address: string;
    phone: string;
    website: string;
    email: string;
    hours: {
        monday: string;
        tuesday: string;
        wednesday: string;
        thursday: string;
        friday: string;
        saturday: string;
        sunday: string;
    };
    photos: string[];
    services: never[];
    reviews: {
        text: string; rating: string; author: string; date: string }[];
    createdAt: string;
    updatedAt: string;
}

// Local DataType interface to match component needs
interface LocalDataType {
    businessId: string;
    name: string;
    category: string;
    location: string;
    address: string;
    phone: string;
    website: string;
    email: string; // Always string, not nullable
    hours: {
        monday: string;
        tuesday: string;
        wednesday: string;
        thursday: string;
        friday: string;
        saturday: string;
        sunday: string;
    };
    photos: string[];
    services: never[];
    reviews: {
        text: string; rating: string; author: string; date: string }[];
    createdAt: string;
    updatedAt: string;
}

const Bookings = () => {
    const { token } = useAuth();
    const [dataTable, setDataTable] = useState<BookingItem[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current_page: 1,
        per_page: 15,
        total: 0,
        last_page: 1
    });
    const [dataProviders, setDataProviders] = useState<DataProviderType[]>([]);
    const [selectedBooking, setSelectedBooking] = useState<BookingItem | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [isLoadingProviders, setIsLoadingProviders] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const { isMobile } = useUIHelpers();

    useEffect(() => {
        const url = new URL(window.location.href);
        const pageParam = url.searchParams.get('page');
        if (pageParam) {
            const page = parseInt(pageParam, 10);
            if (!isNaN(page) && page > 0) {
                setCurrentPage(page);
            }
        }
    }, []);

    useEffect(() => {
        async function getData() {
            setIsLoading(true);
            try {
                const endpoint = `/api/job-bookings?page=${currentPage}`;
                const response = await apiService<ResponseType>(endpoint, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization' : token || ''
                    },
                });

                if (!response.data?.success) {
                    throw new Error(`Response status: ${response.status}`);
                }

                const json = response.data;
                if (!json) {
                    throw new Error('No data received');
                }

                // Store pagination metadata
                setPagination({
                    current_page: json.pagination?.current_page || 1,
                    per_page: json.pagination?.per_page || 15,
                    total: json.pagination?.total || 0,
                    last_page: json.pagination?.last_page || 1
                });

                const data = json.data || [];
                const dataConvert: BookingItem[] = data.map((item) => {
                    return {
                        id: item.jobId,
                        title: item.service.tasks,
                        client: item.contact,
                        provider: {
                            name: "Mike's Plumbing",
                            avatar: "/placeholder.svg",
                            initials: "MP",
                        },
                        status: item.status,
                        date: item.schedule.date,
                        amount: "$1,200",
                        service: item.service,
                        location: item.location,
                        description: item.description,
                    }
                })

                setDataTable(dataConvert)
            } catch (error) {
                console.error((error as Error).message || 'Unknown error');
            } finally {
                setIsLoading(false);
            }
        }
        getData()
    }, [currentPage, token])

    const fetchData = async (service:string, zip: string) => {
        setIsLoadingProviders(true)
        const options = {
            page: 1,
            zip_code: zip
        }
        try {
            const res = await getMockProviders(service, options)
            // Convert external DataType to local DataProviderType ensuring email is string
            const convertedData: DataProviderType[] = res.data.map((item: any) => ({
                ...item,
                email: item.email || '' // Convert null to empty string
            }));
            setDataProviders(convertedData)
            setIsLoadingProviders(false)
        }
        catch (error) {
            console.log(error)
            setDataProviders([])
            setIsLoadingProviders(false)
        }
    }

    // Handler for page changes
    const handlePageChange = (page: number) => {
        // Update URL parameter without reload
        const url = new URL(window.location.href);
        url.searchParams.set('page', page.toString());
        window.history.pushState({}, '', url);

        // Update state
        setCurrentPage(page);
        window.scrollTo(0, 0);
    };

    // Filter function for search
    const filteredData = dataTable.filter(project => {
        if (!searchQuery) return true;
        const searchLower = searchQuery.toLowerCase();

        // Search in title
        if (Array.isArray(project?.title) && project.title.some((item: string) => item.toLowerCase().includes(searchLower))) {
            return true;
        } else if (typeof project?.title === 'string' && project.title.toLowerCase().includes(searchLower)) {
            return true;
        }

        // Search in client name
        if (project.client?.fullName && project.client.fullName.toLowerCase().includes(searchLower)) {
            return true;
        }

        // Search in ID
        if (project.id.toLowerCase().includes(searchLower)) {
            return true;
        }

        // Search in service
        if (project.service?.category && project.service.category.toLowerCase().includes(searchLower)) {
            return true;
        }

        return false;
    });

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "in_progress":
                return <Badge className="bg-blue-500 hover:bg-blue-600 text-white font-medium">In Progress</Badge>;
            case "completed":
                return <Badge className="bg-green-500 hover:bg-green-600 text-white font-medium">Completed</Badge>;
            case "scheduled":
                return <Badge className="bg-amber-500 hover:bg-amber-600 text-white font-medium">Scheduled</Badge>;
            case "awaiting_payment":
                return <Badge className="bg-purple-500 hover:bg-purple-600 text-white font-medium">Awaiting Payment</Badge>;
            default:
                return <Badge className="bg-gray-500 hover:bg-gray-600 text-white font-medium">{status}</Badge>;
        }
    };

    // Function to get gradient class based on service
    const getGradientClass = (service: string) => {
        if (!service) return "bg-gradient-to-r from-gray-200 to-gray-300";

        const serviceCategory = service.toLowerCase();
        if (serviceCategory.includes('plumb')) {
            return "bg-gradient-to-r from-blue-100 to-blue-200 border-l-4 border-blue-500";
        } else if (serviceCategory.includes('electric')) {
            return "bg-gradient-to-r from-yellow-100 to-yellow-200 border-l-4 border-yellow-500";
        } else if (serviceCategory.includes('clean')) {
            return "bg-gradient-to-r from-green-100 to-green-200 border-l-4 border-green-500";
        } else if (serviceCategory.includes('repair')) {
            return "bg-gradient-to-r from-orange-100 to-orange-200 border-l-4 border-orange-500";
        } else {
            return "bg-gradient-to-r from-purple-100 to-purple-200 border-l-4 border-purple-500";
        }
    };

    // Render mobile booking card
    const BookingCard = ({ booking }: { booking: BookingItem }) => {
        const serviceGradient = getGradientClass(booking?.service?.category);

        return (
            <div className={`mb-4 rounded-lg shadow-sm overflow-hidden ${serviceGradient}`}>
                <div className="p-4">
                    <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                            <h3 className="font-medium text-gray-900 truncate">
                                {Array.isArray(booking?.title)
                                    ? booking.title.slice(0, 2).map((item: string, i: number) => (
                                        <span key={i} className="mr-1">
                                            {i > 0 && ", "}
                                            {item}
                                        </span>
                                      ))
                                    : booking?.title || "Unnamed Booking"}
                                {Array.isArray(booking?.title) && booking.title.length > 2 && ", ..."}
                            </h3>
                            <p className="text-xs text-gray-600 mt-0.5 truncate">ID: {booking.id.substring(0, 8)}...</p>
                        </div>
                        <div>
                            {getStatusBadge(booking.status)}
                        </div>
                    </div>

                    <div className="flex items-center gap-2 mb-3 bg-white/50 p-2 rounded-md">
                        <Avatar className="h-8 w-8 border border-gray-200">
                            <AvatarImage src={booking.client.avatar} />
                            <AvatarFallback>{booking.client.initials || booking.client.fullName?.charAt(0) || "C"}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{booking.client.fullName}</p>
                            <div className="flex items-center text-xs text-gray-500">
                                <Calendar className="h-3 w-3 mr-1" />
                                <span>{booking.date}</span>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-between items-center">
                        <div>
                            <span className="text-xs bg-white/70 px-2 py-1 rounded-full font-medium">
                                {booking.service.category}
                            </span>
                        </div>
                        <Button
                            size="sm"
                            className="bg-indigo-600 hover:bg-indigo-700 text-white flex items-center gap-1"
                            onClick={async (e: React.MouseEvent<HTMLButtonElement>) => {
                                e.stopPropagation();
                                setSelectedBooking(booking);
                                setIsDialogOpen(true);
                                await fetchData(booking.service.category, booking.location.zipCode);
                            }}
                        >
                            <Send className="w-3 h-3" />
                            <span>Send</span>
                        </Button>
                    </div>
                </div>
            </div>
        );
    };

    return <>
        <Card className="shadow-md">
            <CardHeader className="pb-3">
                <CardTitle className="text-xl md:text-2xl flex justify-between items-center">
                    <span>Manage Job Booking</span>
                    {isMobile && (
                        <Button variant="outline" size="icon" className="h-8 w-8">
                            <Filter className="h-4 w-4" />
                        </Button>
                    )}
                </CardTitle>

                {isMobile && (
                    <div className="relative mt-2">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                            className="pl-9 pr-8 py-2 rounded-full bg-gray-50 border-gray-200"
                            placeholder="Search bookings..."
                            value={searchQuery}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                        />
                        {searchQuery && (
                            <Button
                                variant="ghost"
                                size="icon"
                                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7"
                                onClick={() => setSearchQuery('')}
                            >
                                <X className="h-3 w-3" />
                            </Button>
                        )}
                    </div>
                )}
            </CardHeader>
            <CardContent>
                {isLoading ? (
                    <div className="flex justify-center items-center py-8">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                    </div>
                ) : isMobile ? (
                    // Mobile view with cards
                    <div className="space-y-1">
                        {filteredData.length === 0 ? (
                            <div className="text-center py-10 bg-gray-50 rounded-lg">
                                <p className="text-gray-500">No bookings found</p>
                                {searchQuery && (
                                    <Button
                                        variant="link"
                                        onClick={() => setSearchQuery('')}
                                        className="mt-2"
                                    >
                                        Clear search
                                    </Button>
                                )}
                            </div>
                        ) : (
                            filteredData.map((booking) => (
                                <BookingCard key={booking.id} booking={booking} />
                            ))
                        )}
                    </div>
                ) : (
                    // Desktop view with table
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Project</TableHead>
                                <TableHead>Client</TableHead>
                                <TableHead>Service</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Date</TableHead>
                                <TableHead>Amount</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredData.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={7} className="text-center py-8">
                                        No bookings found
                                    </TableCell>
                                </TableRow>
                            ) : (
                                filteredData.map((project) => {
                                    return <TableRow key={project.id}>
                                        <TableCell>
                                            <div>
                                                <div className="font-medium">
                                                    {Array.isArray(project?.title)
                                                        ? project.title.map((item: string, index: number) => (
                                                            <span key={"title"+ index}>
                                                                {index > 0 && ", "}
                                                                <span>{item}</span>
                                                            </span>
                                                        ))
                                                        : project?.title || ""
                                                    }
                                                </div>
                                                <div className="text-sm text-muted-foreground">{project.id}</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <Avatar className="h-8 w-8">
                                                    <AvatarImage src={project.client.avatar} />
                                                    <AvatarFallback>{project.client.initials}</AvatarFallback>
                                                </Avatar>
                                                <div>{project.client.fullName}</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <div>{project.service.category}</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>{getStatusBadge(project.status)}</TableCell>
                                        <TableCell>{project.date}</TableCell>
                                        <TableCell>{project.amount}</TableCell>
                                        <TableCell className="flex justify-end" onClick={(e) => e.stopPropagation()}>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                className="flex items-center gap-2"
                                                onClick={async (e: React.MouseEvent<HTMLButtonElement>) => {
                                                    e.stopPropagation();
                                                    setSelectedBooking(project);
                                                    setIsDialogOpen(true);
                                                    await fetchData(project.service.category, project.location.zipCode)
                                                }}
                                            >
                                                <Send className="w-4 h-4" />
                                                Send to Providers
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                })
                            )}
                        </TableBody>
                    </Table>
                )}
            </CardContent>
        </Card>
        {pagination.total > 0 && !isLoading && (
            <div className="mt-4">
                <Pagination
                    totalItems={pagination.total}
                    itemsPerPage={pagination.per_page}
                    currentPage={pagination.current_page}
                    onPageChange={handlePageChange}
                />
            </div>
        )}

        {/* Booking Details Dialog */}
        <BookingDetailsDialog
            booking={selectedBooking ? {
                ...selectedBooking,
                // Ensure title is always string[] for the dialog
                title: typeof selectedBooking.title === 'string' ? [selectedBooking.title] : (selectedBooking.title || []),
                client: {
                    fullName: selectedBooking.client.fullName,
                    email: '',
                    phone: '',
                },
                service: {
                    ...selectedBooking.service,
                    tasks: selectedBooking.service.tasks || [],
                    customTask: null,
                },
                location: {
                    ...selectedBooking.location,
                    address: '',
                    city: '',
                    state: '',
                }
            } : null}
            isOpen={isDialogOpen}
            onClose={() => setIsDialogOpen(false)}
            provider={dataProviders}
            isLoadingProviders={isLoadingProviders}
        />
    </>
};

export default Bookings;
