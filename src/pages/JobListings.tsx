import React, { useState } from 'react';
import { Layout } from '@/components/Layout';
import JobListing from '@/components/JobListing';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Plus, Filter, MapPin } from 'lucide-react';
import { Link } from 'react-router-dom';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis
} from "@/components/ui/pagination";
import { FilterSidebar } from '@/components/FilterSidebar';

// Extended mock data with 50 jobs
const mockJobs = [
  {
    id: "1",
    title: "Kitchen Renovation Project",
    description: "Need a complete kitchen renovation including new cabinets, countertops, and appliance installation. Looking for experienced contractors who can complete the project within 3 weeks.",
    budget: 15000,
    location: "Austin, TX",
    dueDate: "2 weeks",
    category: "Home Improvement",
    status: "open" as const,
    images: [
      "https://images.unsplash.com/photo-1556911220-bff31c812dba",
      "https://images.unsplash.com/photo-1556912167-f556f1f39faa"
    ],
  },
  {
    id: "2",
    title: "Landscaping and Garden Design",
    description: "Looking for a professional landscaper to redesign our backyard. Project includes garden beds, pathway installation, and irrigation system setup.",
    budget: 8000,
    location: "Miami, FL",
    dueDate: "1 month",
    category: "Landscaping",
    status: "open" as const,
    images: [
      "https://images.unsplash.com/photo-1558904541-efa843a96f01",
    ],
    videoUrl: "https://example.com/video.mp4"
  },
  {
    id: "3",
    title: "Emergency Plumbing Repair",
    description: "Need an experienced plumber for a burst pipe repair. Immediate response required. Water damage control needed.",
    location: "Denver, CO",
    dueDate: "Today",
    category: "Plumbing",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1585704032915-c3400ca199e7"]
  },
  {
    id: "4",
    title: "Electrical Panel Upgrade",
    description: "200 amp service upgrade needed for home renovation. Looking for licensed electrician with experience in residential work.",
    location: "Seattle, WA",
    dueDate: "3 days",
    category: "Electrical",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1621905252507-b35492cc74b4"]
  },
  {
    id: "5",
    title: "HVAC System Installation",
    description: "Looking for a certified HVAC technician to install a new energy-efficient system in our home.",
    location: "Phoenix, AZ",
    dueDate: "2 weeks",
    category: "HVAC",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1571905051396-e55a5915310a"]
  },
  {
    id: "6",
    title: "Appliance Repair - Refrigerator",
    description: "Our refrigerator is not cooling properly. Need a technician to diagnose and repair the issue.",
    location: "Las Vegas, NV",
    dueDate: "1 week",
    category: "Appliance Repair",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1518442388711-b99540e299c0"]
  },
  {
    id: "7",
    title: "Pest Control Services",
    description: "Need a pest control service to treat our home for ants and spiders. Looking for eco-friendly options.",
    location: "San Antonio, TX",
    dueDate: "10 days",
    category: "Pest Control",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1576566588028-4147f3842f27"]
  },
  {
    id: "8",
    title: "Roofing Inspection and Repair",
    description: "Looking for a roofing contractor to inspect our roof for leaks and provide necessary repairs.",
    location: "Oklahoma City, OK",
    dueDate: "2 weeks",
    category: "Roofing",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1561899458-dca89c9c7740"]
  },
  {
    id: "9",
    title: "Solar Panel Installation",
    description: "Interested in installing solar panels on our roof to reduce energy costs. Need a consultation and installation service.",
    location: "Albuquerque, NM",
    dueDate: "1 month",
    category: "Solar",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1504949145653-4c9437aa545d"]
  },
  {
    id: "10",
    title: "Handyman Services - General Repairs",
    description: "Need a handyman to help with various small repairs around the house, including fixing doors and patching walls.",
    location: "New Orleans, LA",
    dueDate: "1 week",
    category: "Handyman",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1559548331-a98598249992"]
  },
  {
    id: "11",
    title: "Custom Cabinetry Installation",
    description: "Looking for a skilled carpenter to design and install custom cabinets in our living room.",
    location: "Portland, OR",
    dueDate: "6 weeks",
    category: "Home Improvement",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1586023492125-27b2c025f10e"]
  },
  {
    id: "12",
    title: "Sprinkler System Repair",
    description: "Our sprinkler system is malfunctioning and needs repair. Looking for a technician with experience in irrigation systems.",
    location: "Sacramento, CA",
    dueDate: "5 days",
    category: "Landscaping",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1563019770-1ca81b32c594"]
  },
  {
    id: "13",
    title: "Drain Cleaning Service",
    description: "Clogged drain in the bathroom sink. Need a plumber to clear the blockage.",
    location: "Salt Lake City, UT",
    dueDate: "Tomorrow",
    category: "Plumbing",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1617175868234-9677d585d70a"]
  },
  {
    id: "14",
    title: "Lighting Fixture Installation",
    description: "Need an electrician to install new lighting fixtures in our dining room.",
    location: "Boise, ID",
    dueDate: "4 days",
    category: "Electrical",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1551523859-69431a0d7291"]
  },
  {
    id: "15",
    title: "Air Duct Cleaning",
    description: "Looking for a service to clean our air ducts to improve air quality in our home.",
    location: "Cheyenne, WY",
    dueDate: "1 week",
    category: "HVAC",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1571905051396-e55a5915310a"]
  },
  {
    id: "16",
    title: "Washer Repair",
    description: "Our washing machine is not spinning. Need an appliance repair technician to fix it.",
    location: "Helena, MT",
    dueDate: "3 days",
    category: "Appliance Repair",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1518442388711-b99540e299c0"]
  },
  {
    id: "17",
    title: "Termite Inspection",
    description: "Need a pest control service to inspect our property for termites.",
    location: "Bismarck, ND",
    dueDate: "10 days",
    category: "Pest Control",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1576566588028-4147f3842f27"]
  },
  {
    id: "18",
    title: "Roof Leak Repair",
    description: "Our roof is leaking after the recent storm. Need immediate repair services.",
    location: "Pierre, SD",
    dueDate: "ASAP",
    category: "Roofing",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1561899458-dca89c9c7740"]
  },
  {
    id: "19",
    title: "Solar Panel Maintenance",
    description: "Need a technician to perform routine maintenance on our solar panels.",
    location: "Lincoln, NE",
    dueDate: "2 weeks",
    category: "Solar",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1504949145653-4c9437aa545d"]
  },
  {
    id: "20",
    title: "Furniture Assembly",
    description: "Need a handyman to assemble new furniture for our home office.",
    location: "Topeka, KS",
    dueDate: "1 week",
    category: "Handyman",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1559548331-a98598249992"]
  },
  {
    id: "21",
    title: "Basement Remodeling",
    description: "Looking for a contractor to remodel our basement into a functional living space.",
    location: "Minneapolis, MN",
    dueDate: "8 weeks",
    category: "Home Improvement",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1586023492125-27b2c025f10e"]
  },
  {
    id: "22",
    title: "Lawn Mowing Service",
    description: "Need a reliable lawn mowing service for our residential property.",
    location: "Des Moines, IA",
    dueDate: "Weekly",
    category: "Landscaping",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1563019770-1ca81b32c594"]
  },
  {
    id: "23",
    title: "Toilet Repair",
    description: "Our toilet is constantly running. Need a plumber to fix the issue.",
    location: "Madison, WI",
    dueDate: "2 days",
    category: "Plumbing",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1617175868234-9677d585d70a"]
  },
  {
    id: "24",
    title: "Outlet Installation",
    description: "Need an electrician to install additional outlets in our living room.",
    location: "Springfield, IL",
    dueDate: "3 days",
    category: "Electrical",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1551523859-69431a0d7291"]
  },
  {
    id: "25",
    title: "Furnace Repair",
    description: "Our furnace is not working. Need an HVAC technician to diagnose and repair it.",
    location: "Indianapolis, IN",
    dueDate: "2 days",
    category: "HVAC",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1571905051396-e55a5915310a"]
  },
  {
    id: "26",
    title: "Dishwasher Repair",
    description: "Our dishwasher is leaking. Need an appliance repair technician to fix the leak.",
    location: "Columbus, OH",
    dueDate: "4 days",
    category: "Appliance Repair",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1518442388711-b99540e299c0"]
  },
  {
    id: "27",
    title: "Ant Control",
    description: "Need a pest control service to eliminate ants in our kitchen.",
    location: "Charleston, WV",
    dueDate: "5 days",
    category: "Pest Control",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1576566588028-4147f3842f27"]
  },
  {
    id: "28",
    title: "Gutter Cleaning",
    description: "Need a service to clean our gutters and remove debris.",
    location: "Richmond, VA",
    dueDate: "1 week",
    category: "Roofing",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1561899458-dca89c9c7740"]
  },
  {
    id: "29",
    title: "Solar Panel Cleaning",
    description: "Need a service to clean our solar panels to maintain their efficiency.",
    location: "Raleigh, NC",
    dueDate: "2 weeks",
    category: "Solar",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1504949145653-4c9437aa545d"]
  },
  {
    id: "30",
    title: "Drywall Repair",
    description: "Need a handyman to repair drywall damage in our hallway.",
    location: "Columbia, SC",
    dueDate: "3 days",
    category: "Handyman",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1559548331-a98598249992"]
  },
  {
    id: "31",
    title: "Bathroom Renovation",
    description: "Looking for a contractor to renovate our outdated bathroom.",
    location: "Atlanta, GA",
    dueDate: "6 weeks",
    category: "Home Improvement",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1586023492125-27b2c025f10e"]
  },
  {
    id: "32",
    title: "Tree Trimming",
    description: "Need a service to trim overgrown trees in our yard.",
    location: "Tallahassee, FL",
    dueDate: "1 week",
    category: "Landscaping",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1563019770-1ca81b32c594"]
  },
  {
    id: "33",
    title: "Water Heater Installation",
    description: "Need a plumber to install a new water heater in our home.",
    location: "Montgomery, AL",
    dueDate: "2 days",
    category: "Plumbing",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1617175868234-9677d585d70a"]
  },
  {
    id: "34",
    title: "Wiring Upgrade",
    description: "Need an electrician to upgrade the wiring in our older home.",
    location: "Jackson, MS",
    dueDate: "4 days",
    category: "Electrical",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1551523859-69431a0d7291"]
  },
  {
    id: "35",
    title: "AC Repair",
    description: "Our air conditioner is not cooling. Need an HVAC technician to repair it.",
    location: "Little Rock, AR",
    dueDate: "1 day",
    category: "HVAC",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1571905051396-e55a5915310a"]
  },
  {
    id: "36",
    title: "Oven Repair",
    description: "Our oven is not heating properly. Need an appliance repair technician to fix it.",
    location: "Oklahoma City, OK",
    dueDate: "3 days",
    category: "Appliance Repair",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1518442388711-b99540e299c0"]
  },
  {
    id: "37",
    title: "Roach Control",
    description: "Need a pest control service to eliminate roaches in our apartment.",
    location: "Austin, TX",
    dueDate: "6 days",
    category: "Pest Control",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1576566588028-4147f3842f27"]
  },
  {
    id: "38",
    title: "Shingle Replacement",
    description: "Need a roofing contractor to replace damaged shingles on our roof.",
    location: "Denver, CO",
    dueDate: "1 week",
    category: "Roofing",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1561899458-dca89c9c7740"]
  },
  {
    id: "39",
    title: "Solar Panel Removal",
    description: "Need a service to remove old solar panels from our roof.",
    location: "Phoenix, AZ",
    dueDate: "2 weeks",
    category: "Solar",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1504949145653-4c9437aa545d"]
  },
  {
    id: "40",
    title: "Hanging Pictures",
    description: "Need a handyman to hang pictures and mirrors in our home.",
    location: "Los Angeles, CA",
    dueDate: "2 days",
    category: "Handyman",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1559548331-a98598249992"]
  },
  {
    id: "41",
    title: "Garage Conversion",
    description: "Looking for a contractor to convert our garage into a home office.",
    location: "San Francisco, CA",
    dueDate: "7 weeks",
    category: "Home Improvement",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1586023492125-27b2c025f10e"]
  },
  {
    id: "42",
    title: "Weed Control",
    description: "Need a service to control weeds in our garden.",
    location: "San Diego, CA",
    dueDate: "1 week",
    category: "Landscaping",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1563019770-1ca81b32c594"]
  },
  {
    id: "43",
    title: "Pipe Leak Repair",
    description: "Need a plumber to repair a leaking pipe in our basement.",
    location: "Seattle, WA",
    dueDate: "1 day",
    category: "Plumbing",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1617175868234-9677d585d70a"]
  },
  {
    id: "44",
    title: "Circuit Breaker Replacement",
    description: "Need an electrician to replace a faulty circuit breaker.",
    location: "Portland, OR",
    dueDate: "2 days",
    category: "Electrical",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1551523859-69431a0d7291"]
  },
  {
    id: "45",
    title: "Heat Pump Repair",
    description: "Our heat pump is not working. Need an HVAC technician to repair it.",
    location: "Las Vegas, NV",
    dueDate: "2 days",
    category: "HVAC",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1571905051396-e55a5915310a"]
  },
  {
    id: "46",
    title: "Microwave Repair",
    description: "Our microwave is not heating. Need an appliance repair technician to fix it.",
    location: "San Antonio, TX",
    dueDate: "3 days",
    category: "Appliance Repair",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1518442388711-b99540e299c0"]
  },
  {
    id: "47",
    title: "Spider Control",
    description: "Need a pest control service to eliminate spiders in our home.",
    location: "Albuquerque, NM",
    dueDate: "4 days",
    category: "Pest Control",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1576566588028-4147f3842f27"]
  },
  {
    id: "48",
    title: "Skylight Repair",
    description: "Need a roofing contractor to repair a leaking skylight.",
    location: "New Orleans, LA",
    dueDate: "5 days",
    category: "Roofing",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1561899458-dca89c9c7740"]
  },
  {
    id: "49",
    title: "Solar Panel Cleaning",
    description: "Need a service to clean our solar panels to maintain their efficiency.",
    location: "Salt Lake City, UT",
    dueDate: "2 weeks",
    category: "Solar",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1504949145653-4c9437aa545d"]
  },
  {
    id: "50",
    title: "Shelf Installation",
    description: "Need a handyman to install shelves in our pantry.",
    location: "Boise, ID",
    dueDate: "3 days",
    category: "Handyman",
    status: "open" as const,
    images: ["https://images.unsplash.com/photo-1559548331-a98598249992"]
  }
];

const JobListings = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const itemsPerPage = 8;

  // Calculate pagination
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentJobs = mockJobs.slice(startIndex, endIndex);
  const totalPages = Math.ceil(mockJobs.length / itemsPerPage);

  const handleFilterChange = (filters: any) => {
    console.log('Filters changed:', filters);
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          {/* Header section */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-3xl font-bold text-gray-900">Available Projects</h1>
              <Button asChild>
                <Link to="/create-job">
                  <Plus className="mr-2 h-4 w-4" />
                  Post a Project
                </Link>
              </Button>
            </div>
            
            {/* Search and filter bar */}
            <div className="flex gap-4 items-center">
              <div className="relative flex-1 flex gap-4">
                <div className="relative flex-1">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Enter ZIP code..."
                    value={zipCode}
                    onChange={(e) => setZipCode(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button 
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Service Filters
              </Button>
            </div>
          </div>

          <div className="flex gap-6">
            {/* Filters sidebar */}
            {showFilters && (
              <div className="w-64 flex-shrink-0">
                <FilterSidebar 
                  onFilterChange={handleFilterChange}
                  serviceType="all"
                />
              </div>
            )}

            {/* Job listings grid */}
            <div className="flex-1">
              <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
                {currentJobs.map((job) => (
                  <JobListing key={job.id} {...job} />
                ))}
              </div>

              {/* Pagination */}
              <div className="mt-8">
                <Pagination
                  totalItems={mockJobs.length}
                  itemsPerPage={itemsPerPage}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default JobListings;
