import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft, Search, Filter, MapPin, ChevronDown, Star, CheckCircle, Clock } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SearchBar } from "@/components/SearchBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { MobileProviderCard } from '@/components/mobile/MobileProviderCard';
import { useGeolocation } from '@/hooks/use-geolocation';
import { InputWithIcon } from '@/components/ui/input-with-icon';
import { Job } from '@/types/jobs';
import { DataType } from '@/types/common';
import { MobileProviderSkeleton } from '@/components/mobile/MobileProviderSkeleton';

// Mock data for providers and jobs
const mockProviders: DataType[] = [
  {
    businessId: "pro1",
    name: "<PERSON>'s Plumbing",
    category: "Plumbing",
    location: "Phoenix, AZ",
    address: "123 Main St, Phoenix, AZ",
    phone: "************",
    website: "www.mikesplumbing.com",
    email: "<EMAIL>",
    hours: {
      monday: "8am-5pm",
      tuesday: "8am-5pm",
      wednesday: "8am-5pm",
      thursday: "8am-5pm", 
      friday: "8am-5pm",
      saturday: "9am-2pm",
      sunday: "Closed"
    },
    photos: ["https://images.unsplash.com/photo-1581092160607-ee22621dd758?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"],
    services: [],
    reviews: [{ rating: "5", text: "Great service!", author: "John D.", date: "2025-01-15" }, { rating: "4", text: "Good work", author: "Sarah M.", date: "2025-02-10" }, { rating: "5", text: "Excellent!", author: "Mike P.", date: "2025-03-05" }],
    createdAt: "2024-12-01",
    updatedAt: "2025-04-01",
    badges: ["Verified", "Insured"],
    rating: 4.9,
    reviewCount: 38,
    responseTime: "< 1 hour",
    specialty: "Plumbing",
    fullAddress: "123 Main St, Phoenix, AZ 85001",
    available: true,
    isFeatured: true
  },
  {
    businessId: "pro2",
    name: "Smith Electrical",
    category: "Electrical",
    location: "Phoenix, AZ",
    address: "456 Oak Ave, Phoenix, AZ",
    phone: "************",
    website: "www.smithelectrical.com",
    email: "<EMAIL>",
    hours: {
      monday: "7am-4pm",
      tuesday: "7am-4pm",
      wednesday: "7am-4pm",
      thursday: "7am-4pm", 
      friday: "7am-4pm",
      saturday: "10am-2pm",
      sunday: "Closed"
    },
    photos: ["https://images.unsplash.com/photo-1621905252507-b35492cc74b4?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"],
    services: [],
    reviews: [{ rating: "5", text: "Fast response time", author: "Lisa K.", date: "2025-01-20" }, { rating: "4", text: "Professional work", author: "Robert L.", date: "2025-02-14" }, { rating: "5", text: "Excellent service", author: "Carol D.", date: "2025-03-10" }],
    createdAt: "2024-11-15",
    updatedAt: "2025-03-20",
    badges: ["Verified", "Licensed"],
    rating: 4.7,
    reviewCount: 26,
    responseTime: "< 2 hours",
    specialty: "Electrical",
    fullAddress: "456 Oak Ave, Phoenix, AZ 85016",
    available: true,
    isFeatured: false
  },
  {
    businessId: "pro3",
    name: "Green Lawn Services",
    category: "Landscaping",
    location: "Phoenix, AZ",
    address: "789 Pine Rd, Phoenix, AZ",
    phone: "************",
    website: "www.greenlawn.com",
    email: "<EMAIL>",
    hours: {
      monday: "8am-6pm",
      tuesday: "8am-6pm",
      wednesday: "8am-6pm",
      thursday: "8am-6pm", 
      friday: "8am-6pm",
      saturday: "8am-4pm",
      sunday: "Closed"
    },
    photos: ["https://images.unsplash.com/photo-1594897030264-ab7d87efc473?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"],
    services: [],
    reviews: [{ rating: "4", text: "Good landscaping work", author: "Tom P.", date: "2025-02-01" }, { rating: "5", text: "Beautiful work", author: "Nancy M.", date: "2025-02-25" }, { rating: "4", text: "Nice job", author: "Peter G.", date: "2025-03-15" }],
    createdAt: "2025-01-10",
    updatedAt: "2025-04-15",
    badges: ["Experienced"],
    rating: 4.5,
    reviewCount: 18,
    responseTime: "Same day",
    specialty: "Landscaping",
    fullAddress: "789 Pine Rd, Phoenix, AZ 85008",
    available: true,
    isFeatured: false
  },
  {
    businessId: "pro4",
    name: "Clean & Fresh Cleaning",
    category: "Cleaning",
    location: "Phoenix, AZ",
    address: "321 Elm St, Phoenix, AZ",
    phone: "************",
    website: "www.cleanfresh.com",
    email: "<EMAIL>",
    hours: {
      monday: "9am-5pm",
      tuesday: "9am-5pm",
      wednesday: "9am-5pm",
      thursday: "9am-5pm", 
      friday: "9am-5pm",
      saturday: "9am-3pm",
      sunday: "Closed"
    },
    photos: ["https://images.unsplash.com/photo-1563453392212-326f5e854473?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"],
    services: [],
    reviews: [{ rating: "5", text: "Spotless cleaning", author: "Mary T.", date: "2025-01-25" }, { rating: "5", text: "Excellent service", author: "David C.", date: "2025-03-02" }, { rating: "4", text: "Great job", author: "Julie S.", date: "2025-03-20" }],
    createdAt: "2024-12-20",
    updatedAt: "2025-04-10",
    badges: ["Verified", "Bonded"],
    rating: 4.8,
    reviewCount: 32,
    responseTime: "< 3 hours",
    specialty: "Cleaning",
    fullAddress: "321 Elm St, Phoenix, AZ 85012",
    available: true,
    isFeatured: true
  },
];

const mockJobs: Job[] = [
  {
    id: "job1",
    title: "Kitchen Sink Repair",
    description: "Leaking kitchen sink needs to be fixed",
    customerName: "John Doe",
    serviceType: "Plumbing",
    status: "Open",
    createdDate: "2025-05-01",
    value: "$150-300",
    hasNotes: true,
    location: "Phoenix, AZ"
  },
  {
    id: "job2",
    title: "Bathroom Remodel",
    description: "Complete bathroom renovation including tiles and fixtures",
    customerName: "John Doe",
    serviceType: "Plumbing",
    status: "Open",
    createdDate: "2025-05-03",
    value: "$3,000-5,000",
    hasNotes: false,
    location: "Phoenix, AZ"
  }
];

// Service categories for selection
const serviceCategories = [
  { id: "plumbing", name: "Plumbing", icon: "🚿" },
  { id: "electrical", name: "Electrical", icon: "💡" },
  { id: "hvac", name: "HVAC", icon: "❄️" },
  { id: "landscaping", name: "Landscaping", icon: "🌱" },
  { id: "cleaning", name: "Cleaning", icon: "🧹" },
  { id: "handyman", name: "Handyman", icon: "🔧" },
  { id: "painting", name: "Painting", icon: "🎨" },
  { id: "roofing", name: "Roofing", icon: "🏠" },
];

export default function FindProMobile() {
  const navigate = useNavigate();
  const location = useLocation();
  const [zipCode, setZipCode] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [providers, setProviders] = useState(mockProviders);
  const [showJobSelection, setShowJobSelection] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const [selectedJob, setSelectedJob] = useState<string | null>(null);
  const [invitedProviders, setInvitedProviders] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const { zipCode: detectedZipCode, loading: locationLoading } = useGeolocation();
  
  useEffect(() => {
    if (detectedZipCode && !zipCode) {
      setZipCode(detectedZipCode);
    }
  }, [detectedZipCode, zipCode]);

  // Filter providers based on search query and category
  const filteredProviders = providers.filter(provider => {
    const matchesSearch = !searchQuery || 
      provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.specialty?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = !selectedCategory || 
      provider.category.toLowerCase() === selectedCategory.toLowerCase();
    
    return matchesSearch && matchesCategory;
  });

  const handleProviderInvite = (providerId: string) => {
    setSelectedProvider(providerId);
    setShowJobSelection(true);
  };

  const handleSendInvite = () => {
    if (selectedProvider && selectedJob) {
      // In a real app, we would send this invitation to the backend
      console.log(`Sending invite to provider ${selectedProvider} for job ${selectedJob}`);
      
      // Add to invited providers list
      setInvitedProviders([...invitedProviders, selectedProvider]);
      
      // Reset states
      setShowJobSelection(false);
      setSelectedProvider(null);
      setSelectedJob(null);
      
      // Show success message (in a real app we would use toast notification)
      alert("Invitation sent successfully!");
    }
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId === selectedCategory ? null : categoryId);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-20">
      {/* Header */}
      <header className="sticky top-0 bg-white dark:bg-gray-800 shadow-sm z-10">
        <div className="flex items-center p-4">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => navigate('/customer/dashboard')}
            className="mr-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold">Find Professionals</h1>
        </div>
        
        {/* Search Bar */}
        <div className="px-4 pb-4">
          <SearchBar 
            variant="zipcode"
            placeholder="Search for professionals"
            locationPlaceholder={locationLoading ? "Detecting location..." : "Enter ZIP code"}
            defaultZip={zipCode}
            showButton={true}
            size="sm"
            className="w-full"
            onZipSubmit={(zip) => setZipCode(zip)}
          />
        </div>
        
        {/* Categories Scroller */}
        <div className="overflow-x-auto px-4 pb-3 hide-scrollbar">
          <div className="flex space-x-3">
            {serviceCategories.map(category => (
              <button
                key={category.id}
                onClick={() => handleCategorySelect(category.id)}
                className={`flex flex-col items-center py-2 px-4 rounded-lg min-w-[80px] ${
                  selectedCategory === category.id 
                    ? 'bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-200' 
                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700'
                }`}
              >
                <span className="text-2xl mb-1">{category.icon}</span>
                <span className="text-xs font-medium">{category.name}</span>
              </button>
            ))}
          </div>
        </div>
        
        <Separator />
      </header>

      <main className="p-4">
        {/* Results Summary */}
        <div className="flex justify-between items-center mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {filteredProviders.length} professionals near {zipCode || 'you'}
          </p>
          <Button variant="ghost" size="sm" className="flex items-center gap-1">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
        </div>

        {/* Provider Results */}
        <div className="space-y-4 mb-20">
          {locationLoading ? (
            // Show skeletons while loading
            <>
              <MobileProviderSkeleton />
              <MobileProviderSkeleton />
            </>
          ) : filteredProviders.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-gray-500 dark:text-gray-400">No professionals found matching your criteria</p>
              <Button 
                variant="link" 
                onClick={() => {
                  setSelectedCategory(null);
                  setSearchQuery("");
                }}
              >
                Clear filters
              </Button>
            </div>
          ) : (
            filteredProviders.map(provider => (
              <div key={provider.businessId} className="relative">
                <MobileProviderCard 
                  provider={provider} 
                  onViewProfile={() => navigate(`/professionals/${provider.businessId}`)}
                  className={invitedProviders.includes(provider.businessId) ? "border-green-400" : ""}
                />
                <div className="mt-2">
                  <Button 
                    onClick={() => handleProviderInvite(provider.businessId)}
                    className="w-full"
                    disabled={invitedProviders.includes(provider.businessId)}
                  >
                    {invitedProviders.includes(provider.businessId) ? 
                      "Invitation Sent" : "Invite to Bid"}
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </main>

      {/* Job Selection Modal */}
      {showJobSelection && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center slide-up">
          <div className="bg-white dark:bg-gray-800 rounded-t-xl w-full max-h-[80%] overflow-auto">
            <div className="p-4 border-b">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-bold">Select a job</h3>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => setShowJobSelection(false)}
                >
                  <ArrowLeft className="h-5 w-5" />
                </Button>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Choose which job you want this pro to bid on
              </p>
            </div>
            
            <div className="p-4 space-y-3">
              {mockJobs.map(job => (
                <div 
                  key={job.id}
                  className={`p-4 border rounded-lg ${
                    selectedJob === job.id ? 'border-primary bg-primary/5' : 'border-gray-200 dark:border-gray-700'
                  }`}
                  onClick={() => setSelectedJob(job.id)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{job.title}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {job.description.substring(0, 60)}{job.description.length > 60 ? '...' : ''}
                      </p>
                      <div className="flex items-center mt-2">
                        <Badge variant="secondary" className="mr-2">
                          {job.serviceType}
                        </Badge>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {job.value}
                        </span>
                      </div>
                    </div>
                    {selectedJob === job.id && (
                      <CheckCircle className="h-5 w-5 text-primary" />
                    )}
                  </div>
                </div>
              ))}
              
              <div className="mt-4">
                <Button 
                  variant="link" 
                  className="w-full"
                  onClick={() => navigate('/create-job')}
                >
                  + Create a new job
                </Button>
              </div>
            </div>
            
            <div className="p-4 border-t sticky bottom-0 bg-white dark:bg-gray-800">
              <Button 
                onClick={handleSendInvite} 
                className="w-full" 
                disabled={!selectedJob}
              >
                Send Invitation
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
