
import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { ArrowLeft, HelpCircle, FileText, Calculator, Share2, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import {SEO} from "@/components/SEO.tsx";

const PricingCalculator: React.FC = () => {
  // State for form inputs
  const [laborCost, setLaborCost] = useState<number>(25);
  const [materialsCost, setMaterialsCost] = useState<number>(0);
  const [hoursPerJob, setHoursPerJob] = useState<number>(2);
  const [overheadPercentage, setOverheadPercentage] = useState<number>(20);
  const [profitMargin, setProfitMargin] = useState<number>(30);
  
  // Calculated values
  const [totalCost, setTotalCost] = useState<number>(0);
  const [recommendedPrice, setRecommendedPrice] = useState<number>(0);
  const [hourlyRate, setHourlyRate] = useState<number>(0);

  // Calculate values when inputs change
  useEffect(() => {
    // Calculate direct costs
    const directCosts = (laborCost * hoursPerJob) + materialsCost;
    
    // Calculate overhead amount
    const overheadAmount = directCosts * (overheadPercentage / 100);
    
    // Total cost including overhead
    const calculatedTotalCost = directCosts + overheadAmount;
    setTotalCost(calculatedTotalCost);
    
    // Calculate recommended price with profit margin
    const calculatedRecommendedPrice = calculatedTotalCost / (1 - (profitMargin / 100));
    setRecommendedPrice(calculatedRecommendedPrice);
    
    // Calculate hourly rate
    if (hoursPerJob > 0) {
      setHourlyRate(calculatedRecommendedPrice / hoursPerJob);
    }
  }, [laborCost, materialsCost, hoursPerJob, overheadPercentage, profitMargin]);

  return (
    <Layout>
      <SEO
          title="Service Pricing Calculator | Free Tool to Price Your Services Accurately"
          description="Use our free Service Pricing Calculator to find the perfect rate for your services. Set your costs, choose your profit margin, and get instant pricing recommendations. Try JobON's tool now—free and easy to use!"
          localBusinessSchema={true}
          serviceType="Pricing Calculator"
          serviceSlug="pricing-calculator"
          canonicalUrl="/free-tools/pricing-calculator"
      />
      <div className="pt-28 pb-24 px-6 md:px-12 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto max-w-5xl">
          {/* Header with back button */}
          <div className="mb-8">
            <Link to="/free-tools" className="inline-flex items-center text-primary hover:underline mb-4 text-lg">
              <ArrowLeft className="mr-2 h-5 w-5" />
              Back to Free Tools
            </Link>
          
            {/* Calculator Title */}
            <div className="flex flex-col space-y-4">
              <Badge className="w-fit text-base py-1 px-4 bg-primary/10 text-primary border-none">Free Tool</Badge>
              <h1 className="text-4xl md:text-5xl font-bold tracking-tight">Service Pricing Calculator</h1>
              <p className="text-xl text-foreground/80 max-w-3xl leading-relaxed">
                Set the right price for your services with our intuitive calculator. Enter your costs and desired profit margin to get an optimal pricing recommendation.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-10 mt-12">
            {/* Calculator Inputs */}
            <div className="lg:col-span-2">
              <Card className="border-2 shadow-md">
                <CardHeader className="pb-2">
                  <CardTitle className="text-2xl">Input Your Costs</CardTitle>
                  <CardDescription className="text-base">Enter your service details to calculate optimal pricing</CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                  {/* Labor Cost */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="laborCost" className="text-base flex items-center font-medium">
                        Hourly Labor Cost 
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3">
                              <p className="max-w-xs">The hourly cost of labor (your own or employees)</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg">${laborCost}/hour</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={0} 
                        max={100} 
                        step={1} 
                        defaultValue={[25]} 
                        value={[laborCost]}
                        onValueChange={(value) => setLaborCost(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="laborCost" 
                        value={laborCost}
                        className="w-24 text-lg" 
                        onChange={(e) => setLaborCost(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Hours Per Job */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="hoursPerJob" className="text-base flex items-center font-medium">
                        Hours Per Job
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3">
                              <p className="max-w-xs">Average number of hours spent on each job</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg">{hoursPerJob} hours</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={0.5} 
                        max={24} 
                        step={0.5} 
                        defaultValue={[2]} 
                        value={[hoursPerJob]}
                        onValueChange={(value) => setHoursPerJob(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="hoursPerJob" 
                        value={hoursPerJob}
                        className="w-24 text-lg" 
                        onChange={(e) => setHoursPerJob(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Materials Cost */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="materialsCost" className="text-base flex items-center font-medium">
                        Materials Cost
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3">
                              <p className="max-w-xs">Total cost of materials used for the job</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg">${materialsCost}</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={0} 
                        max={1000} 
                        step={5} 
                        defaultValue={[0]} 
                        value={[materialsCost]}
                        onValueChange={(value) => setMaterialsCost(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="materialsCost" 
                        value={materialsCost}
                        className="w-24 text-lg" 
                        onChange={(e) => setMaterialsCost(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Overhead Percentage */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="overheadPercentage" className="text-base flex items-center font-medium">
                        Overhead Percentage
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3">
                              <p className="max-w-xs">Business expenses like rent, utilities, vehicle costs, insurance, etc.</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg">{overheadPercentage}%</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={0} 
                        max={100} 
                        step={1} 
                        defaultValue={[20]} 
                        value={[overheadPercentage]}
                        onValueChange={(value) => setOverheadPercentage(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="overheadPercentage" 
                        value={overheadPercentage}
                        className="w-24 text-lg" 
                        onChange={(e) => setOverheadPercentage(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Profit Margin */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="profitMargin" className="text-base flex items-center font-medium">
                        Desired Profit Margin
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3">
                              <p className="max-w-xs">The percentage of profit you want to make on each job</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg">{profitMargin}%</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={0} 
                        max={70} 
                        step={1} 
                        defaultValue={[30]} 
                        value={[profitMargin]}
                        onValueChange={(value) => setProfitMargin(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="profitMargin" 
                        value={profitMargin}
                        className="w-24 text-lg" 
                        onChange={(e) => setProfitMargin(Number(e.target.value))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Results Card */}
            <div>
              <Card className="sticky top-24 border-2 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader className="pb-2">
                  <CardTitle className="text-xl">Recommended Pricing</CardTitle>
                  <CardDescription className="text-base">Based on your inputs</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Total Price */}
                  <div className="text-center py-6 bg-primary/5 rounded-lg border border-primary/10">
                    <div className="text-5xl font-bold text-primary mb-1">
                      ${recommendedPrice.toFixed(2)}
                    </div>
                    <p className="text-base text-muted-foreground">Recommended Total Price</p>
                  </div>
                  
                  <Separator className="my-2" />
                  
                  <div className="space-y-4">
                    <div className="flex justify-between py-1">
                      <span className="text-base text-muted-foreground">Total Cost:</span>
                      <span className="font-medium text-lg">${totalCost.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-base text-muted-foreground">Hourly Rate:</span>
                      <span className="font-medium text-lg">${hourlyRate.toFixed(2)}/hr</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-base text-muted-foreground">Your Profit:</span>
                      <span className="font-medium text-lg">${(recommendedPrice - totalCost).toFixed(2)}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex-col space-y-3 pt-2">
                  <Button className="w-full text-base py-6" variant="default">
                    <FileText className="mr-2 h-5 w-5" />
                    Download PDF Report
                  </Button>
                  <Button className="w-full text-base py-6" variant="outline">
                    <Share2 className="mr-2 h-5 w-5" />
                    Share Results
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
          
          {/* Pricing Tips */}
          <div className="mt-16">
            <h2 className="text-3xl font-semibold mb-8 tracking-tight">Pricing Tips for Service Providers</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-6">
              <Card className="border-2 shadow-sm hover:shadow-md transition-all">
                <CardHeader className="pb-2">
                  <CardTitle className="text-xl flex items-center">
                    <span className="p-2 mr-3 rounded-full bg-primary/10">
                      <Calculator className="h-5 w-5 text-primary" />
                    </span>
                    Don't Underprice Your Services
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-lg text-foreground/80 leading-relaxed">
                    Underpricing may get you customers in the short term, but it's unsustainable. Ensure you're covering all costs and making a reasonable profit.
                  </p>
                </CardContent>
              </Card>
              <Card className="border-2 shadow-sm hover:shadow-md transition-all">
                <CardHeader className="pb-2">
                  <CardTitle className="text-xl flex items-center">
                    <span className="p-2 mr-3 rounded-full bg-primary/10">
                      <ArrowRight className="h-5 w-5 text-primary" />
                    </span>
                    Consider Value-Based Pricing
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-lg text-foreground/80 leading-relaxed">
                    Price based on the value you provide to customers, not just your costs. Specialized services should command higher rates.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default PricingCalculator;
