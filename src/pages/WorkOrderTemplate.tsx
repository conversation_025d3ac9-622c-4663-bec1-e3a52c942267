
import React, { useState } from 'react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Download, Send, Plus, Trash, ClipboardCheck, FileCheck } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {SEO} from "@/components/SEO.tsx";

interface WorkOrderTask {
  id: string;
  description: string;
  assignedTo: string;
  status: 'pending' | 'in-progress' | 'completed';
  hours: number;
}

const WorkOrderTemplate: React.FC = () => {
  const [companyName, setCompanyName] = useState<string>('');
  const [workOrderNumber, setWorkOrderNumber] = useState<string>('WO-001');
  const [customerName, setCustomerName] = useState<string>('');
  const [customerPhone, setCustomerPhone] = useState<string>('');
  const [customerEmail, setCustomerEmail] = useState<string>('');
  const [serviceAddress, setServiceAddress] = useState<string>('');
  const [startDate, setStartDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [priority, setPriority] = useState<string>('medium');
  
  const [tasks, setTasks] = useState<WorkOrderTask[]>([
    { id: '1', description: '', assignedTo: '', status: 'pending', hours: 1 }
  ]);
  
  const [notes, setNotes] = useState<string>('');
  const [materialsList, setMaterialsList] = useState<string>('');

  // Add new task
  const addTask = () => {
    const newId = (tasks.length + 1).toString();
    setTasks([...tasks, { id: newId, description: '', assignedTo: '', status: 'pending', hours: 1 }]);
  };

  // Remove task
  const removeTask = (id: string) => {
    if (tasks.length > 1) {
      setTasks(tasks.filter(task => task.id !== id));
    }
  };

  // Update task
  const updateTask = (id: string, field: keyof WorkOrderTask, value: string | number) => {
    setTasks(tasks.map(task => {
      if (task.id === id) {
        return { ...task, [field]: value };
      }
      return task;
    }));
  };

  // Calculate total hours
  const totalHours = tasks.reduce((sum, task) => sum + task.hours, 0);

  // Download work order as PDF (demo functionality)
  const downloadWorkOrder = () => {
    alert("This would download a PDF work order in a real application");
  };

  // Email work order (demo functionality)
  const emailWorkOrder = () => {
    alert("This would send the work order via email in a real application");
  };

  return (
    <Layout>
      <SEO
          title="Work Order Template | Create Free Work Orders for Service Jobs"
          description="Create and customize professional work orders easily with JobON’s free Work Order Template. Organize service jobs, track tasks, and download work orders instantly!"
          localBusinessSchema={true}
          serviceType="Work order template"
          serviceSlug="work-order-template"
          canonicalUrl="/free-tools/work-order-template"
      />
      <div className="pt-28 pb-24 px-6 md:px-12 bg-gradient-to-b from-background via-background to-background/50 min-h-screen">
        <div className="container mx-auto max-w-6xl">
          {/* Header with back button */}
          <div className="mb-6">
            <Link to="/free-tools" className="inline-flex items-center text-primary hover:underline mb-2">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Free Tools
            </Link>
          
            {/* Page Title */}
            <h1 className="text-3xl md:text-4xl font-bold mt-4 mb-2">Work Order Template</h1>
            <p className="text-muted-foreground text-lg max-w-2xl">
              Create professional work orders to organize and track your service jobs.
            </p>
          </div>
          
          {/* Template Options - Moved to the top */}
          <div className="mb-12">
            <h2 className="text-2xl font-semibold mb-6">Work Order Templates</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="bg-white dark:bg-gray-800 border-2 border-primary transition-all hover:shadow-md">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Standard Template</CardTitle>
                </CardHeader>
                <CardContent className="flex justify-center py-8">
                  <ClipboardCheck className="h-20 w-20 text-primary opacity-70" />
                </CardContent>
                <CardFooter className="bg-primary/5 pt-3 pb-4 px-6 rounded-b-lg">
                  <div className="text-sm text-center w-full text-primary/80 font-medium">Currently Selected</div>
                </CardFooter>
              </Card>
              <Card className="cursor-pointer hover:border-primary/50 transition-all hover:shadow-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Detailed Template</CardTitle>
                </CardHeader>
                <CardContent className="flex justify-center py-8">
                  <FileCheck className="h-20 w-20 opacity-40" />
                </CardContent>
                <CardFooter className="bg-gray-50 dark:bg-gray-800/50 pt-3 pb-4 px-6 rounded-b-lg">
                  <div className="w-full text-center text-xs text-muted-foreground">
                    Coming Soon
                  </div>
                </CardFooter>
              </Card>
              <Card className="cursor-pointer hover:border-primary/50 transition-all hover:shadow-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Service Template</CardTitle>
                </CardHeader>
                <CardContent className="flex justify-center py-8">
                  <FileCheck className="h-20 w-20 opacity-40" />
                </CardContent>
                <CardFooter className="bg-gray-50 dark:bg-gray-800/50 pt-3 pb-4 px-6 rounded-b-lg">
                  <div className="w-full text-center text-xs text-muted-foreground">
                    Coming Soon
                  </div>
                </CardFooter>
              </Card>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Work Order Form */}
            <div className="lg:col-span-7 space-y-8">
              <Card className="shadow-sm border border-gray-200 dark:border-gray-700">
                <CardHeader className="pb-3 space-y-1">
                  <CardTitle className="text-xl">Work Order Information</CardTitle>
                  <CardDescription>Enter the details for your work order</CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                  {/* Company Details */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-lg">Company Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="companyName" className="text-sm font-medium">Company Name</Label>
                        <Input 
                          id="companyName" 
                          value={companyName}
                          onChange={(e) => setCompanyName(e.target.value)}
                          placeholder="Your Company Name"
                          className="h-10"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="workOrderNumber" className="text-sm font-medium">Work Order Number</Label>
                        <Input 
                          id="workOrderNumber" 
                          value={workOrderNumber}
                          onChange={(e) => setWorkOrderNumber(e.target.value)}
                          placeholder="e.g. WO-001"
                          className="h-10"
                        />
                      </div>
                    </div>
                  </div>

                  <Separator className="my-2" />

                  {/* Customer Details */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-lg">Customer Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="customerName" className="text-sm font-medium">Customer Name</Label>
                        <Input 
                          id="customerName" 
                          value={customerName}
                          onChange={(e) => setCustomerName(e.target.value)}
                          placeholder="Customer Name"
                          className="h-10"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="customerPhone" className="text-sm font-medium">Phone Number</Label>
                        <Input 
                          id="customerPhone" 
                          value={customerPhone}
                          onChange={(e) => setCustomerPhone(e.target.value)}
                          placeholder="(*************"
                          className="h-10"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2 pt-2">
                      <Label htmlFor="customerEmail" className="text-sm font-medium">Email Address</Label>
                      <Input 
                        id="customerEmail" 
                        type="email"
                        value={customerEmail}
                        onChange={(e) => setCustomerEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        className="h-10"
                      />
                    </div>
                    
                    <div className="space-y-2 pt-2">
                      <Label htmlFor="serviceAddress" className="text-sm font-medium">Service Address</Label>
                      <Textarea 
                        id="serviceAddress" 
                        value={serviceAddress}
                        onChange={(e) => setServiceAddress(e.target.value)}
                        placeholder="123 Main St, Anytown, ST 12345"
                        rows={2}
                      />
                    </div>
                  </div>

                  <Separator className="my-2" />

                  {/* Job Details */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-lg">Job Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="startDate" className="text-sm font-medium">Start Date</Label>
                        <Input 
                          id="startDate" 
                          type="date"
                          value={startDate}
                          onChange={(e) => setStartDate(e.target.value)}
                          className="h-10"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="priority" className="text-sm font-medium">Priority</Label>
                        <Select value={priority} onValueChange={setPriority}>
                          <SelectTrigger className="h-10">
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="urgent">Urgent</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Tasks */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium text-lg">Tasks</h3>
                      <Button type="button" variant="outline" size="sm" onClick={addTask} className="h-8">
                        <Plus className="h-4 w-4 mr-1" /> Add Task
                      </Button>
                    </div>
                    
                    <div className="space-y-4">
                      {tasks.map((task) => (
                        <div key={task.id} className="grid grid-cols-12 gap-4 items-start p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50 border border-gray-100 dark:border-gray-800">
                          <div className="col-span-12 md:col-span-5 space-y-2">
                            <Label htmlFor={`description-${task.id}`} className="text-sm font-medium">Description</Label>
                            <Input 
                              id={`description-${task.id}`}
                              value={task.description}
                              onChange={(e) => updateTask(task.id, 'description', e.target.value)}
                              placeholder="Task Description"
                              className="h-10"
                            />
                          </div>
                          <div className="col-span-6 md:col-span-3 space-y-2">
                            <Label htmlFor={`assignedTo-${task.id}`} className="text-sm font-medium">Assigned To</Label>
                            <Input 
                              id={`assignedTo-${task.id}`}
                              value={task.assignedTo}
                              onChange={(e) => updateTask(task.id, 'assignedTo', e.target.value)}
                              placeholder="Employee Name"
                              className="h-10"
                            />
                          </div>
                          <div className="col-span-4 md:col-span-3 space-y-2">
                            <Label htmlFor={`hours-${task.id}`} className="text-sm font-medium">Hours</Label>
                            <Input 
                              id={`hours-${task.id}`}
                              type="number"
                              value={task.hours}
                              onChange={(e) => updateTask(task.id, 'hours', parseFloat(e.target.value))}
                              min="0.5"
                              step="0.5"
                              className="h-10"
                            />
                          </div>
                          <div className="col-span-2 md:col-span-1 flex items-end justify-end pt-8">
                            <Button 
                              type="button" 
                              variant="ghost" 
                              size="icon"
                              onClick={() => removeTask(task.id)}
                              disabled={tasks.length === 1}
                              className="h-8 w-8"
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator className="my-2" />

                  {/* Materials */}
                  <div className="space-y-2">
                    <Label htmlFor="materialsList" className="text-sm font-medium">Materials Required</Label>
                    <Textarea 
                      id="materialsList" 
                      value={materialsList}
                      onChange={(e) => setMaterialsList(e.target.value)}
                      placeholder="List materials needed for this job"
                      rows={3}
                    />
                  </div>

                  {/* Notes */}
                  <div className="space-y-2">
                    <Label htmlFor="notes" className="text-sm font-medium">Special Instructions</Label>
                    <Textarea 
                      id="notes" 
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Add any special instructions or notes for the team"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Work Order Preview */}
            <div className="lg:col-span-5">
              <Card className="sticky top-24 shadow-sm border border-gray-200 dark:border-gray-700">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Work Order Preview</CardTitle>
                  <CardDescription>How your work order will look</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-6 rounded-md shadow-sm">
                    <div className="flex justify-between items-start mb-6">
                      <div>
                        <h2 className="text-xl font-bold">{companyName || "Your Company Name"}</h2>
                        <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">Work Order #{workOrderNumber}</div>
                      </div>
                      <div className="text-sm">
                        <div>Date: {startDate}</div>
                        <div className="mt-1">
                          Priority: 
                          <span className={`ml-1 font-semibold ${
                            priority === 'urgent' ? 'text-red-500' : 
                            priority === 'high' ? 'text-orange-500' : 
                            priority === 'medium' ? 'text-blue-500' : 
                            'text-green-500'
                          }`}>
                            {priority.charAt(0).toUpperCase() + priority.slice(1)}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-850 rounded-md">
                      <div className="text-sm font-medium mb-2">Customer Information:</div>
                      <div className="text-sm mb-1">{customerName || "Customer Name"}</div>
                      <div className="text-sm mb-1">{customerPhone || "Phone Number"}</div>
                      <div className="text-sm mb-1">{customerEmail || "Email Address"}</div>
                      <div className="text-sm">{serviceAddress || "Service Address"}</div>
                    </div>
                    
                    <div className="mb-6">
                      <div className="text-sm font-medium mb-2">Tasks:</div>
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b dark:border-gray-700">
                            <th className="text-left py-2 px-1">Description</th>
                            <th className="text-left py-2 px-1">Assigned</th>
                            <th className="text-right py-2 px-1">Hours</th>
                          </tr>
                        </thead>
                        <tbody>
                          {tasks.map((task) => (
                            <tr key={task.id} className="border-b dark:border-gray-700">
                              <td className="py-2 px-1">{task.description || "Task Description"}</td>
                              <td className="py-2 px-1">{task.assignedTo || "Staff"}</td>
                              <td className="text-right py-2 px-1">{task.hours}</td>
                            </tr>
                          ))}
                        </tbody>
                        <tfoot>
                          <tr>
                            <td colSpan={2} className="text-right pt-2 font-medium">Total Hours:</td>
                            <td className="text-right pt-2 font-medium">{totalHours}</td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                    
                    {materialsList && (
                      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-850 rounded-md">
                        <div className="text-sm font-medium mb-2">Materials Required:</div>
                        <div className="text-sm whitespace-pre-line">{materialsList}</div>
                      </div>
                    )}
                    
                    {notes && (
                      <div className="p-4 bg-gray-50 dark:bg-gray-850 rounded-md mb-6">
                        <div className="text-sm font-medium mb-2">Special Instructions:</div>
                        <div className="text-sm whitespace-pre-line">{notes}</div>
                      </div>
                    )}
                    
                    <div className="mt-8 pt-4 border-t dark:border-gray-700 grid grid-cols-2 gap-6">
                      <div>
                        <div className="font-medium text-sm mb-2">Customer Signature:</div>
                        <div className="h-10 mt-2 border-b border-dashed border-gray-300 dark:border-gray-600"></div>
                      </div>
                      <div>
                        <div className="font-medium text-sm mb-2">Completion Date:</div>
                        <div className="h-10 mt-2 border-b border-dashed border-gray-300 dark:border-gray-600"></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex-col space-y-3 pt-4">
                  <Button className="w-full h-10" onClick={downloadWorkOrder}>
                    <Download className="mr-2 h-4 w-4" />
                    Download Work Order
                  </Button>
                  <Button className="w-full h-10" variant="outline" onClick={emailWorkOrder}>
                    <Send className="mr-2 h-4 w-4" />
                    Email Work Order
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default WorkOrderTemplate;
