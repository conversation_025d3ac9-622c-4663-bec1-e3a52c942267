
import React, { useEffect } from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { JobsManagement } from '@/components/provider/jobs/JobsManagement';
import { useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { Card } from '@/components/ui/card';

const ProviderJobs = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  // Check if user is authenticated as a provider
  useEffect(() => {
    const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
    const userType = localStorage.getItem('userType');
    
    if (!isAuthenticated || userType !== 'professional') {
      // For demo purposes, we'll set these values
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('userType', 'professional');
      
      // Alternatively, redirect to login: navigate('/auth');
    }
  }, [navigate]);

  return (
    <ProviderDashboardLayout pageTitle="Jobs">
      <div className={isMobile ? "px-2" : ""}>
        {isMobile && (
          <div className="mb-4 bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl shadow-sm border border-blue-200">
            <h2 className="text-lg font-semibold mb-1">Manage Your Jobs</h2>
            <p className="text-sm text-muted-foreground">
              View and update your leads, active jobs, and completed work.
            </p>
          </div>
        )}
        <JobsManagement />
      </div>
    </ProviderDashboardLayout>
  );
};

export default ProviderJobs;
