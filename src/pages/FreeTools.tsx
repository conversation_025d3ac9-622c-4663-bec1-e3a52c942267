import React, { useState } from 'react';
import { Layout } from '@/components/Layout';
import { Link } from 'react-router-dom';
import { 
  Calculator, 
  FileText, 
  Calendar, 
  BookOpen, 
  ClipboardCheck, 
  ChartBar, 
  ScrollText,
  Receipt,
  Home,
  Scissors,
  Construction,
  LucideIcon,
  Laptop,
  ImageIcon,
  ArrowRight,
  CalendarClock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SEO } from '@/components/SEO';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { RecentTools } from '@/components/RecentTools';
import FreeToolsSignupDialog from '@/components/FreeToolsSignupDialog';

interface ToolCardProps {
  icon: React.ReactNode;
  title: string;
  description?: string;
  href: string;
  comingSoon?: boolean;
  illustration?: string;
}

const ToolCard: React.FC<ToolCardProps> = ({ 
  icon, 
  title, 
  description, 
  href, 
  comingSoon = false,
  illustration
}) => (
  <Card className="h-full transition-all hover:shadow-md overflow-hidden hover:border-primary/30 hover:scale-[1.02]">
    <CardHeader className="pb-2">
      <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary mb-2">
        {icon}
      </div>
      <CardTitle className="text-lg font-bold text-gray-800 dark:text-gray-100">
        {title}
      </CardTitle>
      {description && (
        <p className="text-sm text-gray-600 dark:text-gray-300">{description}</p>
      )}
    </CardHeader>
    <CardContent className="pt-0 pb-3 flex justify-center">
      {illustration ? (
        <div className="w-full h-32 flex items-center justify-center">
          <img 
            src={illustration} 
            alt={title} 
            className="h-28 object-contain" 
          />
        </div>
      ) : (
        <div className="w-full h-32 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
          {React.cloneElement(icon as React.ReactElement, { 
            className: 'h-16 w-16 text-primary opacity-20' 
          })}
        </div>
      )}
    </CardContent>
    <CardFooter>
      {comingSoon ? (
        <Button variant="outline" disabled className="w-full">
          Coming Soon
        </Button>
      ) : (
        <Button asChild className="w-full">
          <Link to={href}>Access Tool</Link>
        </Button>
      )}
    </CardFooter>
  </Card>
);

interface CalculatorCardProps {
  icon: React.ReactNode; 
  title: string;
  href: string;
  comingSoon?: boolean;
  type?: 'calculator' | 'document' | 'image' | 'laptop' | 'receipt' | 'calendar';
  popular?: boolean;
}

const CalculatorCard: React.FC<CalculatorCardProps> = ({ 
  icon, 
  title, 
  href, 
  comingSoon = false,
  type = 'calculator',
  popular = false
}) => {
  const bgColor = "bg-gray-50 dark:bg-gray-800";
  const accentColor = "text-primary";
  const darkText = "text-gray-800 dark:text-white";

  return (
    <Link 
      to={comingSoon ? "#" : `/free-tools${href}`}
      className={`group block h-full transition-all ${comingSoon ? 'cursor-not-allowed opacity-70' : 'hover:scale-[1.02]'}`}
    >
      <div className={`${bgColor} rounded-xl p-5 h-full flex flex-col border-2 border-transparent group-hover:border-primary/20 group-hover:shadow-md`}>
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-10 h-10 mr-3 rounded-full bg-primary/10">
              {React.cloneElement(icon as React.ReactElement, { 
                className: "h-6 w-6 text-primary"
              })}
            </div>
            <h3 className={`${darkText} font-bold text-xl`}>{title}</h3>
          </div>
          {popular && (
            <Badge variant="secondary" className="bg-primary/10 text-primary text-sm">Popular</Badge>
          )}
        </div>

        <div className="flex-grow flex items-center justify-center relative">
          {type === 'calculator' && (
            <div className="relative">
              <div className="border-2 border-gray-300 dark:border-gray-600 h-36 w-28 rounded-lg bg-white dark:bg-gray-700 relative overflow-hidden shadow-sm">
                <div className="absolute top-0 left-0 right-0 h-8 bg-primary flex items-center justify-center">
                  <span className="text-xs font-bold uppercase text-white">
                    {title.includes("Profit") ? "Profit" : title.includes("Labor") ? "Labor" : "Pricing"}
                  </span>
                </div>
                <div className="absolute bottom-0 left-0 right-0 h-24 grid grid-cols-3 grid-rows-4 gap-[1px] p-1 pt-8">
                  {Array(12).fill(0).map((_, i) => (
                    <div key={i} className="bg-gray-200 dark:bg-gray-600 rounded-sm"></div>
                  ))}
                </div>
              </div>
              <div className="absolute -right-4 bottom-4 h-32 w-1 bg-gray-300 dark:bg-gray-500 transform rotate-12 rounded-full"></div>
              <div className="absolute -right-6 bottom-8 w-3 h-3 rounded-full bg-primary"></div>
              <div className="absolute -right-8 bottom-20 w-2 h-2 rounded-full bg-primary"></div>
              <div className="absolute -left-2 top-4 w-2 h-2 rounded-full bg-primary"></div>
              <div className="absolute -left-4 top-10 w-3 h-3 rounded-full bg-primary"></div>
            </div>
          )}

          {type === 'document' && (
            <div className="relative">
              <div className="h-36 w-28 bg-white dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 rounded-lg transform rotate-[-5deg] absolute left-4 top-0 z-10 shadow-sm"></div>
              <div className="h-36 w-28 bg-white dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 rounded-lg transform rotate-[5deg] absolute right-4 top-0 z-10 shadow-sm"></div>
              <div className="h-36 w-28 bg-white dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 rounded-lg relative z-20 shadow-sm">
                <div className="h-3 w-20 bg-primary mt-4 ml-4 rounded-sm"></div>
                <div className="h-2 w-16 bg-gray-200 dark:bg-gray-500 mt-3 ml-4 rounded-sm"></div>
                <div className="h-2 w-18 bg-gray-200 dark:bg-gray-500 mt-2 ml-4 rounded-sm"></div>
                <div className="h-2 w-12 bg-gray-200 dark:bg-gray-500 mt-2 ml-4 rounded-sm"></div>
                <div className="h-8 w-8 border-2 border-primary mt-3 ml-4 rounded-sm flex items-center justify-center">
                  <span className="text-xl font-bold text-primary">$</span>
                </div>
              </div>
              <div className="absolute -right-3 bottom-4 w-2 h-2 rounded-full bg-primary"></div>
              <div className="absolute -right-1 bottom-10 w-3 h-3 rounded-full bg-primary"></div>
              <div className="absolute -left-2 top-4 w-2 h-2 rounded-full bg-primary"></div>
            </div>
          )}

          {type === 'laptop' && (
            <div className="relative">
              <div className="w-36 h-24 bg-gray-300 dark:bg-gray-600 rounded-t-lg border-2 border-gray-300 dark:border-gray-600 flex items-center justify-center shadow-sm">
                <div className="w-28 h-16 bg-white dark:bg-gray-700 rounded-sm flex items-center justify-center">
                  <div className="h-3 w-20 bg-primary rounded-sm"></div>
                </div>
              </div>
              <div className="w-44 h-3 bg-gray-300 dark:bg-gray-600 rounded-b-lg border-x-2 border-b-2 border-gray-300 dark:border-gray-600 -mt-[1px] mx-auto shadow-sm"></div>
              <div className="absolute -right-3 bottom-4 w-2 h-2 rounded-full bg-primary"></div>
              <div className="absolute -right-1 bottom-10 w-3 h-3 rounded-full bg-primary"></div>
              <div className="absolute -left-2 top-4 w-2 h-2 rounded-full bg-primary"></div>
            </div>
          )}

          {type === 'image' && (
            <div className="relative">
              <div className="flex space-x-2">
                <div className="w-20 h-32 border-2 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded-lg flex items-center justify-center shadow-sm">
                  <div className="w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded"></div>
                </div>
                <div className="w-20 h-32 border-2 border-gray-300 dark:border-gray-600 bg-primary/20 rounded-lg flex items-center justify-center shadow-sm">
                  <div className="w-12 h-12 bg-primary rounded"></div>
                </div>
              </div>
              <div className="absolute -right-3 top-8 w-3 h-3 rounded-full bg-primary"></div>
              <div className="absolute -right-1 top-20 w-2 h-2 rounded-full bg-primary"></div>
              <div className="absolute -left-2 bottom-4 w-2 h-2 rounded-full bg-primary"></div>
            </div>
          )}

          {type === 'receipt' && (
            <div className="relative">
              <div className="w-28 h-36 bg-white dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 rounded-lg transform rotate-[8deg] shadow-sm">
                <div className="h-3 w-20 bg-primary mt-4 ml-4 rounded-sm"></div>
                <div className="h-2 w-16 bg-gray-200 dark:bg-gray-500 mt-3 ml-4 rounded-sm"></div>
                <div className="h-2 w-18 bg-gray-200 dark:bg-gray-500 mt-2 ml-4 rounded-sm"></div>
                <div className="h-12 w-20 border-2 border-gray-200 dark:border-gray-500 mt-3 ml-4 rounded flex items-center justify-center">
                  <div className="w-12 h-5 bg-primary rounded-sm"></div>
                </div>
              </div>
              <div className="absolute -right-3 bottom-10 w-3 h-3 rounded-full bg-primary"></div>
              <div className="absolute -right-5 bottom-20 w-2 h-2 rounded-full bg-primary"></div>
              <div className="absolute -left-2 top-8 w-2 h-2 rounded-full bg-primary"></div>
            </div>
          )}

          {type === 'calendar' && (
            <div className="relative">
              <div className="w-32 h-36 bg-white dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-sm">
                <div className="h-8 w-full bg-primary flex items-center justify-center">
                  <span className="text-xs font-bold uppercase text-white">June</span>
                </div>
                <div className="grid grid-cols-7 gap-1 p-2">
                  {Array(28).fill(0).map((_, i) => (
                    <div key={i} 
                      className={`h-4 w-4 flex items-center justify-center text-[8px] ${
                        [8, 15, 22].includes(i) ? 'bg-primary text-white rounded-full' : ''
                      }`}>
                      {i + 1}
                    </div>
                  ))}
                </div>
              </div>
              <div className="absolute -right-3 top-8 w-3 h-3 rounded-full bg-primary"></div>
              <div className="absolute -right-1 top-20 w-2 h-2 rounded-full bg-primary"></div>
              <div className="absolute -left-2 bottom-4 w-2 h-2 rounded-full bg-primary"></div>
            </div>
          )}
        </div>

        {comingSoon && (
          <div className="text-center text-gray-500 dark:text-gray-400 text-base mt-2">
            Coming Soon
          </div>
        )}

        {!comingSoon && (
          <div className="mt-4 text-primary text-base font-medium flex items-center opacity-0 group-hover:opacity-100 transition-opacity">
            Access tool <ArrowRight className="ml-2 h-5 w-5" />
          </div>
        )}
      </div>
    </Link>
  );
};

const FreeTools: React.FC = () => {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <Layout>
      <SEO
          title="Free Business Tools for Service Pros – Calculators & Templates"
          description="Access free tools made for service providers: pricing calculators, profit margin templates, and scheduling aids. No sign-up needed—grow smarter with JobON."
          localBusinessSchema={true}
          serviceType="Financing"
          serviceSlug="financing"
          canonicalUrl="/free-tools"
      />

      <FreeToolsSignupDialog delayInMs={7000} />

      <div className="pt-28 pb-24 px-6 md:px-0 bg-gradient-to-b from-primary/5 to-background">
        <div className="container mx-auto max-w-6xl">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-16 text-center"
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-8 text-gray-900 dark:text-white">
              Free Tools for Service Providers
            </h1>
            <p className="text-xl md:text-2xl text-foreground/70 max-w-3xl mx-auto mb-10 leading-relaxed">
              Grow your business with our collection of free calculators, templates, and resources designed specifically for home service professionals.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20 px-4 py-2.5 text-lg">No signup required</Badge>
              <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20 px-4 py-2.5 text-lg">Always free</Badge>
              <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20 px-4 py-2.5 text-lg">Download & use offline</Badge>
            </div>
          </motion.div>

          <Tabs defaultValue="calculators" className="mb-16 max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
              className="w-full"
            >
              <TabsList className="w-full h-fit max-w-3xl mx-auto grid grid-cols-2 md:grid-cols-4 bg-white dark:bg-gray-800 backdrop-blur-sm rounded-xl p-2 shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
                <TabsTrigger value="calculators" className="text-lg font-medium">Calculators</TabsTrigger>
                <TabsTrigger value="templates" className="text-lg font-medium">Templates</TabsTrigger>
                <TabsTrigger value="resources" className="text-lg font-medium">Resources</TabsTrigger>
                <TabsTrigger value="guides" className="text-lg font-medium">Guides</TabsTrigger>
              </TabsList>
            </motion.div>

            <TabsContent value="calculators" className="mt-12 w-full">
              <motion.div 
                variants={container}
                initial="hidden"
                animate="show"
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<Calculator className="h-6 w-6" />}
                    title="Service Price Calculator"
                    href="/pricing-calculator"
                    type="calculator"
                    popular={true}
                  />
                </motion.div>
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<Calculator className="h-6 w-6" />}
                    title="Profit Margin Calculator"
                    href="/profit-margin-calculator"
                    type="calculator"
                  />
                </motion.div>
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<Calculator className="h-6 w-6" />}
                    title="Labor Cost Calculator"
                    href="/labor-cost-calculator"
                    type="calculator"
                  />
                </motion.div>
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<Home className="h-6 w-6" />}
                    title="House Cleaning Calculator"
                    href="/house-cleaning-calculator"
                    type="calculator"
                  />
                </motion.div>
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<Scissors className="h-6 w-6" />}
                    title="Lawn Care Calculator"
                    href="/lawn-care-calculator"
                    type="calculator"
                  />
                </motion.div>
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<Construction className="h-6 w-6" />}
                    title="Roofing Cost Calculator"
                    href="/roofing-calculator"
                    type="calculator"
                  />
                </motion.div>
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<CalendarClock className="h-6 w-6" />}
                    title="Scheduling Assistant"
                    href="/scheduling-assistant"
                    type="calendar"
                    popular={true}
                  />
                </motion.div>
              </motion.div>
            </TabsContent>

            <TabsContent value="templates" className="mt-12 w-full">
              <motion.div 
                variants={container}
                initial="hidden"
                animate="show"
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<FileText className="h-6 w-6" />}
                    title="Estimate Template"
                    href="/estimate-template"
                    type="document"
                  />
                </motion.div>
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<FileText className="h-6 w-6" />}
                    title="Invoice Templates"
                    href="/invoice-templates"
                    type="document"
                    popular={true}
                  />
                </motion.div>
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<Receipt className="h-6 w-6" />}
                    title="Receipt Generator"
                    href="/receipt-generator"
                    type="receipt"
                  />
                </motion.div>
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<FileText className="h-6 w-6" />}
                    title="Work Order Template"
                    href="/work-order-template"
                    type="laptop"
                  />
                </motion.div>
              </motion.div>
            </TabsContent>

            <TabsContent value="resources" className="mt-12 w-full">
              <motion.div 
                variants={container}
                initial="hidden"
                animate="show"
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<ChartBar className="h-6 w-6" />}
                    title="Profit Forecast Tool"
                    href="/profit-forecast"
                    type="calculator"
                  />
                </motion.div>
                <motion.div variants={item}>
                  <CalculatorCard
                    icon={<ImageIcon className="h-6 w-6" />}
                    title="Before/After Editor"
                    href="/image-editor"
                    type="image"
                  />
                </motion.div>
              </motion.div>
            </TabsContent>

            <TabsContent value="guides" className="mt-12 w-full">
              <motion.div 
                variants={container}
                initial="hidden"
                animate="show"
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                <motion.div variants={item}>
                  <ToolCard
                    icon={<BookOpen className="h-6 w-6" />}
                    title="Pricing Guide"
                    description="Learn how to price your services competitively while ensuring profitability."
                    href="/free-tools/pricing-guide"
                    comingSoon={false}
                  />
                </motion.div>
                <motion.div variants={item}>
                  <ToolCard
                    icon={<BookOpen className="h-6 w-6" />}
                    title="Marketing Basics"
                    description="Simple marketing strategies to help grow your service business."
                    href="/free-tools/marketing-guide"
                    comingSoon={false}
                  />
                </motion.div>
                <motion.div variants={item}>
                  <ToolCard
                    icon={<BookOpen className="h-6 w-6" />}
                    title="Business Operations"
                    description="Best practices for running an efficient service business."
                    href="/free-tools/operations-guide"
                    comingSoon={false}
                  />
                </motion.div>
              </motion.div>
            </TabsContent>
          </Tabs>

          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative rounded-2xl p-8 md:p-12 mt-20 overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/5 z-0"></div>
            <div className="absolute inset-0 backdrop-blur-sm z-0"></div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center relative z-10">
              <div>
                <div className="inline-block px-4 py-2 bg-white/20 dark:bg-white/10 backdrop-blur-sm rounded-full text-primary text-base font-medium mb-4">
                  Most Popular Tool
                </div>
                <h2 className="text-3xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white leading-tight">Pricing Calculator</h2>
                <p className="text-lg md:text-xl text-foreground/80 mb-8 leading-relaxed">
                  Our most popular tool helps you calculate optimal pricing for your services.
                  Ensure you're charging enough to cover costs and make a profit.
                </p>
                <div className="space-y-5">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/40 flex items-center justify-center">
                      <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <span className="text-base md:text-lg text-gray-700 dark:text-gray-300">Calculate labor costs accurately</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/40 flex items-center justify-center">
                      <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <span className="text-base md:text-lg text-gray-700 dark:text-gray-300">Include materials and overhead</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/40 flex items-center justify-center">
                      <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <span className="text-base md:text-lg text-gray-700 dark:text-gray-300">Set your ideal profit margin</span>
                  </div>
                </div>
                <Button asChild size="lg" className="mt-8 rounded-lg text-lg px-8 py-6 h-auto">
                  <Link to="/free-tools/pricing-calculator">
                    Try It Now
                  </Link>
                </Button>
              </div>
              <div className="bg-white/40 dark:bg-gray-800/40 p-6 rounded-xl shadow-sm backdrop-blur-sm border border-white/20 dark:border-gray-700/30">
                <div className="w-full h-[250px] flex items-center justify-center">
                  <div className="relative">
                    <div className="border-2 border-gray-500 dark:border-gray-400 h-52 w-40 rounded-lg bg-white dark:bg-gray-700 relative overflow-hidden shadow-md">
                      <div className="absolute top-0 left-0 right-0 h-12 bg-primary flex items-center justify-center">
                        <span className="text-sm font-bold uppercase text-white">Pricing</span>
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 h-36 grid grid-cols-3 grid-rows-4 gap-[1px] p-1 pt-10">
                        {Array(12).fill(0).map((_, i) => (
                          <div key={i} className="bg-gray-200 dark:bg-gray-600 rounded-sm"></div>
                        ))}
                      </div>
                    </div>
                    <div className="absolute -right-6 bottom-8 h-40 w-2 bg-gray-500 dark:bg-gray-400 transform rotate-12 rounded-full"></div>
                    <div className="absolute -right-10 bottom-16 w-4 h-4 rounded-full bg-primary"></div>
                    <div className="absolute -right-12 bottom-30 w-3 h-3 rounded-full bg-primary"></div>
                    <div className="absolute -left-4 top-6 w-3 h-3 rounded-full bg-primary"></div>
                    <div className="absolute -left-8 top-16 w-4 h-4 rounded-full bg-primary"></div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          <div className="mt-24">
            <div className="flex items-center justify-between mb-10">
              <h2 className="text-3xl md:text-4xl font-bold">Recently Added Tools</h2>
              <Link 
                to="/blog/service-provider-tools" 
                className="text-primary hover:text-primary/80 flex items-center text-lg font-medium"
              >
                See all tools
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </div>

            <RecentTools />
          </div>

          <div className="mt-24 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Need More Business Tools?</h2>
            <p className="text-xl text-muted-foreground mb-10 max-w-3xl mx-auto leading-relaxed">
              Join our platform to access premium features, get listed in our pro directory, 
              and find more clients for your service business.
            </p>
            <div className="flex flex-col sm:flex-row gap-5 justify-center">
              <Button variant="default" size="lg" asChild className="text-lg px-8 py-6 h-auto">
                <Link to="/create-account">Join as a Pro</Link>
              </Button>
              <Button variant="outline" size="lg" asChild className="text-lg px-8 py-6 h-auto">
                <Link to="/blog/service-provider-tools">Browse Our Blog</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default FreeTools;
