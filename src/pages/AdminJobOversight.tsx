
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Search, Filter, BarChart3, Clock, Activity, ShieldAlert, Menu, ArrowRight, Download, FileSpreadsheet } from "lucide-react";
import { Input } from "@/components/ui/input";
import { JobOversightSummary } from "@/components/admin/job-oversight/JobOversightSummary";
import { JobBidsList } from "@/components/admin/job-oversight/JobBidsList";
import { JobTimeline } from "@/components/admin/job-oversight/JobTimeline";
import { ProviderPerformance } from "@/components/admin/job-oversight/ProviderPerformance";
import { DisputesList } from "@/components/admin/job-oversight/DisputesList";
import { MobileDisputesList } from "@/components/admin/job-oversight/MobileDisputesList";
import DisputeDetail from "@/components/admin/job-oversight/dispute-detail/DisputeDetail";
import { JobDispute, DisputeStatus } from "@/types/jobs";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { JobsOverview } from "@/components/admin/job-oversight/JobsOverview";

const AdminJobOversight = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDispute, setSelectedDispute] = useState<JobDispute | null>(null);
  const {
    isMobile,
    isTablet
  } = useUIHelpers();
  const {
    toast
  } = useToast();

  const handleViewDispute = (dispute: JobDispute) => {
    setSelectedDispute(dispute);
  };

  const handleBackFromDispute = () => {
    setSelectedDispute(null);
  };

  const handleDisputeStatusChange = (disputeId: string, newStatus: DisputeStatus) => {
    // In a real app, this would make an API call
    toast({
      title: "Dispute status updated",
      description: `Dispute status changed to ${newStatus.replace("_", " ")}`
    });

    // Update the selected dispute with the new status
    if (selectedDispute && selectedDispute.id === disputeId) {
      setSelectedDispute({
        ...selectedDispute,
        status: newStatus,
        updatedAt: new Date().toISOString()
      });
    }
  };

  // Function to render tab label with icon for mobile
  const renderTabLabel = (icon: React.ReactNode, label: string, count?: number) => {
    return (
      <div className="relative flex items-center gap-1.5">
        {icon}
        {!isMobile && <span>{label}</span>}
        {count !== undefined && 
          <Badge 
            variant={label === "Disputes" ? "destructive" : "secondary"} 
            className={`${isMobile ? 'absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center rounded-full text-[10px]' : 'ml-1'}`}
          >
            {count}
          </Badge>
        }
      </div>
    );
  };

  // Only show the dispute detail view if there's a selected dispute
  if (selectedDispute && activeTab === "disputes") {
    return (
      <div className="space-y-4 md:space-y-6">
        <DisputeDetail 
          dispute={selectedDispute} 
          onBack={handleBackFromDispute} 
          onStatusChange={handleDisputeStatusChange} 
        />
      </div>
    );
  }

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3">
        <div>
          <h1 className="text-xl md:text-2xl font-bold">Job Oversight Dashboard</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Monitor job progress, review bids, and track provider performance
          </p>
        </div>

        <div className="flex items-center gap-2 w-full md:w-auto">
          <div className="relative flex-1 md:flex-none md:w-64">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search jobs..." className="pl-9 pr-4 w-full" value={searchQuery} onChange={e => setSearchQuery(e.target.value)} />
          </div>
          
          {isMobile ? (
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right">
                <div className="p-4 space-y-4">
                  <h3 className="font-medium text-lg">Filter Options</h3>
                  {/* Filter options would go here */}
                  <div className="pt-4">
                    <Button className="w-full">Apply Filters</Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          ) : (
            <Button variant="outline">
              <Filter className="h-4 w-4 md:mr-2" />
              <span className="hidden md:inline">Filter</span>
            </Button>
          )}
        </div>
      </div>

      {/* Summary Cards */}
      <JobOversightSummary />

      {/* Main Content Tabs */}
      <Card>
        <CardHeader className="pb-0">
          <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center pb-3 gap-3">
              <TabsList className="grid grid-cols-5 w-full sm:w-auto bg-white border rounded-md p-1 shadow-sm">
                <TabsTrigger value="overview" className={`px-2 py-1.5 sm:py-2 text-xs sm:text-sm rounded-md
                    ${activeTab === "overview" ? "bg-gray-100 text-gray-900" : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`}>
                  {renderTabLabel(<Activity className="h-4 w-4" />, "Overview")}
                </TabsTrigger>
                <TabsTrigger value="bids" className={`px-2 py-1.5 sm:py-2 text-xs sm:text-sm rounded-md
                    ${activeTab === "bids" ? "bg-gray-100 text-gray-900" : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`}>
                  {renderTabLabel(<Clock className="h-4 w-4" />, "Bids", 8)}
                </TabsTrigger>
                <TabsTrigger value="timeline" className={`px-2 py-1.5 sm:py-2 text-xs sm:text-sm rounded-md
                    ${activeTab === "timeline" ? "bg-gray-100 text-gray-900" : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`}>
                  {renderTabLabel(<Activity className="h-4 w-4" />, "Timeline")}
                </TabsTrigger>
                <TabsTrigger value="performance" className={`px-2 py-1.5 sm:py-2 text-xs sm:text-sm rounded-md
                    ${activeTab === "performance" ? "bg-gray-100 text-gray-900" : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`}>
                  {renderTabLabel(<BarChart3 className="h-4 w-4" />, "Performance")}
                </TabsTrigger>
                <TabsTrigger value="disputes" className={`px-2 py-1.5 sm:py-2 text-xs sm:text-sm rounded-md
                    ${activeTab === "disputes" ? "bg-gray-100 text-gray-900" : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`}>
                  {renderTabLabel(<ShieldAlert className="h-4 w-4" />, "Disputes", 4)}
                </TabsTrigger>
              </TabsList>

              {!isMobile && (
                <Button variant="default" size="sm" className="bg-indigo-600 hover:bg-indigo-700">
                  <Download className="h-4 w-4 mr-2" />
                  Export Report
                </Button>
              )}
            </div>

            <CardContent className="pt-4 md:pt-6 px-2 md:px-6">
              <TabsContent value="overview" className="space-y-4 mt-0">
                <p className="text-sm text-muted-foreground mb-4">
                  Monitor all active jobs and their current status across your platform.
                </p>
                
                <JobsOverview />
              </TabsContent>

              <TabsContent value="bids" className="mt-0 space-y-4">
                <p className="text-sm text-muted-foreground mb-4">
                  Review and manage incoming provider bids for all open jobs.
                </p>
                
                <JobBidsList />
              </TabsContent>
              
              <TabsContent value="timeline" className="mt-0 space-y-4">
                <p className="text-sm text-muted-foreground mb-4">
                  Track job progress through different stages of completion.
                </p>
                
                <JobTimeline />
              </TabsContent>
              
              <TabsContent value="performance" className="mt-0 space-y-4">
                <p className="text-sm text-muted-foreground mb-4">
                  Analyze provider performance metrics and satisfaction ratings.
                </p>
                
                <ProviderPerformance />
              </TabsContent>

              <TabsContent value="disputes" className="mt-0 space-y-4">
                <p className="text-sm text-muted-foreground mb-4">
                  Mediate and resolve disputes between customers and service providers.
                </p>
                
                {isMobile ? (
                  <MobileDisputesList 
                    disputes={[...mockDisputes]} 
                    onViewDispute={handleViewDispute} 
                  />
                ) : (
                  <DisputesList onViewDispute={handleViewDispute} />
                )}
              </TabsContent>
            </CardContent>
          </Tabs>
        </CardHeader>
      </Card>

      {/* Mobile Export Button */}
      {isMobile && activeTab !== "disputes" && (
        <div className="fixed bottom-4 right-4 z-10">
          <Button 
            size="icon"
            className="rounded-full h-12 w-12 shadow-lg bg-indigo-600 hover:bg-indigo-700"
          >
            <Download className="h-5 w-5" />
          </Button>
        </div>
      )}
    </div>
  );
};

// Mock disputes data for the MobileDisputesList component
const mockDisputes: JobDispute[] = [
  {
    id: "disp-1",
    jobId: "job-123",
    title: "Incomplete service",
    description: "Provider left before completing all agreed services",
    status: "under_review",
    severity: "medium",
    initiator: "customer",
    initiatorName: "Jane Cooper",
    initiatorAvatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330",
    respondentName: "Alex Johnson",
    respondentAvatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e",
    createdAt: "2025-05-01T14:32:00Z",
    updatedAt: "2025-05-02T09:15:00Z",
    evidence: [{
      id: "ev-1",
      type: "image",
      submittedBy: "customer",
      submitterName: "Jane Cooper",
      description: "Unfinished bathroom tiling",
      url: "https://example.com/evidence1.jpg",
      timestamp: "2025-05-01T14:35:00Z"
    }, {
      id: "ev-2",
      type: "message",
      submittedBy: "provider",
      submitterName: "Alex Johnson",
      description: "Chat messages showing agreement to continue work next day",
      timestamp: "2025-05-01T15:00:00Z"
    }]
  }, {
    id: "disp-2",
    jobId: "job-456",
    title: "Payment dispute",
    description: "Provider claiming additional charges not agreed upon",
    status: "mediation",
    severity: "high",
    initiator: "provider",
    initiatorName: "Michael Wilson",
    initiatorAvatar: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7",
    respondentName: "Sarah Miller",
    respondentAvatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80",
    createdAt: "2025-04-28T10:22:00Z",
    updatedAt: "2025-05-03T11:30:00Z",
    evidence: [{
      id: "ev-3",
      type: "contract",
      submittedBy: "provider",
      submitterName: "Michael Wilson",
      description: "Original service contract",
      url: "https://example.com/contract.pdf",
      timestamp: "2025-04-28T10:25:00Z"
    }, {
      id: "ev-4",
      type: "document",
      submittedBy: "customer",
      submitterName: "Sarah Miller",
      description: "Email confirming fixed price",
      url: "https://example.com/email.pdf",
      timestamp: "2025-04-28T16:40:00Z"
    }],
    adminNotes: ["Customer showed evidence of fixed price agreement", "Provider claims additional work was performed beyond scope"]
  }, {
    id: "disp-3",
    jobId: "job-789",
    title: "Service quality issue",
    description: "Customer unhappy with quality of painting work",
    status: "resolved",
    severity: "low",
    initiator: "customer",
    initiatorName: "Robert Brown",
    initiatorAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e",
    respondentName: "Emily Davis",
    respondentAvatar: "https://images.unsplash.com/photo-**********-94ddf0286df2",
    createdAt: "2025-04-25T09:10:00Z",
    updatedAt: "2025-05-02T14:20:00Z",
    evidence: [{
      id: "ev-5",
      type: "image",
      submittedBy: "customer",
      submitterName: "Robert Brown",
      description: "Paint peeling after 2 days",
      url: "https://example.com/paint.jpg",
      timestamp: "2025-04-25T09:15:00Z"
    }],
    resolution: {
      type: "admin_decision",
      description: "Provider to redo the affected areas at no additional cost",
      actionTaken: [{
        type: "service_modification",
        description: "Provider agreed to repaint affected areas",
        appliedTo: "provider"
      }],
      adminId: "admin-1",
      adminName: "Admin User",
      resolvedAt: "2025-05-02T14:20:00Z"
    }
  }, {
    id: "disp-4",
    jobId: "job-101",
    title: "No-show provider",
    description: "Provider did not appear for scheduled appointment",
    status: "submitted",
    severity: "urgent",
    initiator: "customer",
    initiatorName: "Lisa Wang",
    initiatorAvatar: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f",
    respondentName: "David Smith",
    respondentAvatar: "https://images.unsplash.com/photo-1463453091185-61582044d556",
    createdAt: "2025-05-03T15:00:00Z",
    updatedAt: "2025-05-03T15:05:00Z",
    evidence: [{
      id: "ev-6",
      type: "document",
      submittedBy: "customer",
      submitterName: "Lisa Wang",
      description: "Appointment confirmation",
      url: "https://example.com/appointment.pdf",
      timestamp: "2025-05-03T15:02:00Z"
    }]
  }
];

export default AdminJobOversight;
