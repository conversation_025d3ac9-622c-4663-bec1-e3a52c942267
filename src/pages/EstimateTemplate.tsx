import React, { useState } from 'react';
import { Layout } from '@/components/Layout';
import { SEO } from '@/components/SEO';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  FileText, 
  Star, 
  Check, 
  Calculator,
  Eye,
  Edit3,
  Users,
  TrendingUp,
  Shield
} from 'lucide-react';
import { motion } from 'framer-motion';
import { PDFPreviewDialog } from '@/components/invoice-templates/PDFPreviewDialog';

interface TemplateCardProps {
  title: string;
  description: string;
  features: string[];
  downloadUrl: string;
  previewUrl?: string;
  popular?: boolean;
  category: string;
}

const TemplateCard: React.FC<TemplateCardProps> = ({
  title,
  description,
  features,
  downloadUrl,
  previewUrl,
  popular = false,
  category
}) => {
  const [showPreview, setShowPreview] = useState(false);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `${title.toLowerCase().replace(/\s+/g, '-')}-template.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <Card className="h-full transition-all hover:shadow-lg hover:scale-[1.02] relative overflow-hidden">
        {popular && (
          <div className="absolute top-4 right-4 z-10">
            <Badge className="bg-primary text-white">
              <Star className="h-3 w-3 mr-1" />
              Popular
            </Badge>
          </div>
        )}
        
        <CardHeader className="pb-4">
          <div className="flex items-center gap-2 mb-2">
            <FileText className="h-5 w-5 text-primary" />
            <span className="text-sm text-muted-foreground">{category}</span>
          </div>
          <CardTitle className="text-xl">{title}</CardTitle>
          <p className="text-muted-foreground">{description}</p>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="space-y-3">
            <h4 className="font-medium text-sm">What's included:</h4>
            <ul className="space-y-2">
              {features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2 text-sm">
                  <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex gap-2">
            <Button onClick={handleDownload} className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
            {previewUrl && (
              <Button variant="outline" size="sm" onClick={() => setShowPreview(true)}>
                <Eye className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {previewUrl && (
        <PDFPreviewDialog
          isOpen={showPreview}
          onClose={() => setShowPreview(false)}
          pdfUrl={previewUrl}
          title={title}
        />
      )}
    </>
  );
};

const EstimateTemplate: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const templates = [
    {
      title: "Standard Service Estimate",
      description: "Professional estimate template for general service work including labor, materials, and project details.",
      features: [
        "Professional header with business info",
        "Detailed line items for services",
        "Material and labor cost breakdown",
        "Terms and conditions section",
        "Customer approval signature area"
      ],
      downloadUrl: "/estimate-templates/standard-service-estimate.pdf",
      previewUrl: "/estimate-templates/standard-service-estimate.pdf",
      popular: true,
      category: "General"
    },
    {
      title: "Plumbing Service Estimate",
      description: "Specialized template for plumbing contractors with pipe, fixture, and labor specifications.",
      features: [
        "Plumbing-specific line items",
        "Pipe and fixture specifications",
        "Emergency service rates",
        "Warranty information",
        "Permit requirements section"
      ],
      downloadUrl: "/estimate-templates/plumbing-service-estimate.pdf",
      previewUrl: "/estimate-templates/plumbing-service-estimate.pdf",
      category: "Plumbing"
    },
    {
      title: "Electrical Work Estimate",
      description: "Template designed for electrical contractors with circuit, outlet, and safety specifications.",
      features: [
        "Electrical component breakdown",
        "Safety compliance notes",
        "Circuit specifications",
        "Code compliance section",
        "Installation timeline"
      ],
      downloadUrl: "/estimate-templates/electrical-service-estimate.pdf",
      previewUrl: "/estimate-templates/electrical-service-estimate.pdf",
      category: "Electrical"
    },
    {
      title: "Cleaning Service Estimate",
      description: "Comprehensive template for residential and commercial cleaning services.",
      features: [
        "Room-by-room breakdown",
        "Frequency options",
        "Special service add-ons",
        "Supply cost breakdown",
        "Recurring service discounts"
      ],
      downloadUrl: "/estimate-templates/cleaning-service-estimate.pdf",
      previewUrl: "/estimate-templates/cleaning-service-estimate.pdf",
      category: "Cleaning"
    },
    {
      title: "Landscaping Project Estimate",
      description: "Detailed template for landscaping and outdoor improvement projects.",
      features: [
        "Plant and material specifications",
        "Seasonal considerations",
        "Maintenance requirements",
        "Irrigation system costs",
        "Project timeline phases"
      ],
      downloadUrl: "/estimate-templates/landscaping-estimate.pdf",
      previewUrl: "/estimate-templates/landscaping-estimate.pdf",
      category: "Landscaping"
    },
    {
      title: "Construction Estimate",
      description: "Comprehensive template for construction and renovation projects.",
      features: [
        "Material quantity calculations",
        "Labor hour breakdowns",
        "Equipment rental costs",
        "Permit and inspection fees",
        "Change order procedures"
      ],
      downloadUrl: "/estimate-templates/construction-estimate.pdf",
      previewUrl: "/estimate-templates/construction-estimate.pdf",
      category: "Construction"
    }
  ];

  const categories = ['all', 'General', 'Plumbing', 'Electrical', 'Cleaning', 'Landscaping', 'Construction'];

  const filteredTemplates = selectedCategory === 'all' 
    ? templates 
    : templates.filter(template => template.category === selectedCategory);

  const benefits = [
    {
      icon: <Calculator className="h-6 w-6" />,
      title: "Accurate Pricing",
      description: "Ensure you include all costs and maintain healthy profit margins with structured pricing sections."
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: "Professional Image",
      description: "Impress clients with polished, branded estimates that build trust and credibility."
    },
    {
      icon: <TrendingUp className="h-6 w-6" />,
      title: "Win More Jobs",
      description: "Clear, detailed estimates help customers understand value and increase your close rate."
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: "Legal Protection",
      description: "Include proper terms, conditions, and scope to protect your business from disputes."
    }
  ];

  return (
    <Layout>
      <SEO
        title="Free Estimate Templates for Service Providers | Download Professional Templates"
        description="Download free, professional estimate templates for service providers. Includes templates for plumbing, electrical, cleaning, landscaping, and more. Win more jobs with professional estimates."
        canonicalUrl="/free-tools/estimate-template"
      />

      <div className="pt-28 pb-24 px-6 md:px-0">
        <div className="container mx-auto max-w-6xl">
          {/* Hero Section */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
              <FileText className="h-4 w-4" />
              Professional Templates
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold mb-6 text-gray-900 dark:text-white">
              Professional Estimate Templates
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-8 leading-relaxed">
              Download free, customizable estimate templates designed specifically for service providers. 
              Win more jobs with professional, detailed estimates.
            </p>

            <div className="flex flex-wrap justify-center gap-4">
              <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20 px-4 py-2 text-base">
                6 Professional Templates
              </Badge>
              <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20 px-4 py-2 text-base">
                Instant Download
              </Badge>
              <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20 px-4 py-2 text-base">
                Fully Customizable
              </Badge>
            </div>
          </motion.div>

          {/* Benefits Section */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-16"
          >
            <h2 className="text-3xl font-bold text-center mb-12">Why Professional Estimates Matter</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  className="text-center p-6 rounded-lg bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/10"
                >
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 text-primary rounded-lg mb-4">
                    {benefit.icon}
                  </div>
                  <h3 className="font-semibold mb-2">{benefit.title}</h3>
                  <p className="text-muted-foreground text-sm">{benefit.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Category Filter */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mb-8"
          >
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="capitalize"
                >
                  {category === 'all' ? 'All Templates' : category}
                </Button>
              ))}
            </div>
          </motion.div>

          {/* Templates Grid */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
            id="templates"
          >
            {filteredTemplates.map((template, index) => (
              <motion.div
                key={template.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
              >
                <TemplateCard {...template} />
              </motion.div>
            ))}
          </motion.div>

          {/* How to Use Section */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="bg-gradient-to-br from-primary/5 to-primary/10 rounded-2xl p-8 md:p-12 mb-16"
          >
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4">How to Use These Templates</h2>
              <p className="text-muted-foreground text-lg">
                Get professional estimates ready in minutes with our easy-to-customize templates.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary text-white rounded-lg mb-4 text-xl font-bold">
                  1
                </div>
                <h3 className="font-semibold mb-2">Download</h3>
                <p className="text-muted-foreground text-sm">
                  Choose and download the template that matches your service type.
                </p>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary text-white rounded-lg mb-4 text-xl font-bold">
                  2
                </div>
                <h3 className="font-semibold mb-2">Customize</h3>
                <p className="text-muted-foreground text-sm">
                  Add your business info, logo, and customize the content for your client.
                </p>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary text-white rounded-lg mb-4 text-xl font-bold">
                  3
                </div>
                <h3 className="font-semibold mb-2">Send</h3>
                <p className="text-muted-foreground text-sm">
                  Present your professional estimate to clients and win more jobs.
                </p>
              </div>
            </div>
          </motion.div>

          {/* CTA Section */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="text-center"
          >
            <h2 className="text-3xl font-bold mb-6">Ready to Create Professional Estimates?</h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join thousands of service providers who use our templates to create winning estimates and grow their business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild className="text-lg px-8 py-6 h-auto">
                <a href="#templates">
                  <Download className="h-5 w-5 mr-2" />
                  Download Templates
                </a>
              </Button>
              <Button variant="outline" size="lg" asChild className="text-lg px-8 py-6 h-auto">
                <a href="/free-tools">
                  <Edit3 className="h-5 w-5 mr-2" />
                  More Free Tools
                </a>
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default EstimateTemplate;
