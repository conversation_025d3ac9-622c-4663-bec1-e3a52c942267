import React, { useState, useEffect } from 'react';
import { ServicePageTemplate } from '../components/ServicePageTemplate';
import { ArrowRight, Check } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Link } from 'react-router-dom';
import { PaintBucket, ShieldCheck, Clock, Award, Handshake, Users, Sparkles, Trash, CheckCheck, Home, Briefcase, Building, FileInput, DollarSign, CalendarCheck, Brush, Wind, CloudRain, Building2, Hotel, Car, Shield } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { ServiceNeeds } from '@/components/ServiceNeeds';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { SEO } from '@/components/SEO';
import { fetchPosts } from '@/services/wordpressApi';
import {SERVICE_CATEGORY} from "@/types/enum.ts";

const CleaningService: React.FC = () => {
  const isMobile = useIsMobile();
  const [cleaningBlogPosts, setCleaningBlogPosts] = useState<BlogPost[]>([])
    useEffect(() => {
        const loadInitialData = async () => {
            try {
                const { posts, totalPages: pages } = await fetchPosts(1, 3, SERVICE_CATEGORY.CLEANING);
                setCleaningBlogPosts(posts)
            } catch (err) {
                console.log(err);
            }
        };
        loadInitialData();
    }, []);

  const benefits = [
    {
      title: "Trained & Vetted Cleaning Pros",
      description: "Our cleaning professionals undergo thorough background checks and training before joining our trusted network.",
      icon: <ShieldCheck className="h-6 w-6 text-primary" />
    }, {
      title: "Flexible Scheduling",
      description: "Book regular cleaning services or one-time deep cleans at times that work with your busy schedule.",
      icon: <Clock className="h-6 w-6 text-primary" />
    }, {
      title: "Eco-Friendly Options",
      description: "We offer green cleaning options using environmentally-friendly products that are safe for families, employees, and customers.",
      icon: <Award className="h-6 w-6 text-primary" />
    }, {
      title: "Satisfaction Guaranteed",
      description: "If you're not completely satisfied with our cleaning service, we'll make it right – guaranteed.",
      icon: <Handshake className="h-6 w-6 text-primary" />
    }, {
      title: "Consistent Quality",
      description: "Our detailed cleaning checklists ensure consistent, high-quality results every time.",
      icon: <Users className="h-6 w-6 text-primary" />
    }, {
      title: "Customized Cleaning Plans",
      description: "We tailor our cleaning services to your specific residential or commercial needs for the perfect clean.",
      icon: <PaintBucket className="h-6 w-6 text-primary" />
    }
  ];
  const faqs = [
    {
      question: "What types of properties do you clean?",
      answer: "We provide cleaning services for residential homes, apartments, offices, retail spaces, medical facilities, schools, and commercial buildings of all sizes. Our professional cleaners are trained to handle the unique requirements of each property type."
    }, {
      question: "How do you screen your cleaning professionals?",
      answer: "All cleaning professionals undergo comprehensive background checks, reference verification, and training before joining our network. We only work with experienced, reliable cleaners who meet our high standards."
    }, {
      question: "Do I need to provide cleaning supplies?",
      answer: "No, our cleaning professionals bring all necessary supplies and equipment. If you prefer specific products or have special requirements (like eco-friendly cleaners), just let us know when booking."
    }, {
      question: "What's included in a standard commercial cleaning?",
      answer: "Our standard commercial cleaning includes dusting, vacuuming, mopping, kitchen/break room cleaning, bathroom sanitization, trash removal, and general tidying. We can also create custom plans for specific business needs like conference room preparation or reception area detailing."
    }, {
      question: "What's the difference between regular cleaning and deep cleaning?",
      answer: "Regular cleaning maintains your property's cleanliness with standard tasks like dusting, vacuuming, and bathroom/kitchen cleaning. Deep cleaning is more intensive, including hard-to-reach areas, inside appliances, baseboards, ceiling fans, and other detailed work typically done less frequently."
    }, {
      question: "Can you accommodate after-hours cleaning for my business?",
      answer: "Yes, we understand that many businesses prefer cleaning services after business hours. We can arrange cleaning schedules outside of your operating hours to minimize disruption to your business operations."
    }
  ];
  const cleaningEstimates = [
    {
      tier: "Residential Cleaning",
      price: "$75-200",
      description: "For homes and apartments",
      features: ["Experienced cleaner", "Kitchen & bathroom cleaning", "Dusting & vacuuming", "Floor cleaning", "Bedrooms & living areas", "Eco-friendly options"]
    }, {
      tier: "Office Cleaning",
      price: "$120-350",
      description: "For businesses and workspaces",
      features: ["Professional cleaning team", "Workstations & common areas", "Break room sanitizing", "Restroom disinfection", "Trash removal & recycling", "Floor care & maintenance"],
      recommended: true
    }, {
      tier: "Commercial Deep Cleaning",
      price: "$300-1500+",
      description: "For all property types",
      features: ["Elite cleaning team", "High-traffic area treatment", "Carpet extraction cleaning", "Hard surface floor restoration", "Window & glass cleaning", "Disinfection protocols", "Custom cleaning solutions"]
    }
  ];
  const commonCleaningNeeds = [
    {
      icon: <Home className="h-8 w-8 text-primary" />,
      label: "Residential Cleaning"
    }, {
      icon: <Briefcase className="h-8 w-8 text-primary" />,
      label: "Office Cleaning"
    }, {
      icon: <Building className="h-8 w-8 text-primary" />,
      label: "Commercial Cleaning"
    }, {
      icon: <Brush className="h-8 w-8 text-primary" />,
      label: "Carpet Cleaning"
    }, {
      icon: <Clock className="h-8 w-8 text-primary" />,
      label: "Move-In/Move-Out"
    }, {
      icon: <Trash className="h-8 w-8 text-primary" />,
      label: "Post-Construction"
    }, {
      icon: <Building2 className="h-8 w-8 text-primary" />,
      label: "Medical Facilities"
    }, {
      icon: <Sparkles className="h-8 w-8 text-primary" />,
      label: "Deep Clean"
    }, {
      icon: <Wind className="h-8 w-8 text-primary" />,
      label: "Window Cleaning"
    }, {
      icon: <Hotel className="h-8 w-8 text-primary" />,
      label: "Hospitality"
    }
  ];

  return <>
      <SEO title="Book Reliable Cleaning Services – Compare Local Bids for Home & Office" description="Get top-rated cleaning pros for your home or office. Compare bids, book flexible plans, and enjoy eco-friendly, insured service—only on JobON." localBusinessSchema={true} serviceType="Cleaning" serviceSlug="cleaning" canonicalUrl='/services/cleaning' />

      <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-background pt-20 pb-8 md:pt-24 md:pb-12">
        <div className="container mx-auto px-4 lg:px-8">
          {isMobile ? <div className="w-full">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
                <div className="relative h-52 overflow-hidden">
                  <img src="/lovable-uploads/c78bdc9f-7294-4aa6-a0a1-4da41162b0b0.png" alt="Professional cleaning service" className="w-full h-full object-cover object-center" style={{objectPosition: "center 30%"}} />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent flex items-end">
                    <div className="p-3 text-white w-full">
                      <h1 className="text-2xl font-bold mb-1 text-left">
                        Professional Cleaning
                      </h1>
                      <h2 className="text-lg font-medium text-blue-300 mb-1 text-left">
                        <span className="block">Expert Service</span>
                        <span className="block">Spotless Results</span>
                      </h2>
                      <div className="flex items-center space-x-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((_, i) => <svg key={i} className="w-3 h-3 text-amber-400 fill-amber-400" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>)}
                        </div>
                        <span className="text-xs font-medium text-white">4.9/5 · 1,874 reviews</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-3">
                  <p className="text-sm text-gray-800 dark:text-gray-300 mb-3 font-medium">
                    Professional cleaning services for homes, offices, and commercial spaces
                  </p>

                  <div className="mb-3">
                    <div className="flex shadow-lg rounded-xl overflow-hidden">


                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Link to="/create-job" className="flex-1">
                      <Button variant="default" size="default" className="w-full rounded-md font-semibold text-sm py-1.5">
                        Post a Job
                      </Button>
                    </Link>
                    <Link to="/professionals/cleaning" className="flex-1">
                      <Button variant="outline" size="default" className="w-full rounded-md font-semibold text-sm py-1.5 text-gray-800 dark:text-white border-gray-400">
                        Browse Cleaners
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-2 mb-3 flex items-center justify-between">
                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-green-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Vetted
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">
                      Cleaners
                    </span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">Response</span>
                    <span className="font-bold text-xs text-black dark:text-white">&#60; 1hr</span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Award className="h-5 w-5 text-purple-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Satisfaction
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">Guaranteed</span>
                  </div>
                </div>
              </div>
            </div> : <div className="flex flex-col lg:flex-row gap-12 items-center">
              <div className="w-full lg:w-1/2 space-y-8">
                <div className="space-y-4">
                  <span className="inline-block bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-hover px-4 py-1.5 rounded-full font-medium text-sm">Professional Cleaning Services</span>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark-heading leading-tight">
                    Expert Cleaning Services,
                    <br className="hidden md:inline" />
                    <span className="text-primary mt-2 block dark:text-primary-hover dark:dark-mode-text-shadow">
                      For Every Space.
                    </span>
                  </h1>
                  <div className="text-xl font-semibold text-gray-700 dark-subheading">
                    <div className="flex flex-col space-y-3 text-left">
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Homes, Offices & Commercial Spaces</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Verified & Insured Professionals</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Customized Cleaning Plans</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Eco-Friendly Options Available</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Link to="/create-job" className="w-full sm:w-auto">
                    <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Post a Project
                      <ArrowRight size={20} className="ml-2" />
                    </Button>
                  </Link>
                  <Link to="/professionals/cleaning" className="w-full sm:w-auto">
                    <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Browse Cleaners
                    </Button>
                  </Link>
                </div>

                <div className="pt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Instant Quotes</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Verified Cleaners</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>100% Satisfaction</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Customized Plans</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Transparent Pricing</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Bonded & Insured</span>
                  </div>
                </div>
              </div>

              <div className="w-full lg:w-1/2 relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/5 rounded-xl filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/c78bdc9f-7294-4aa6-a0a1-4da41162b0b0.png" alt="Happy family enjoying clean home" className="w-full h-full object-cover" />
                    </div>
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/f9caeb3c-3a81-4977-9d76-cf7b704e0484.png" alt="Young family with baby in clean bedroom" className="w-full h-full object-cover" />
                    </div>
                    <div className="col-span-2 aspect-[16/9] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/952e738a-aa59-4fca-8062-a6104eae1953.png" alt="Office team meeting in clean workspace" className="w-full h-full object-cover" />
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <div className="flex justify-center mb-2">
                      <div className="flex text-amber-400">
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">4.9/5 (1,874 reviews)</span>
                    </div>
                    <h3 className="font-bold text-xl mb-1 dark:text-white">Find Top Local Cleaners</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Enter your ZIP code to get matched with professional cleaners in your area</p>

                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                      <div className="flex-1 flex items-center bg-gray-50 dark:bg-gray-700 p-4">
                        <input type="text" placeholder="Enter your ZIP code" className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-300 text-lg" />
                      </div>
                      <Button type="submit" className="px-8 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all">
                        <span>Search</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>}
        </div>
      </section>

      <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Common Professional Cleaning Services</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
              Select from our most requested cleaning services
            </p>
          </div>

          <ServiceNeeds serviceId="cleaning" needs={commonCleaningNeeds} estimates={cleaningEstimates} />
        </div>
      </section>

      <section className="py-8 md:py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Recently Completed Cleaning Projects</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Browse through recently completed cleaning jobs by our network of professionals
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&q=80" alt="Deep house cleaning" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">3 days ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Whole Home Deep Clean</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Complete deep cleaning of 4-bedroom home including baseboards, inside cabinets, and appliances.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Berkeley, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$350</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1521737852567-6949f3f9f2b5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80" alt="Office cleaning service" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">1 week ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Weekly Office Cleaning</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Comprehensive cleaning of startup office space including workstations, meeting rooms, and kitchen area.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Francisco, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$185/week</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1613665813446-82a78c468a1d?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80" alt="Commercial cleaning service" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 weeks ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Retail Store Deep Clean</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Post-renovation cleaning of 3,000 sq ft retail space including floors, fixtures, windows, and merchandise displays.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Jose, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$650</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Latest Cleaning Tips & Guides</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Expert advice and helpful resources for maintaining a clean and healthy environment
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {cleaningBlogPosts.map(post => <BlogCard key={post.id} post={post} />)}
          </div>

          <div className="text-center mt-8 md:mt-12">
            <Link to="/blog">
              <Button variant="default" size={isMobile ? "default" : "lg"} className="font-medium">
                View All Cleaning Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <ServicePageTemplate serviceId="cleaning" title="Professional Cleaning Services" subtitle="Get your home, office, or commercial space spotlessly clean by vetted professionals" description="From residential cleaning to office maintenance and commercial deep cleans, our reliable cleaning professionals deliver thorough, customized services to meet your needs." heroImage="https://images.unsplash.com/photo-1600623471616-8c1966c91ff9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1374&q=80" benefits={benefits} faqs={faqs} estimates={cleaningEstimates} commonNeeds={[]} // Emptied the commonNeeds since we're now displaying it separately
    hideEstimator={false} hideHero={true} professionalTitle="Cleaners" seoTitle="Book Reliable Cleaning Services | Get Local Bids" customCta={<div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/create-job" className="w-full sm:w-auto">
              <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Post a Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/professionals/cleaning" className="w-full sm:w-auto">
              <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Browse Cleaners
              </Button>
            </Link>
          </div>} />
    </>;
};
export default CleaningService;
