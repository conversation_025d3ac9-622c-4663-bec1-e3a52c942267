
import React, { useState, useEffect } from 'react';
import { ServicePageTemplate } from '../components/ServicePageTemplate';
import { ArrowRight, Check } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Link, useLocation } from 'react-router-dom';
import { ShieldCheck, Clock, Award, Handshake, Users, Shovel, Flower2, Cloud, Ruler, Scissors, Leaf, Sprout, Fence, Droplet, TreePine, LandPlot } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { ServiceNeeds } from '@/components/ServiceNeeds';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { motion } from 'framer-motion';
import { TextRotate } from '@/components/ui/text-rotate';
import { SEO } from '@/components/SEO';
import { fetchPosts } from '@/services/wordpressApi';
import {SERVICE_CATEGORY} from "@/types/enum.ts";

const LandscapingService: React.FC = () => {
  const isMobile = useIsMobile();
  const [landscapingBlogPosts, setLandscapingBlogPosts] = useState<BlogPost[]>([])
  const location = useLocation(); // Using the imported hook correctly

    useEffect(() => {
        const loadInitialData = async () => {
            try {
                const { posts, totalPages: pages } = await fetchPosts(1, 3, SERVICE_CATEGORY.LANDSCAPING);
                setLandscapingBlogPosts(posts)
            } catch (err) {
                console.log(err);
            }
        };
        loadInitialData();
    }, []);

  const benefits = [{
    title: "Licensed Landscapers",
    description: "Our landscaping professionals are fully licensed, insured, and trained to handle all your outdoor needs safely.",
    icon: <ShieldCheck className="h-6 w-6 text-primary" />
  }, {
    title: "Flexible Schedule",
    description: "Book regular maintenance services or one-time landscaping projects at times that work with your schedule.",
    icon: <Clock className="h-6 w-6 text-primary" />
  }, {
    title: "Eco-Friendly Options",
    description: "We offer sustainable landscaping options using environmentally-friendly practices and native plants.",
    icon: <Award className="h-6 w-6 text-primary" />
  }, {
    title: "100% Guaranteed",
    description: "If you're not completely satisfied with our landscaping service, we'll make it right – guaranteed.",
    icon: <Handshake className="h-6 w-6 text-primary" />
  }, {
    title: "Expert Gardeners",
    description: "Our team has years of experience designing and maintaining beautiful outdoor spaces for all types of properties.",
    icon: <Users className="h-6 w-6 text-primary" />
  }, {
    title: "Custom Designs",
    description: "We tailor our landscaping services to your specific needs and preferences for the perfect outdoor space.",
    icon: <TreePine className="h-6 w-6 text-primary" />
  }];
  const faqs = [{
    question: "What's included in standard lawn maintenance?",
    answer: "Our standard lawn maintenance includes mowing, edging, trimming, blowing, and cleanup. We can customize service frequency to suit your lawn's growth rate and your specific needs."
  }, {
    question: "How do you screen your landscaping professionals?",
    answer: "All landscaping professionals undergo comprehensive background checks, reference verification, and training before joining our network. We only work with experienced, reliable landscapers who meet our high standards."
  }, {
    question: "Do I need to provide any equipment or supplies?",
    answer: "No, our landscaping professionals bring all necessary equipment and supplies. If you prefer specific products or have special requirements (like organic fertilizers), just let us know when booking."
  }, {
    question: "How often should I schedule lawn maintenance?",
    answer: "During peak growing season (spring and summer), we recommend weekly or bi-weekly service. During fall and winter, every 3-4 weeks is typically sufficient, depending on your location and lawn type."
  }, {
    question: "What's the difference between landscaping and lawn care?",
    answer: "Lawn care focuses specifically on maintaining your grass through mowing, fertilization, aeration, and weed control. Landscaping is more comprehensive, including design, planting, hardscaping, irrigation systems, and overall outdoor space management."
  }, {
    question: "What if I'm not satisfied with the service?",
    answer: "Your satisfaction is guaranteed. If you're not completely happy with any aspect of our service, contact us within 24 hours and we'll arrange to rectify the issue at no additional cost."
  }];
  const landscapingEstimates = [{
    tier: "Basic Landscaping",
    price: "$80-150",
    description: "For residential properties",
    features: ["Experienced landscaper", "Lawn mowing & edging", "Basic garden maintenance", "Leaf removal", "Seasonal clean-up", "Organic options"]
  }, {
    tier: "Landscape Design",
    price: "$150-400",
    description: "For all property types",
    features: ["Professional landscaping team", "Custom landscape design", "Plant selection & installation", "Mulching & fertilizing", "Irrigation setup", "Sustainable practices"],
    recommended: true
  }, {
    tier: "Complete Transformation",
    price: "$500-2500+",
    description: "For comprehensive projects",
    features: ["Master landscaping team", "Full property assessment", "Hardscaping elements", "Water feature installation", "Lighting design", "Multi-season planning", "Long-term maintenance plans"]
  }];
  const commonLandscapingNeeds = [{
    icon: <Shovel className="h-8 w-8 text-primary" />,
    label: "Lawn Maintenance"
  }, {
    icon: <Scissors className="h-8 w-8 text-primary" />,
    label: "Tree & Shrub Pruning"
  }, {
    icon: <Flower2 className="h-8 w-8 text-primary" />,
    label: "Garden Design"
  }, {
    icon: <Sprout className="h-8 w-8 text-primary" />,
    label: "Planting Services"
  }, {
    icon: <Droplet className="h-8 w-8 text-primary" />,
    label: "Irrigation Systems"
  }, {
    icon: <Leaf className="h-8 w-8 text-primary" />,
    label: "Mulching & Fertilizing"
  }, {
    icon: <Fence className="h-8 w-8 text-primary" />,
    label: "Hardscaping"
  }, {
    icon: <LandPlot className="h-8 w-8 text-primary" />,
    label: "Land Grading"
  }, {
    icon: <TreePine className="h-8 w-8 text-primary" />,
    label: "Tree Installation"
  }, {
    icon: <Cloud className="h-8 w-8 text-primary" />,
    label: "Yard Clean-up"
  }];

  const rotatingWords = ["Beautiful", "Professional", "Expert", "Custom", "Premium", "Eco-Friendly"];
  return (
      <>
      <SEO title="Landscaping Services Near You – Compare Lawn & Yard Bids Instantly" description="Transform your yard with help from trusted landscapers near you. Compare quotes, schedule custom designs, and enjoy eco-friendly lawn care—only on JobON." localBusinessSchema={true} serviceType="Landscaping" serviceSlug="landscaping" canonicalUrl="/services/landscaping" />

      <section className="relative bg-gradient-to-b from-green-50 to-white dark:from-gray-900 dark:to-background pt-16 pb-4 md:pt-24 md:pb-12">
        <div className="container mx-auto px-3 lg:px-8">
          {isMobile ? <div className="w-full">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-3">
                <div className="relative h-52 overflow-hidden">
                  <img src="/lovable-uploads/0881985f-c1bf-469c-aee0-13b6131ff75b.png" alt="Professional landscaping service" className="w-full h-full object-cover object-center" style={{
                    objectPosition: "center 30%"
                  }} />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent flex items-end">
                    <div className="p-3 text-white w-full">
                      <h1 className="text-2xl font-bold mb-0.5 text-left">
                        Professional Landscaping
                      </h1>
                      <h2 className="text-lg font-medium text-blue-300 mb-1 text-left">
                        <span className="block">Expert Service</span>
                        <span className="block">Beautiful Results</span>
                      </h2>
                      <div className="flex items-center space-x-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((_, i) => <svg key={i} className="w-3 h-3 text-amber-400 fill-amber-400" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>)}
                        </div>
                        <span className="text-xs font-medium text-white">4.9/5 · 1,243 reviews</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-3">
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                    Professional landscaping services for homes, businesses, and commercial properties
                  </p>

                  <div className="flex space-x-2">
                    <Link to="/create-job" state={{ from: location.pathname }} className="flex-1">
                      <Button variant="default" size="default" className="w-full rounded-md font-semibold text-sm py-1.5">Post a Job</Button>
                    </Link>
                    <Link to="/professionals/landscaping" className="flex-1">
                      <Button variant="outline" size="default" className="w-full rounded-md font-semibold text-sm py-1.5 text-gray-800 dark:text-white border-gray-400">
                        Browse Landscapers
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-2 mb-3 flex items-center justify-between">
                <div className="flex items-center">
                  <ShieldCheck className="h-5 w-5 text-green-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-500 dark:text-gray-400">
                      Verified
                    </span>
                    <span className="font-bold text-xs">
                      Landscapers
                    </span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-500 dark:text-gray-400">Response</span>
                    <span className="font-bold text-xs">&#60; 1hr</span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Award className="h-5 w-5 text-purple-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-500 dark:text-gray-400">
                      Satisfaction
                    </span>
                    <span className="font-bold text-xs">Guaranteed</span>
                  </div>
                </div>
              </div>
            </div> : <div className="flex flex-col lg:flex-row gap-12 items-center">
              <div className="w-full lg:w-1/2 space-y-8">
                <div className="space-y-4">
                  <span className="inline-block bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-hover px-4 py-1.5 rounded-full font-medium text-sm">Professional Landscaping Services</span>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark-heading leading-tight">
                    <motion.div className="flex flex-wrap">
                      <TextRotate texts={rotatingWords} mainClassName="overflow-hidden text-primary mr-3 dark:text-primary-hover dark:dark-mode-text-shadow" staggerDuration={0.03} staggerFrom="last" rotationInterval={3000} transition={{
                    type: "spring",
                    damping: 30,
                    stiffness: 400
                  }} />
                      <span>Landscaping</span>
                    </motion.div>
                    <span className="block mt-2">
                      For Your Outdoor Space.
                    </span>
                  </h1>
                  <div className="text-xl font-semibold text-gray-700 dark-subheading">
                    <div className="flex flex-col space-y-3 text-left">
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Residential & Commercial Properties</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Licensed & Insured Professionals</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Custom Landscape Designs</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Eco-Friendly Options Available</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Link to="/create-job" state={{ from: location.pathname }} className="w-full sm:w-auto">
                    <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Post a Project
                      <ArrowRight size={20} className="ml-2" />
                    </Button>
                  </Link>
                  <Link to="/professionals/landscaping" className="w-full sm:w-auto">
                    <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Find Landscapers
                    </Button>
                  </Link>
                </div>

                <div className="pt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Instant Quotes</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Verified Landscapers</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>100% Satisfaction</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Custom Designs</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Transparent Pricing</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Bonded & Insured</span>
                  </div>
                </div>
              </div>

              <div className="w-full lg:w-1/2 relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/5 rounded-xl filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/4ba5baaf-1e19-48d9-b58d-7f193cb41c05.png" alt="Beautifully landscaped garden with flowers" className="w-full h-full object-cover" />
                    </div>
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/805b036b-e105-4438-9d5b-003bf76fce85.png" alt="Family enjoying their backyard lawn" className="w-full h-full object-cover" />
                    </div>
                    <div className="col-span-2 aspect-[16/9] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/0881985f-c1bf-469c-aee0-13b6131ff75b.png" alt="Professional landscaper working in garden" className="w-full h-full object-cover" />
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <div className="flex justify-center mb-2">
                      <div className="flex text-amber-400">
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">4.9/5 (1,243 reviews)</span>
                    </div>
                    <h3 className="font-bold text-xl mb-1 dark:text-white">Find Top Local Landscapers</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Enter your ZIP code to get matched with professional landscapers in your area</p>

                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                      <div className="flex-1 flex items-center bg-gray-50 dark:bg-gray-700 p-4">
                        <input type="text" placeholder="Enter your ZIP code" className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-300 text-lg" />
                      </div>
                      <Button type="submit" className="px-8 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all">
                        <span>Search</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>}
        </div>
      </section>

      <section className="py-6 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-1.5 md:mb-3 text-black dark:text-white">Common Professional Landscaping Services</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
              Select from our most requested landscaping services
            </p>
          </div>

          <ServiceNeeds serviceId="landscaping" needs={commonLandscapingNeeds} estimates={landscapingEstimates} />
        </div>
      </section>

      <section className="py-8 md:py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Recently Completed Landscaping Projects</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Browse through recently completed landscaping jobs by our network of professionals
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1558904541-efa843a96f01?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1771&q=80" alt="Backyard landscaping project" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">5 days ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Backyard Transformation</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Complete redesign of backyard including new lawn, garden beds, stone pathway, and irrigation system.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Palo Alto, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$3,800</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1769&q=80" alt="Front yard landscaping" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 weeks ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Front Yard Makeover</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Front yard redesign with drought-resistant plants, decorative rocks, and automatic sprinkler system.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Francisco, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$2,450</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1512249167167-32fb4f8b17f7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80" alt="Commercial landscaping" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">3 weeks ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Commercial Property Maintenance</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Ongoing maintenance for office complex including lawn care, pruning, seasonal planting, and irrigation upkeep.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Jose, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$750/month</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Latest Landscaping Tips & Guides</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Expert advice and helpful resources for maintaining beautiful outdoor spaces
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
            {landscapingBlogPosts.slice(0, isMobile ? 4 : 8).map(post => <div key={post.id} className="h-full">
                <BlogCard post={post} compact={true} />
              </div>)}
          </div>

          <div className="text-center mt-8 md:mt-12">
            <Link to="/blog">
              <Button variant="default" size={isMobile ? "default" : "lg"} className="font-medium">
                View All Landscaping Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <ServicePageTemplate 
        serviceId="landscaping" 
        title="Professional Landscaping Services" 
        subtitle="Transform your outdoor space with expert landscaping solutions" 
        description="From lawn care and garden design to hardscaping and outdoor living areas, our experienced landscapers create beautiful, functional outdoor spaces." 
        heroImage="https://images.unsplash.com/photo-1600608524888-131956781a86?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2030&q=80" 
        benefits={benefits} 
        faqs={faqs} 
        estimates={landscapingEstimates} 
        commonNeeds={[]} 
        hideEstimator={false} 
        hideHero={true} 
        professionalTitle="Landscapers" 
        seoTitle="Landscaping Services Near You | Lawn & Yard Bids Fast" 
        customCta={
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/create-job" state={{ from: location.pathname }} className="w-full sm:w-auto">
              <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Post a Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/professionals/landscaping" className="w-full sm:w-auto">
              <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Find Landscapers
              </Button>
            </Link>
          </div>
        } 
      />
      </>
  );
};

export default LandscapingService;
