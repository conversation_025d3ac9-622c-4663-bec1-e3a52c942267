
import React from 'react';
import { Layout } from '@/components/Layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Link } from 'react-router-dom';
import { RocketIcon, Target, UsersIcon, Zap, Award, ArrowRight, Flame, Brain } from 'lucide-react';
import { SEO } from '@/components/SEO';

const AboutUs = () => {
  return (
    <Layout>
      <SEO 
        title="About Us - Building the Future of Home Services" 
        description="We're a driven team of innovators reshaping the home services industry. Join us in our mission to create exceptional experiences for homeowners and service providers."
      />
      
      {/* Hero Section */}
      <section className="relative pt-32 pb-20">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-white to-primary/10 dark:from-primary/10 dark:via-background dark:to-primary/5"></div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Building the Future of <span className="text-primary">Home Services</span>
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground mb-8">
              We're not just another platform – we're a team of relentless innovators 
              driven by the vision of transforming how people connect with and manage home services.
            </p>
            <div className="flex flex-wrap gap-4 justify-center">
              <Link to="/careers">
                <Button size="lg" className="gap-2">
                  Join Our Team <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
              <Link to="/how-it-works">
                <Button variant="outline" size="lg">
                  See How It Works
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-white dark:bg-gray-800/50">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Our Mission</h2>
              <p className="text-lg text-muted-foreground mb-6">
                We're building more than just a platform – we're creating a revolution 
                in how homeowners and service providers connect, communicate, and collaborate.
              </p>
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <Target className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">High Standards</h3>
                    <p className="text-muted-foreground">
                      We set ambitious goals and push ourselves beyond conventional limits.
                      Mediocrity is not in our vocabulary.
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <Brain className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">Continuous Learning</h3>
                    <p className="text-muted-foreground">
                      We embrace failures as learning opportunities. Every setback is a setup 
                      for a stronger comeback.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/5 rounded-2xl filter blur-xl"></div>
              <Card className="relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden">
                <CardContent className="p-8">
                  <div className="aspect-video rounded-lg overflow-hidden mb-6">
                    <img 
                      src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?auto=format&fit=crop&w=800&q=80" 
                      alt="Team collaboration" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary mb-1">24/7</div>
                      <p className="text-sm text-muted-foreground">Dedication</p>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary mb-1">100%</div>
                      <p className="text-sm text-muted-foreground">Commitment</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Our Core Values</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              These principles guide everything we do – from how we build our product 
              to how we interact with our users and each other.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white dark:bg-gray-800">
              <CardContent className="p-8">
                <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  <Flame className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Intensity</h3>
                <p className="text-muted-foreground">
                  We're not your typical 9-to-5 team. We're driven by passion and the 
                  desire to create something extraordinary, whatever it takes.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800">
              <CardContent className="p-8">
                <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  <RocketIcon className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Innovation</h3>
                <p className="text-muted-foreground">
                  We challenge conventions and push boundaries. Status quo is not in our DNA – 
                  we're here to revolutionize the industry.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800">
              <CardContent className="p-8">
                <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  <Award className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Excellence</h3>
                <p className="text-muted-foreground">
                  Good is not enough. We strive for excellence in everything we do, 
                  from code quality to user experience.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-primary/5 dark:bg-gray-800/50">
        <div className="container mx-auto px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Ready to Make an Impact?</h2>
            <p className="text-lg text-muted-foreground mb-8">
              We're always looking for passionate individuals who share our vision and aren't 
              afraid of challenging the status quo. If you're ready to push boundaries and 
              make a real difference, we want to hear from you.
            </p>
            <Link to="/careers">
              <Button size="lg" className="gap-2">
                Join Our Team <Zap className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default AboutUs;
