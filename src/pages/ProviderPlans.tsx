
import React, { useState } from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { SubscriptionPlanCard } from '@/components/provider/subscription/SubscriptionPlanCard';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';

const ProviderPlans = () => {
  const { toast } = useToast();
  const [currentPlan, setCurrentPlan] = useState('free'); // This would come from user data
  
  // Mock checkout function - in a real app, this would call your Stripe integration
  const handleCheckout = (planId: string) => {
    toast({
      title: "Checkout initiated",
      description: `Processing checkout for ${planId} plan. This would redirect to payment in a real app.`,
    });
    
    // Mock successful payment after 2 seconds
    setTimeout(() => {
      setCurrentPlan(planId);
      toast({
        title: "Subscription updated!",
        description: `You are now subscribed to the ${planId} plan.`,
        variant: "default", // Changed from "success" to "default"
      });
    }, 2000);
  };
  
  return (
    <ProviderDashboardLayout pageTitle="Plans & Pricing">
      <div className="space-y-6">
        {/* Introduction card */}
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-0 dark:from-blue-950/20 dark:to-indigo-950/20">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                <CheckCircle className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold mb-2">Choose the right plan for your business</h2>
                <p className="text-muted-foreground">
                  Upgrade to unlock more leads, lower commission rates, and premium features to grow your business.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Current plan indicator */}
        {currentPlan !== 'free' && (
          <div className="flex items-center p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3">
              <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="font-medium">
                You are currently on the {' '}
                <span className="font-semibold text-green-700 dark:text-green-400">
                  {currentPlan === 'pro' ? 'Pro' : 'Elite'} Plan
                </span>
              </p>
              <p className="text-sm text-muted-foreground">
                Your subscription renews on May 21, 2025. You can manage billing details below.
              </p>
            </div>
          </div>
        )}
        
        {/* Subscription plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <SubscriptionPlanCard
            title="Starter"
            price="Free"
            description="Get started with the basic tools you need"
            commission="20%"
            isCurrentPlan={currentPlan === 'free'}
            features={[
              { included: true, text: "Access to bidding jobs" },
              { included: true, text: "Basic job alerts" },
              { included: true, text: "Standard visibility" },
              { included: true, text: "Basic dashboard analytics" },
              { included: true, text: "Community support" },
              { included: false, text: "Priority access to leads" },
              { included: false, text: "Advanced insights dashboard" },
              { included: false, text: "BNPL (Buy Now Pay Later)" },
            ]}
            ctaText="Current Plan"
            onSelect={() => {}}
            disabled={currentPlan === 'free'}
          />
          
          <SubscriptionPlanCard
            title="Pro"
            price="$159"
            description="Grow your business with more leads & tools"
            commission="15%"
            isCurrentPlan={currentPlan === 'pro'}
            isPopular={true}
            features={[
              { included: true, text: "Access to bidding jobs" },
              { included: true, text: "Priority access to leads" },
              { included: true, text: "Instant alerts" },
              { included: true, text: "Boosted visibility" },
              { included: true, text: "Advanced insights dashboard" },
              { included: true, text: "Add-on for BNPL" },
              { included: true, text: "Email support" },
              { included: false, text: "Premium featured placement" },
              { included: false, text: "Full business tools" },
            ]}
            ctaText={currentPlan === 'free' ? 'Upgrade to Pro' : currentPlan === 'pro' ? 'Current Plan' : 'Downgrade to Pro'}
            onSelect={() => handleCheckout('pro')}
            disabled={currentPlan === 'pro'}
          />
          
          <SubscriptionPlanCard
            title="Elite"
            price="$319"
            description="Maximize your business with premium tools"
            commission="12%"
            isCurrentPlan={currentPlan === 'elite'}
            features={[
              { included: true, text: "Access to bidding jobs" },
              { included: true, text: "Top placement for leads" },
              { included: true, text: "Real-time priority alerts" },
              { included: true, text: "Premium featured placement" },
              { included: true, text: "Full business tools" },
              { included: true, text: "CRM and client management" },
              { included: true, text: "BNPL included" },
              { included: true, text: "Priority support" },
            ]}
            ctaText={currentPlan === 'elite' ? 'Current Plan' : 'Upgrade to Elite'}
            onSelect={() => handleCheckout('elite')}
            disabled={currentPlan === 'elite'}
            highlighted={true}
          />
        </div>
        
        {/* FAQ and Support */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <Info className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Need help choosing?</h3>
                <p className="text-muted-foreground mb-4">
                  Contact our team for personalized guidance on selecting the right plan for your business needs.
                </p>
                <Button variant="outline">Contact Support</Button>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Billing Management Section */}
        <div className="space-y-4 mt-8">
          <h2 className="text-xl font-semibold">Billing Management</h2>
          <Card>
            <CardContent className="p-6 space-y-4">
              <div className="flex justify-between items-center border-b pb-4">
                <div>
                  <h3 className="font-medium">Payment Method</h3>
                  {currentPlan !== 'free' ? (
                    <p className="text-sm text-muted-foreground">Visa ending in 4242</p>
                  ) : (
                    <p className="text-sm text-muted-foreground">No payment method on file</p>
                  )}
                </div>
                <Button variant="outline" size="sm">Update</Button>
              </div>
              
              <div className="border-b pb-4">
                <h3 className="font-medium mb-3">Billing History</h3>
                {currentPlan !== 'free' ? (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Apr 21, 2025</span>
                      <span>{currentPlan === 'pro' ? '$159.00' : '$319.00'}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Mar 21, 2025</span>
                      <span>{currentPlan === 'pro' ? '$159.00' : '$319.00'}</span>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No billing history available</p>
                )}
              </div>
              
              {currentPlan !== 'free' && (
                <div className="pt-2">
                  <Button 
                    variant="outline" 
                    className="text-red-500 hover:text-red-600 hover:bg-red-50"
                    onClick={() => {
                      toast({
                        title: "Subscription canceled",
                        description: "Your subscription has been canceled. You will have access until the end of your current billing period.",
                      });
                      setTimeout(() => setCurrentPlan('free'), 1000);
                    }}
                  >
                    Cancel Subscription
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </ProviderDashboardLayout>
  );
};

export default ProviderPlans;
