import React, { useState } from 'react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Download, Eye, FileText, CheckCircle2, Settings } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { PDFPreviewDialog } from '@/components/invoice-templates/PDFPreviewDialog';

interface TemplateCardProps {
  title: string;
  imageSrc: string;
  description: string;
  comingSoon?: boolean;
  popular?: boolean;
  price?: number;
  isPaid?: boolean;
  colorScheme?: string;
  pdfUrl?: string;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ 
  title, 
  imageSrc, 
  description, 
  comingSoon = false, 
  popular = false,
  price = 0,
  isPaid = false,
  colorScheme = "bg-gradient-to-br from-gray-100 to-gray-200",
  pdfUrl = ""
}) => {
  // Whether to show pricing
  const showPrice = price > 0 && isPaid;
  const [pdfPreviewOpen, setPdfPreviewOpen] = useState(false);
  
  const handlePreview = () => {
    if (pdfUrl) {
      setPdfPreviewOpen(true);
    } else {
      toast.error("Preview not available", { 
        description: "This template preview is not available yet." 
      });
    }
  };

  const handleDownload = () => {
    if (showPrice) {
      toast.info("Premium Template", {
        description: "This is a premium template. Please purchase to download."
      });
      return;
    }

    if (pdfUrl) {
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = `${title.toLowerCase().replace(/\s+/g, '-')}-invoice-template.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success("Download started", {
        description: `${title} template is being downloaded.`
      });
    } else {
      toast.error("Download not available", {
        description: "This template is not available for download yet."
      });
    }
  };
  
  return (
    <>
      <Card className="overflow-hidden border-2 transition-all hover:shadow-md">
        <div className="relative">
          <div className={`aspect-[4/5] bg-muted overflow-hidden ${colorScheme}`}>
            <div className="p-8 h-full flex items-center justify-center">
              <div className="bg-white w-full max-w-[85%] h-[85%] shadow-lg rounded-md p-4 flex flex-col">
                <div className="mb-2 flex justify-between">
                  <div className="w-1/3 h-6 bg-gray-200 rounded"></div>
                  <div className="w-1/4 h-6 bg-gray-800 rounded"></div>
                </div>
                <div className="w-full h-4 bg-gray-100 mb-3 rounded"></div>
                <div className="w-2/3 h-4 bg-gray-100 mb-6 rounded"></div>
                <div className="flex justify-between mb-4">
                  <div className="w-1/2 h-4 bg-gray-200 rounded"></div>
                  <div className="w-1/4 h-4 bg-gray-200 rounded"></div>
                </div>
                <div className="w-full h-[1px] bg-gray-300 my-2"></div>
                <div className="flex justify-between mb-2">
                  <div className="w-2/3 h-3 bg-gray-100 rounded"></div>
                  <div className="w-1/5 h-3 bg-gray-100 rounded"></div>
                </div>
                <div className="flex justify-between mb-2">
                  <div className="w-2/3 h-3 bg-gray-100 rounded"></div>
                  <div className="w-1/5 h-3 bg-gray-100 rounded"></div>
                </div>
                <div className="flex justify-between mb-2">
                  <div className="w-2/3 h-3 bg-gray-100 rounded"></div>
                  <div className="w-1/5 h-3 bg-gray-100 rounded"></div>
                </div>
                <div className="w-full h-[1px] bg-gray-300 my-2"></div>
                <div className="mt-auto flex justify-between">
                  <div className="w-1/3 h-4 bg-gray-800 rounded"></div>
                  <div className="w-1/4 h-4 bg-gray-800 rounded"></div>
                </div>
              </div>
            </div>
          </div>
          {popular && (
            <Badge variant="default" className="absolute top-3 right-3 bg-primary">
              Popular
            </Badge>
          )}
          {showPrice && (
            <Badge variant="default" className="absolute top-3 left-3 bg-green-600">
              ${price.toFixed(2)}
            </Badge>
          )}
        </div>
        <CardHeader className="pb-2">
          <CardTitle className="text-xl">{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-foreground/80 text-sm leading-relaxed">{description}</p>
        </CardContent>
        <CardFooter className="flex justify-between gap-3 pt-2">
          {comingSoon ? (
            <Button className="w-full" disabled>Coming Soon</Button>
          ) : (
            <>
              <Button variant="outline" className="flex-1" onClick={handlePreview}>
                <Eye className="mr-2 h-4 w-4" /> Preview
              </Button>
              <Button className="flex-1" onClick={handleDownload}>
                {showPrice ? "Purchase" : <><Download className="mr-2 h-4 w-4" /> Download</>}
              </Button>
            </>
          )}
        </CardFooter>
      </Card>

      {pdfUrl && (
        <PDFPreviewDialog 
          isOpen={pdfPreviewOpen} 
          onClose={() => setPdfPreviewOpen(false)} 
          pdfUrl={pdfUrl} 
          title={title} 
        />
      )}
    </>
  );
};

const InvoiceTemplates: React.FC = () => {
  // Mock PDF URLs - in a real app, these would come from your API or file storage
  const pdfUrls = {
    standard: "/estimate-templates/standard-service-estimate.pdf",
    construction: "/estimate-templates/construction-estimate.pdf",
    landscaping: "/estimate-templates/landscaping-estimate.pdf",
    cleaning: "/estimate-templates/cleaning-service-estimate.pdf",
    plumbing: "/estimate-templates/plumbing-service-estimate.pdf",
    electrical: "/estimate-templates/electrical-service-estimate.pdf",
  };

  // State for pricing settings
  const [showPricing, setShowPricing] = useState<boolean>(false);
  const [templatePrices, setTemplatePrices] = useState({
    standard: 0,
    construction: 0,
    landscaping: 0,
    cleaning: 0,
    plumbing: 15, // Example priced template
    electrical: 15, // Example priced template
  });

  const handleSaveSettings = () => {
    toast.success("Template settings updated!", {
      description: "Your pricing configuration has been saved."
    });
  };

  return (
    <Layout>
      <div className="pt-24 pb-20 px-6 md:px-12">
        <div className="container mx-auto max-w-6xl">
          {/* Header with back button */}
          <div className="mb-6">
            <Link to="/free-tools" className="inline-flex items-center text-primary hover:underline mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Free Tools
            </Link>
          
            {/* Page Title with Settings Button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl md:text-4xl font-bold mt-2 mb-3">Professional Invoice Templates</h1>
                <p className="text-lg text-foreground/80 mb-2 max-w-3xl">
                  Download free, customizable invoice templates for your service business. Available in multiple formats including Word, Excel, and PDF.
                </p>
              </div>
              
              <Dialog>
                <DialogTrigger asChild>
                  <Button 
                    size="lg" 
                    className="gap-2 shadow-md transition-all"
                    variant="pricing"
                  >
                    <Settings className="h-5 w-5" />
                    Template Settings
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Template Settings</DialogTitle>
                    <DialogDescription>
                      Configure pricing and availability for templates.
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="py-4 space-y-5">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="enablePricing" 
                        checked={showPricing}
                        onCheckedChange={(checked) => setShowPricing(!!checked)} 
                      />
                      <Label htmlFor="enablePricing">Enable Premium Templates</Label>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-3">
                      <h3 className="font-medium">Template Pricing ($)</h3>
                      <p className="text-sm text-muted-foreground">
                        Set prices for each template. Enter 0 to keep templates free.
                      </p>
                      
                      <div className="grid grid-cols-2 gap-3">
                        {Object.entries(templatePrices).map(([template, price]) => (
                          <div key={template} className="flex items-center justify-between gap-3">
                            <Label htmlFor={`price-${template}`} className="capitalize whitespace-nowrap">
                              {template}:
                            </Label>
                            <div className="flex items-center">
                              <span className="mr-2">$</span>
                              <input
                                id={`price-${template}`}
                                type="number"
                                className="w-16 h-8 rounded border border-input px-2 text-right"
                                value={price}
                                onChange={(e) => setTemplatePrices(prev => ({
                                  ...prev,
                                  [template]: parseFloat(e.target.value) || 0
                                }))}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <DialogFooter>
                    <Button onClick={handleSaveSettings}>Save Changes</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Format filters */}
          <div className="flex flex-wrap gap-2 mb-8">
            <Badge variant="outline" className="px-3 py-1 text-sm cursor-pointer bg-primary/5 hover:bg-primary/10">
              All Formats
            </Badge>
            <Badge variant="outline" className="px-3 py-1 text-sm cursor-pointer hover:bg-primary/10">
              Word
            </Badge>
            <Badge variant="outline" className="px-3 py-1 text-sm cursor-pointer hover:bg-primary/10">
              Excel
            </Badge>
            <Badge variant="outline" className="px-3 py-1 text-sm cursor-pointer hover:bg-primary/10">
              PDF
            </Badge>
            <Badge variant="outline" className="px-3 py-1 text-sm cursor-pointer hover:bg-primary/10">
              Google Docs
            </Badge>
          </div>

          {/* Templates Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <TemplateCard 
              title="Standard Service Invoice" 
              imageSrc="/placeholder.svg"
              description="A clean, professional invoice template for all service businesses. Includes itemized services, taxes, and payment terms."
              popular={true}
              price={templatePrices.standard}
              isPaid={showPricing}
              colorScheme="bg-gradient-to-br from-blue-50 to-blue-100"
              pdfUrl={pdfUrls.standard}
            />
            <TemplateCard 
              title="Detailed Construction Invoice" 
              imageSrc="/placeholder.svg"
              description="Perfect for contractors with detailed line items for materials, labor, and equipment costs."
              popular={true}
              price={templatePrices.construction}
              isPaid={showPricing}
              colorScheme="bg-gradient-to-br from-amber-50 to-amber-100"
              pdfUrl={pdfUrls.construction}
            />
            <TemplateCard 
              title="Landscaping Invoice" 
              imageSrc="/placeholder.svg"
              description="Designed for landscapers with sections for recurring maintenance and one-time services."
              price={templatePrices.landscaping}
              isPaid={showPricing}
              colorScheme="bg-gradient-to-br from-green-50 to-green-100"
              pdfUrl={pdfUrls.landscaping}
            />
            <TemplateCard 
              title="Cleaning Service Invoice" 
              imageSrc="/placeholder.svg"
              description="For cleaning businesses with checklists of completed tasks and frequency options."
              price={templatePrices.cleaning}
              isPaid={showPricing}
              colorScheme="bg-gradient-to-br from-cyan-50 to-cyan-100"
              pdfUrl={pdfUrls.cleaning}
            />
            <TemplateCard 
              title="Plumbing Service Invoice" 
              imageSrc="/placeholder.svg"
              description="Specialized template for plumbers with emergency service rates and parts inventory."
              comingSoon
              price={templatePrices.plumbing}
              isPaid={showPricing}
              colorScheme="bg-gradient-to-br from-indigo-50 to-indigo-100"
              pdfUrl={pdfUrls.plumbing}
            />
            <TemplateCard 
              title="Electrical Service Invoice" 
              imageSrc="/placeholder.svg"
              description="For electricians with sections for inspection details and code compliance."
              comingSoon
              price={templatePrices.electrical}
              isPaid={showPricing}
              colorScheme="bg-gradient-to-br from-purple-50 to-purple-100"
              pdfUrl={pdfUrls.electrical}
            />
          </div>

          {/* How to Use Section */}
          <div className="glass rounded-2xl p-8 md:p-12 mb-16 bg-gradient-to-br from-primary/5 to-primary/10 border-2 border-primary/10">
            <h2 className="text-2xl md:text-3xl font-bold mb-6">How to Use Our Invoice Templates</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="space-y-3 bg-white dark:bg-gray-800/50 p-6 rounded-xl shadow-sm">
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                  <span className="text-xl font-bold">1</span>
                </div>
                <h3 className="text-xl font-semibold">Download</h3>
                <p className="text-foreground/80 leading-relaxed">
                  Choose the template that best suits your business needs and download it in your preferred format.
                </p>
              </div>
              
              <div className="space-y-3 bg-white dark:bg-gray-800/50 p-6 rounded-xl shadow-sm">
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                  <span className="text-xl font-bold">2</span>
                </div>
                <h3 className="text-xl font-semibold">Customize</h3>
                <p className="text-foreground/80 leading-relaxed">
                  Add your business details, logo, and customize the services and pricing to match your offerings.
                </p>
              </div>
              
              <div className="space-y-3 bg-white dark:bg-gray-800/50 p-6 rounded-xl shadow-sm">
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                  <span className="text-xl font-bold">3</span>
                </div>
                <h3 className="text-xl font-semibold">Send to Clients</h3>
                <p className="text-foreground/80 leading-relaxed">
                  Save as PDF and email to your clients or print physical copies for in-person jobs.
                </p>
              </div>
            </div>
          </div>

          {/* Tips Section */}
          <div>
            <div className="flex items-center gap-3 mb-4">
              <FileText className="h-5 w-5 text-primary" />
              <h2 className="text-2xl font-semibold">Invoice Tips for Service Professionals</h2>
            </div>
            <Separator className="mb-8" />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-12">
              <div className="flex items-start gap-3">
                <CheckCircle2 className="h-5 w-5 text-primary mt-1 shrink-0" />
                <div>
                  <h3 className="text-lg font-medium mb-2">Include Clear Payment Terms</h3>
                  <p className="text-foreground/80 leading-relaxed">
                    Always specify when payment is due, accepted payment methods, and any late payment policies.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle2 className="h-5 w-5 text-primary mt-1 shrink-0" />
                <div>
                  <h3 className="text-lg font-medium mb-2">Itemize Your Services</h3>
                  <p className="text-foreground/80 leading-relaxed">
                    Break down exactly what the client is paying for with detailed descriptions and individual costs.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle2 className="h-5 w-5 text-primary mt-1 shrink-0" />
                <div>
                  <h3 className="text-lg font-medium mb-2">Add Your Branding</h3>
                  <p className="text-foreground/80 leading-relaxed">
                    Customize templates with your logo, brand colors, and contact information for a professional appearance.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle2 className="h-5 w-5 text-primary mt-1 shrink-0" />
                <div>
                  <h3 className="text-lg font-medium mb-2">Follow Up on Unpaid Invoices</h3>
                  <p className="text-foreground/80 leading-relaxed">
                    Have a system for tracking and following up on unpaid invoices to maintain cash flow.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default InvoiceTemplates;
