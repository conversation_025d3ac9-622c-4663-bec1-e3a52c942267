import React, { useState, useEffect } from 'react';
import { ServicePageTemplate } from '../components/ServicePageTemplate';
import { Button } from "@/components/ui/button";
import { Link } from 'react-router-dom';
import { ArrowRight, Check, ShieldCheck, Clock, Award, Handshake, Users, Sun, Battery, Zap, Activity, BarChart, Leaf, LineChart, Calculator, Home, Building, Briefcase, Settings } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { ServiceNeeds } from '@/components/ServiceNeeds';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { SEO } from '@/components/SEO';
import { fetchPosts } from '@/services/wordpressApi';
import {SERVICE_CATEGORY} from "@/types/enum.ts";

const SolarService: React.FC = () => {
  const isMobile = useIsMobile();
    const [solarBlogPosts, setSolarBlogPosts] = useState<BlogPost[]>([])

    useEffect(() => {
        const loadInitialData = async () => {
            try {
                const { posts, totalPages: pages } = await fetchPosts(1, 3, SERVICE_CATEGORY.SOLAR);
                setSolarBlogPosts(posts)
            } catch (err) {
                console.log(err);
            }
        };
        loadInitialData();
    }, []);

  const benefits = [{
    title: "Licensed & Certified Solar Installers",
    description: "All our solar professionals are fully licensed, certified, and insured to handle all types of solar installations and services.",
    icon: <ShieldCheck className="h-6 w-6 text-primary" />
  }, {
    title: "Energy Savings Analysis",
    description: "Get a comprehensive energy audit and savings projection before committing to a solar installation.",
    icon: <BarChart className="h-6 w-6 text-primary" />
  }, {
    title: "Green Energy Solutions",
    description: "Reduce your carbon footprint with environmentally friendly solar power solutions tailored to your needs.",
    icon: <Leaf className="h-6 w-6 text-primary" />
  }, {
    title: "Satisfaction Guaranteed",
    description: "If you're not completely satisfied with our solar service, we'll make it right – guaranteed.",
    icon: <Handshake className="h-6 w-6 text-primary" />
  }, {
    title: "Experienced Professionals",
    description: "Our solar technicians have extensive experience with all types of solar panel installations and equipment.",
    icon: <Users className="h-6 w-6 text-primary" />
  }, {
    title: "Long-term Monitoring",
    description: "We offer monitoring services to ensure your solar system performs optimally for years to come.",
    icon: <Activity className="h-6 w-6 text-primary" />
  }];
  const faqs = [{
    question: "How much can I save with solar panels?",
    answer: "The savings from solar panels vary based on factors like your current electricity costs, available sunlight, system size, and local incentives. Most homeowners see 40-70% reduction in their electricity bills, with many systems paying for themselves within 7-10 years through savings."
  }, {
    question: "What is the process of installing solar panels?",
    answer: "Our solar installation process includes a site assessment, custom system design, permit acquisition, professional installation (usually completed in 1-3 days), inspection, and final utility connection. Throughout this process, we handle all paperwork, permits, and utility company coordination."
  }, {
    question: "How long do solar panels last?",
    answer: "Modern solar panels typically last 25-30 years, with manufacturers' warranties generally covering 25 years. Even after this period, panels continue producing electricity at slightly reduced efficiency. Inverters usually last 10-15 years and may need replacement during your system's lifetime."
  }, {
    question: "Can I install solar panels if my roof doesn't face south?",
    answer: "Yes, while south-facing roofs are ideal in the Northern Hemisphere, east and west-facing roofs can still generate substantial energy. Modern solar technology has improved efficiency for various roof orientations. During our assessment, we'll evaluate your specific situation and recommend the optimal setup for maximum energy production."
  }, {
    question: "What happens to my solar system during a power outage?",
    answer: "Standard grid-tied systems will shut down during a power outage for safety reasons, preventing electricity from being sent to the grid while workers are repairing lines. If you want backup power during outages, we can install a system with battery storage or special transfer switches that provide emergency power while remaining safe."
  }, {
    question: "Are there tax incentives or rebates available for solar installation?",
    answer: "Yes, many solar incentives are available. The federal Investment Tax Credit (ITC) allows you to deduct a percentage of your solar system cost from your federal taxes. Additionally, many states, municipalities, and utilities offer rebates, performance-based incentives, and property tax exemptions. Our team will help you identify all eligible incentives for your location."
  }];
  const solarEstimates = [{
    tier: "Residential Solar",
    price: "$8,000-25,000",
    description: "For homes and properties",
    features: ["Licensed solar installers", "5-15 kW system size", "Custom rooftop design", "Inverter installation", "25-year panel warranty", "Monitoring system"]
  }, {
    tier: "Commercial Solar",
    price: "$25,000-100,000+",
    description: "For businesses and organizations",
    features: ["Expert solar technicians", "15-50 kW system size", "Custom design for maximum ROI", "Premium inverter technology", "Battery storage options", "Energy management system", "30-year performance warranty"],
    recommended: true
  }, {
    tier: "Industrial Solar",
    price: "$100,000+",
    description: "For large facilities",
    features: ["Master solar engineers", "50+ kW system size", "Rooftop or ground mount", "Advanced monitoring", "Grid integration", "Peak demand reduction", "Custom maintenance plans", "Power purchase agreements"]
  }];
  const commonSolarNeeds = [{
    icon: <Sun className="h-8 w-8 text-primary" />,
    label: "Solar Panel Installation"
  }, {
    icon: <Home className="h-8 w-8 text-primary" />,
    label: "Residential Solar"
  }, {
    icon: <Building className="h-8 w-8 text-primary" />,
    label: "Commercial Solar"
  }, {
    icon: <Battery className="h-8 w-8 text-primary" />,
    label: "Solar Battery Storage"
  }, {
    icon: <Settings className="h-8 w-8 text-primary" />,
    label: "Solar Maintenance"
  }, {
    icon: <Calculator className="h-8 w-8 text-primary" />,
    label: "Solar Assessment"
  }, {
    icon: <Zap className="h-8 w-8 text-primary" />,
    label: "Off-Grid Solar"
  }, {
    icon: <LineChart className="h-8 w-8 text-primary" />,
    label: "Energy Monitoring"
  }, {
    icon: <Leaf className="h-8 w-8 text-primary" />,
    label: "Green Energy Consulting"
  }, {
    icon: <Briefcase className="h-8 w-8 text-primary" />,
    label: "Solar Financing"
  }];

  const solarServiceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Solar Panel Installation and Service",
    "serviceType": "Solar Energy Services",
    "provider": {
      "@type": "Organization",
      "name": "JobON",
      "url": "https://jobon.app"
    },
    "description": "Professional solar panel installation, maintenance, and repair services for residential and commercial properties.",
    "areaServed": "United States",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Solar Services",
      "itemListElement": [{
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Residential Solar Installation"
        }
      }, {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Commercial Solar Installation"
        }
      }, {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Solar Battery Storage"
        }
      }, {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Solar System Maintenance"
        }
      }]
    }
  };
  return <>
      <SEO title="Solar Panel Installers Near You – Get Competitive Bids" description="Save on solar with certified pros. Compare quotes for residential or commercial solar panel installs, energy audits, and green upgrades—only on JobON." localBusinessSchema={true} serviceType="Solar" serviceSlug="solar" canonicalUrl="/services/solar" />

      <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-background pt-16 pb-4 md:pt-24 md:pb-12">
        <div className="container mx-auto px-3 lg:px-8">
          {isMobile ? <div className="w-full">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-3">
                <div className="relative h-40 overflow-hidden">
                  <img src="/lovable-uploads/560b5b89-7d33-46bf-9da5-a574b280ed8f.png" alt="Solar panels with blue sky and clouds" className="w-full h-full object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent flex items-end">
                    <div className="p-3 text-white w-full">
                      <h1 className="text-xl font-bold mb-0.5 text-left">
                        Professional Solar
                      </h1>
                      <h2 className="text-base font-medium text-white mb-1 text-left">
                        <span className="block">Expert Installation</span>
                        <span className="block">Energy Savings</span>
                      </h2>
                      <div className="flex items-center space-x-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((_, i) => <svg key={i} className="w-3 h-3 text-amber-400 fill-amber-400" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>)}
                        </div>
                        <span className="text-xs font-medium text-white">4.9/5 · 1,965 reviews</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-3">
                  <p className="text-sm text-gray-800 dark:text-gray-300 mb-3 font-medium">
                    Professional solar installation and services for homes, businesses, and industrial properties
                  </p>

                  <div className="mb-3">
                    <div className="flex shadow-lg rounded-xl overflow-hidden">


                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Link to="/create-job" className="flex-1">
                      <Button variant="default" size="default" className="w-full rounded-md font-semibold text-sm py-2">
                        Find Solar Pro
                      </Button>
                    </Link>
                    <Link to="/professionals/solar" className="flex-1">
                      <Button variant="outline" size="default" className="w-full rounded-md font-semibold text-sm py-2 text-gray-800 dark:text-white border-gray-400">
                        Browse Solar Pros
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-2 mb-3 flex items-center justify-between">
                <div className="flex items-center">
                  <ShieldCheck className="h-6 w-6 text-green-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Certified
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">
                      Installers
                    </span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Clock className="h-6 w-6 text-blue-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">Response</span>
                    <span className="font-bold text-xs text-black dark:text-white">&#60; 1hr</span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Award className="h-6 w-6 text-purple-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Satisfaction
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">Guaranteed</span>
                  </div>
                </div>
              </div>
            </div> :
            <div className="flex flex-col lg:flex-row gap-12 items-center">
              <div className="w-full lg:w-1/2 space-y-8">
                <div className="space-y-4">
                  <span className="inline-block bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-hover px-4 py-1.5 rounded-full font-medium text-sm">Professional Solar Services</span>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark-heading leading-tight">
                    Expert Solar Solutions,
                    <br className="hidden md:inline" />
                    <span className="text-primary mt-2 block dark:text-primary-hover dark:dark-mode-text-shadow">
                      For Every Property.
                    </span>
                  </h1>
                  <div className="text-xl font-semibold text-gray-700 dark-subheading">
                    <div className="flex flex-col space-y-3 text-left">
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Homes, Businesses & Industrial Properties</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Certified & Insured Installers</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Custom Energy Savings Plans</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">25-Year Performance Warranty</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Link to="/create-job" className="w-full sm:w-auto">
                    <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Post a Project
                      <ArrowRight size={20} className="ml-2" />
                    </Button>
                  </Link>
                  <Link to="/professionals/solar" className="w-full sm:w-auto">
                    <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Browse Solar Pros
                    </Button>
                  </Link>
                </div>

                <div className="pt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Free Assessments</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Verified Experts</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>100% Satisfaction</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Financing Options</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Transparent Pricing</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Tax Credit Assistance</span>
                  </div>
                </div>
              </div>

              <div className="w-full lg:w-1/2 relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/5 rounded-xl filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/560b5b89-7d33-46bf-9da5-a574b280ed8f.png" alt="Solar panels with blue sky" className="w-full h-full object-cover" />
                    </div>
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/216c0ecb-96fd-4688-8314-864f824f303f.png" alt="Solar panel field installation" className="w-full h-full object-cover" />
                    </div>
                    <div className="col-span-2 aspect-[16/9] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/768f826b-3e1b-4638-8c9e-7ca2d9c2d8ec.png" alt="House with solar panels on roof with beautiful garden" className="w-full h-full object-cover" />
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <div className="flex justify-center mb-2">
                      <div className="flex text-amber-400">
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">4.9/5 (1,965 reviews)</span>
                    </div>
                    <h3 className="font-bold text-xl mb-1 dark:text-white">Find Top Local Solar Pros</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Enter your ZIP code to get matched with professional solar installers in your area</p>

                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                      <div className="flex-1 flex items-center bg-gray-50 dark:bg-gray-700 p-4">
                        <input type="text" placeholder="Enter your ZIP code" className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-300 text-lg" />
                      </div>
                      <Button type="submit" className="px-8 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all">
                        <span>Search</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>}
        </div>
      </section>

      <section className="py-6 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Common Professional Solar Services</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
              Select from our most requested solar energy services
            </p>
          </div>

          <ServiceNeeds serviceId="solar" needs={commonSolarNeeds} estimates={solarEstimates} />
        </div>
      </section>

      <section className="py-6 md:py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Recently Completed Solar Projects</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Browse through recently completed solar installations by our network of professionals
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="/lovable-uploads/c86fb70f-b386-4bc8-bc63-2687d0fc05e8.png" alt="Residential solar panel installation" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-3 md:p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">1 week ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Residential Solar Installation</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Complete installation of a 10kW solar system on a residential property with battery backup.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Berkeley, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$22,500</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="/lovable-uploads/f96ea7a2-b5e1-4a6d-bd88-95b4732fd7fc.png" alt="Commercial solar installation" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-3 md:p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 weeks ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Office Building Solar Array</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Installation of a 35kW commercial solar system with advanced monitoring on office building rooftop.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Francisco, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$68,000</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1744&q=80" alt="Industrial solar installation" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-3 md:p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">1 month ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Industrial Solar Field</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Large-scale 120kW ground-mounted solar installation for manufacturing facility with energy management system.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Oakland, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$185,000</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-6 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Latest Solar Tips & Guides</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Expert advice and helpful resources for solar energy systems
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-6">
            {solarBlogPosts.map(post => <BlogCard key={post.id} post={post} />)}
          </div>

          <div className="text-center mt-6 md:mt-12">
            <Link to="/blog">
              <Button variant="default" size={isMobile ? "default" : "lg"} className="font-medium">
                View All Solar Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <ServicePageTemplate serviceId="solar" title="Professional Solar Services" subtitle="Expert solar installation and maintenance for homes and businesses" description="Our certified solar technicians provide reliable installation, maintenance, and repair services for residential, commercial, and industrial solar energy systems." heroImage="/lovable-uploads/560b5b89-7d33-46bf-9da5-a574b280ed8f.png" benefits={benefits} faqs={faqs} estimates={solarEstimates} commonNeeds={[]} // Emptied the commonNeeds since we're now displaying it separately
    hideEstimator={false} hideHero={true} commercialSpecific={true} professionalTitle="Solar Pros" seoTitle="Solar Panel Installers Near You | Get Competitive Bids" customCta={<div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/create-job" className="w-full sm:w-auto">
              <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Post a Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/professionals/solar" className="w-full sm:w-auto">
              <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Browse Solar Pros
              </Button>
            </Link>
          </div>} />
    </>;
};
export default SolarService;
