import React, { useState, useEffect } from 'react';
import { ServicePageTemplate } from '../components/ServicePageTemplate';
import { ArrowRight, Check } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Link } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { ServiceNeeds } from '@/components/ServiceNeeds';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { ShieldCheck, Clock, Award, Handshake, Users, Lightbulb, Plug, WifiIcon, PowerIcon, CircuitBoard, Flashlight, BatteryCharging, DollarSign, ThermometerIcon, Shield, Zap, Cable, FanIcon, ChevronsUpDown } from 'lucide-react';
import { SEO } from '@/components/SEO';
import { fetchPosts } from '@/services/wordpressApi';
import { SERVICE_CATEGORY } from "@/types/enum.ts";
const ElectricalService: React.FC = () => {
  const isMobile = useIsMobile();
  const [electricalBlogPosts, setElectricalBlogPosts] = useState<BlogPost[]>([]);
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const {
          posts,
          totalPages: pages
        } = await fetchPosts(1, 3, SERVICE_CATEGORY.ELECTRICAL);
        setElectricalBlogPosts(posts);
      } catch (err) {
        console.log(err);
      }
    };
    loadInitialData();
  }, []);
  const benefits = [{
    title: "Licensed Electricians",
    description: "Our electrical professionals are fully licensed, insured, and trained to handle all electrical work safely.",
    icon: <ShieldCheck className="h-6 w-6 text-primary" />
  }, {
    title: "24/7 Emergency Service",
    description: "Electrical emergencies don't wait, and neither do we. Get help anytime, day or night.",
    icon: <Clock className="h-6 w-6 text-primary" />
  }, {
    title: "Upfront Pricing",
    description: "No surprises or hidden fees. We provide clear, detailed quotes before work begins.",
    icon: <DollarSign className="h-6 w-6 text-primary" />
  }, {
    title: "100% Guaranteed",
    description: "If you're not completely satisfied with our electrical service, we'll make it right – guaranteed.",
    icon: <Handshake className="h-6 w-6 text-primary" />
  }, {
    title: "Expert Electricians",
    description: "Our team has years of experience with residential and commercial electrical systems.",
    icon: <Users className="h-6 w-6 text-primary" />
  }, {
    title: "Energy Efficient Solutions",
    description: "We help you save money on your energy bills with modern, efficient electrical solutions.",
    icon: <BatteryCharging className="h-6 w-6 text-primary" />
  }];
  const faqs = [{
    question: "How do I know if I need to rewire my house?",
    answer: "Signs that your home may need rewiring include frequent circuit breaker trips, flickering lights, burning smells from outlets, discolored outlets or switches, aluminum wiring (in homes built between 1965-1973), or if your home is over 40 years old and hasn't been rewired. A professional electrical inspection can determine if rewiring is necessary."
  }, {
    question: "What's the difference between a fuse and a circuit breaker?",
    answer: "Both fuses and circuit breakers protect your electrical system from overloads, but they work differently. A fuse contains a metal wire that melts when overheated, requiring replacement after it 'blows.' A circuit breaker has an internal switch that trips when overloaded and can be reset multiple times. Circuit breakers are standard in modern homes and offer better protection and convenience."
  }, {
    question: "How often should I have my electrical system inspected?",
    answer: "Most electricians recommend a professional inspection every 3-5 years for homes under 40 years old, and every 1-2 years for older homes. You should also get an inspection when buying a new home, after a major renovation, or if you notice any warning signs like flickering lights or frequent circuit trips."
  }, {
    question: "Can I install a ceiling fan where a light fixture currently exists?",
    answer: "In many cases, yes, but it depends on the existing electrical box and support structure. Light fixture boxes aren't always rated to hold the weight and vibration of a ceiling fan. A licensed electrician can determine if your existing box can support a fan or if a fan-rated box needs to be installed with proper structural support."
  }, {
    question: "Why do my lights dim when I turn on appliances?",
    answer: "This typically happens when large appliances draw significant power upon startup, causing a momentary voltage drop in your electrical system. While occasional dimming might be normal, frequent or severe dimming could indicate inadequate wiring, an overloaded circuit, or an electrical panel that needs upgrading. This should be inspected by a professional electrician."
  }, {
    question: "How much does it cost to install an EV charging station at home?",
    answer: "The cost to install a home EV charging station typically ranges from $500-$2,500. This varies based on the charger type (Level 1 vs. Level 2), existing electrical capacity, distance from your electrical panel to the installation location, and any necessary electrical upgrades. A basic Level 2 installation in a garage with adequate electrical service might cost $750-$1,000."
  }, {
    question: "Is it worth upgrading to a smart electrical panel?",
    answer: "Smart electrical panels can be a worthwhile investment for many homeowners. They provide detailed energy usage monitoring, remote circuit control, integration with solar systems, and improved safety features. They're particularly valuable if you have solar panels, electric vehicles, or want to optimize energy usage. However, with costs ranging from $2,000-$4,000, you should weigh the benefits against your specific needs and budget."
  }];
  const electricalEstimates = [{
    tier: "Basic Service",
    price: "$150 - $300",
    description: "Installing a new electrical outlet, including parts and labor.",
    features: ["Licensed technicians", "90-day warranty", "Standard service"]
  }, {
    tier: "Standard Installation",
    price: "$150 - $400",
    description: "Replacing or installing a new light fixture, excluding fixture cost.",
    features: ["Licensed technicians", "1-year warranty", "Quality materials", "Thorough inspection"]
  }, {
    tier: "Premium Installation",
    price: "$200 - $500",
    description: "Installing a ceiling fan with light kit, excluding fan cost.",
    features: ["Master electricians", "2-year warranty", "Premium materials", "Complete testing", "Extended support"]
  }, {
    tier: "Panel Upgrade",
    price: "$1,500 - $4,000",
    description: "Upgrading electrical service panel from 100A to 200A.",
    features: ["Licensed specialists", "5-year warranty", "Permits included", "Code compliance", "Safety inspection"]
  }, {
    tier: "Whole House Rewiring",
    price: "$8,000 - $15,000",
    description: "Complete rewiring for a 1,500-2,500 sq ft home.",
    features: ["Master electricians", "Lifetime warranty", "Permits included", "Code compliance", "Full-service project management"]
  }, {
    tier: "Generator Installation",
    price: "$3,000 - $10,000",
    description: "Installing a whole-house standby generator, excluding generator cost.",
    features: ["Specialized technicians", "3-year warranty", "Permits included", "Fuel connection", "Control system integration"]
  }, {
    tier: "EV Charger Installation",
    price: "$500 - $2,500",
    description: "Installing a Level 2 electric vehicle charging station.",
    features: ["EV certified technicians", "2-year warranty", "Permits if required", "Load calculation", "Wi-Fi configuration"]
  }, {
    tier: "Electrical Troubleshooting",
    price: "$75 - $150/hr",
    description: "Hourly rate for diagnosing electrical issues.",
    features: ["Expert diagnostics", "No fix no fee options", "Same day service", "Transparent pricing"]
  }];
  const commonElectricalNeeds = [{
    icon: <Lightbulb className="h-8 w-8 text-primary" />,
    label: "Lighting Installation"
  }, {
    icon: <Plug className="h-8 w-8 text-primary" />,
    label: "Outlet Repairs"
  }, {
    icon: <CircuitBoard className="h-8 w-8 text-primary" />,
    label: "Panel Upgrades"
  }, {
    icon: <WifiIcon className="h-8 w-8 text-primary" />,
    label: "Smart Home Wiring"
  }, {
    icon: <PowerIcon className="h-8 w-8 text-primary" />,
    label: "Generator Installation"
  }, {
    icon: <Flashlight className="h-8 w-8 text-primary" />,
    label: "Electrical Inspections"
  }, {
    icon: <ThermometerIcon className="h-8 w-8 text-primary" />,
    label: "Thermostat Installation"
  }, {
    icon: <Zap className="h-8 w-8 text-primary" />,
    label: "Surge Protection"
  }, {
    icon: <Cable className="h-8 w-8 text-primary" />,
    label: "Wiring & Rewiring"
  }, {
    icon: <FanIcon className="h-8 w-8 text-primary" />,
    label: "Ceiling Fan Installation"
  }];
  return <>
      <SEO title="Licensed Electricians Near You – Get Fast, Safe Bids" description="Hire certified electricians for residential or commercial needs. Compare bids for repairs, panel upgrades, smart wiring, and more—only on JobON." localBusinessSchema={true} serviceType="Electrical" serviceSlug="electrical" canonicalUrl='/services/electrical' />

      <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-background pt-20 pb-8 md:pt-24 md:pb-12">
        <div className="container mx-auto px-4 lg:px-8">
          {isMobile ? <div className="w-full">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
                <div className="relative h-52 overflow-hidden">
                  <img src="/lovable-uploads/2baa1dfd-f6b1-4d32-aacc-beac830541e7.png" alt="Professional electrician working on electrical panel" className="w-full h-full object-cover object-center" style={{
                objectPosition: "center 30%"
              }} />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent flex items-end">
                    <div className="p-3 text-white w-full">
                      <h1 className="text-2xl font-bold mb-0.5 text-left">
                        Professional Electrical
                      </h1>
                      <h2 className="text-lg font-medium text-blue-300 mb-1 text-left">
                        <span className="block">Expert Service</span>
                        <span className="block">Reliable Results</span>
                      </h2>
                      <div className="flex items-center space-x-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((_, i) => <svg key={i} className="w-3 h-3 text-amber-400 fill-amber-400" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>)}
                        </div>
                        <span className="text-xs font-medium text-white">4.9/5 · 1,432 reviews</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-3">
                  <p className="text-sm text-gray-800 dark:text-gray-300 mb-3 font-medium">
                    Professional electrical services for homes, offices, and commercial properties
                  </p>

                  <div className="mb-3">
                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                      
                      
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Link to="/create-job" className="flex-1">
                      <Button variant="default" size="default" className="w-full rounded-md font-semibold text-sm py-1.5">
                        Post a Job
                      </Button>
                    </Link>
                    <Link to="/professionals/electrical" className="flex-1">
                      <Button variant="outline" size="default" className="w-full rounded-md font-semibold text-sm py-1.5 text-gray-800 dark:text-white border-gray-400">
                        Find Electricians
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-2 mb-3 flex items-center justify-between">
                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-green-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Licensed
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">
                      Electricians
                    </span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">Response</span>
                    <span className="font-bold text-xs text-black dark:text-white">&#60; 1hr</span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Award className="h-5 w-5 text-purple-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Satisfaction
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">Guaranteed</span>
                  </div>
                </div>
              </div>
            </div> : <div className="flex flex-col lg:flex-row gap-12 items-center">
              <div className="w-full lg:w-1/2 space-y-8">
                <div className="space-y-4">
                  <span className="inline-block bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-hover px-4 py-1.5 rounded-full font-medium text-sm">Professional Electrical Services</span>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark-heading leading-tight">
                    Trusted Electrical
                    <br className="hidden md:inline" />
                    <span className="text-primary mt-2 block dark:text-primary-hover dark:dark-mode-text-shadow">
                      Services You Can Rely On.
                    </span>
                  </h1>
                  <div className="text-xl font-semibold text-gray-700 dark-subheading">
                    <div className="flex flex-col space-y-3 text-left">
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Licensed Professionals</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Same-Day Service</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Fair Pricing</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">24/7 Emergency Service</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Link to="/create-job" className="w-full sm:w-auto">
                    <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Post a Project
                      <ArrowRight size={20} className="ml-2" />
                    </Button>
                  </Link>
                  <Link to="/professionals/electrical" className="w-full sm:w-auto">
                    <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Find Electricians
                    </Button>
                  </Link>
                </div>

                <div className="pt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Instant Quotes</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Licensed Electricians</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>100% Satisfaction</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Emergency Service</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Transparent Pricing</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Insured & Bonded</span>
                  </div>
                </div>
              </div>

              <div className="w-full lg:w-1/2 relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/5 rounded-xl filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/2baa1dfd-f6b1-4d32-aacc-beac830541e7.png" alt="Electrician working on electrical panel" className="w-full h-full object-cover" />
                    </div>
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/d766d3b3-fed7-48f8-9dba-c3658cc6c322.png" alt="Electrician installing outlet" className="w-full h-full object-cover" />
                    </div>
                    <div className="col-span-2 aspect-[16/9] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/24a0423f-e69d-4435-90d6-50398e43751c.png" alt="Professional electrician working on electrical equipment" className="w-full h-full object-cover" />
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <div className="flex justify-center mb-2">
                      <div className="flex text-amber-400">
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">4.9/5 (1,432 reviews)</span>
                    </div>
                    <h3 className="font-bold text-xl mb-1 dark:text-white">Find Top Local Electricians</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Enter your ZIP code to get matched with professional electricians in your area</p>

                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                      <div className="flex-1 flex items-center bg-gray-50 dark:bg-gray-700 p-4">
                        <input type="text" placeholder="Enter your ZIP code" className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-300 text-lg" />
                      </div>
                      <Button type="submit" className="px-8 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all">
                        <span>Search</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>}
        </div>
      </section>

      <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Common Professional Electrical Services</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
              Select from our most requested electrical services
            </p>
          </div>

          <ServiceNeeds serviceId="electrical" needs={commonElectricalNeeds} estimates={electricalEstimates.slice(0, 3)} />
        </div>
      </section>

      <section className="py-8 md:py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Recently Completed Electrical Projects</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Browse through recently completed electrical jobs by our network of professionals
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="/lovable-uploads/f96ea7a2-b5e1-4a6d-bd88-95b4732fd7fc.png" alt="Panel upgrade project" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 days ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Electrical Panel Upgrade</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Upgraded 100A electrical panel to 200A service with new breakers and modern safety features.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Oakland, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$2,200</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="/lovable-uploads/fc281951-ed53-4d20-b7c7-2fa57dfdef3a.png" alt="Generator installation" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">1 week ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Whole House Generator</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Installation of 22kW standby generator with automatic transfer switch for complete home backup power.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Francisco, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$8,500</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="/lovable-uploads/cf2b46d4-a387-4124-80a3-9a137037a620.png" alt="Smart home electrical upgrade" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 weeks ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Smart Home Wiring</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Complete smart home electrical upgrade including lighting, switches, outlets, and home automation hub installation.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Jose, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$4,800</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Latest Electrical Tips & Guides</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Expert advice and helpful resources for maintaining safe and efficient electrical systems
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {electricalBlogPosts.map(post => <BlogCard key={post.id} post={post} />)}
          </div>

          <div className="text-center mt-8 md:mt-12">
            <Link to="/blog">
              <Button variant="default" size={isMobile ? "default" : "lg"} className="font-medium">
                View All Electrical Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <ServicePageTemplate serviceId="electrical" title="Professional Electrical Services" subtitle="Safe, code-compliant electrical solutions for your home or business" description="From repairs and rewiring to new installations and upgrades, our licensed electricians provide safe, reliable electrical services you can trust." heroImage="https://images.unsplash.com/photo-1621905251918-48416bd8575a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2069&q=80" benefits={benefits} faqs={faqs} estimates={electricalEstimates} commonNeeds={[]} hideEstimator={false} hideHero={true} professionalTitle="Electricians" seoTitle="Licensed Electricians Near You | Get Fast, Safe Bids" customCta={<div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/create-job" className="w-full sm:w-auto">
              <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Post a Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/professionals/electrical" className="w-full sm:w-auto">
              <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Find Electricians
              </Button>
            </Link>
          </div>} />
    </>;
};
export default ElectricalService;