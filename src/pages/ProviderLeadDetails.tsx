
import React from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { LeadDetailsView } from '@/components/provider/leads/LeadDetailsView';
import { useParams } from 'react-router-dom';

const ProviderLeadDetails = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <ProviderDashboardLayout pageTitle="Lead Details">
      <LeadDetailsView leadId={id || ''} />
    </ProviderDashboardLayout>
  );
};

export default ProviderLeadDetails;
