import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { ArrowLeft, HelpCircle, FileText, Calculator, Share2, Home, Trash2, Clock, CheckCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {SEO} from "@/components/SEO.tsx";

const HouseCleaningCalculator: React.FC = () => {
  // State for form inputs
  const [squareFootage, setSquareFootage] = useState<number>(1500);
  const [numberOfBedrooms, setNumberOfBedrooms] = useState<number>(2);
  const [numberOfBathrooms, setNumberOfBathrooms] = useState<number>(2);
  const [cleaningFrequency, setCleaningFrequency] = useState<string>("biweekly");
  
  // Additional services
  const [deepCleaning, setDeepCleaning] = useState<boolean>(false);
  const [windowCleaning, setWindowCleaning] = useState<boolean>(false);
  const [laundryService, setLaundryService] = useState<boolean>(false);
  
  // Calculated values
  const [baseCost, setBaseCost] = useState<number>(0);
  const [additionalServicesCost, setAdditionalServicesCost] = useState<number>(0);
  const [totalCost, setTotalCost] = useState<number>(0);
  const [timeEstimate, setTimeEstimate] = useState<number>(0);

  // Calculate values when inputs change
  useEffect(() => {
    // Calculate base cost based on square footage and rooms
    let calculatedBaseCost = 75; // Minimum base cost
    
    // Add cost based on square footage
    calculatedBaseCost += Math.floor(squareFootage / 500) * 15;
    
    // Add cost for bedrooms and bathrooms
    calculatedBaseCost += numberOfBedrooms * 10;
    calculatedBaseCost += numberOfBathrooms * 20;
    
    // Apply frequency discount
    let frequencyMultiplier = 1;
    switch (cleaningFrequency) {
      case "once":
        frequencyMultiplier = 1;
        break;
      case "weekly":
        frequencyMultiplier = 0.8;
        break;
      case "biweekly":
        frequencyMultiplier = 0.9;
        break;
      case "monthly":
        frequencyMultiplier = 0.95;
        break;
    }
    calculatedBaseCost = calculatedBaseCost * frequencyMultiplier;
    
    // Calculate additional services cost
    let calculatedAdditionalServicesCost = 0;
    if (deepCleaning) calculatedAdditionalServicesCost += 75;
    if (windowCleaning) calculatedAdditionalServicesCost += 40;
    if (laundryService) calculatedAdditionalServicesCost += 30;
    
    // Calculate total cost
    const calculatedTotalCost = calculatedBaseCost + calculatedAdditionalServicesCost;
    
    // Calculate time estimate (in hours)
    let calculatedTimeEstimate = 2; // Minimum time
    calculatedTimeEstimate += Math.floor(squareFootage / 1000) * 1;
    calculatedTimeEstimate += numberOfBedrooms * 0.5;
    calculatedTimeEstimate += numberOfBathrooms * 0.5;
    if (deepCleaning) calculatedTimeEstimate += 2;
    if (windowCleaning) calculatedTimeEstimate += 1;
    if (laundryService) calculatedTimeEstimate += 0.5;
    
    setBaseCost(calculatedBaseCost);
    setAdditionalServicesCost(calculatedAdditionalServicesCost);
    setTotalCost(calculatedTotalCost);
    setTimeEstimate(calculatedTimeEstimate);

  }, [squareFootage, numberOfBedrooms, numberOfBathrooms, cleaningFrequency, deepCleaning, windowCleaning, laundryService]);

  // Button action handlers
  const handleSaveAsPDF = () => {
    toast.info("Preparing PDF download...");
    // In a real implementation, this would generate and download a PDF
    setTimeout(() => {
      toast.success("PDF downloaded successfully!");
    }, 1500);
  };

  const handleShareResults = () => {
    toast.info("Preparing to share results...");
    // In a real implementation, this would open a share dialog
    setTimeout(() => {
      toast.success("Results ready to share!");
    }, 1000);
  };

  const handleGetQuotes = () => {
    toast.success("Finding cleaning professionals in your area...");
    // In a real implementation, this would redirect to a quotes page or form
  };

  return (
    <Layout>
      <SEO
          title="House Cleaning Cost Calculator | Estimate Your Cleaning Price Free"
          description="Estimate your house cleaning costs in minutes with JobON’s free Cleaning Cost Calculator. Enter your home size, rooms, and frequency to get an instant quote!"
          localBusinessSchema={true}
          serviceType="House cleaning calculator"
          serviceSlug="house-cleaning-calculator"
          canonicalUrl="/free-tools/house-cleaning-calculator"
      />
      <div className="min-h-screen pt-16 pb-20 bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto max-w-4xl px-4 md:px-6">
          {/* Header with back button */}
          <div className="mb-6 mt-8">
            <Link to="/free-tools" className="flex items-center text-primary hover:underline text-base">
              <ArrowLeft className="mr-2 h-5 w-5" />
              Back to Free Tools
            </Link>
          </div>
          
          {/* Free Tool Badge */}
          <div className="mb-4">
            <Badge variant="outline" className="border-primary text-primary font-medium px-3 py-1 text-base">
              Free Tool
            </Badge>
          </div>
          
          {/* Calculator Title */}
          <h1 className="text-3xl md:text-5xl font-bold mb-3 text-gray-900 dark:text-white">House Cleaning Cost Calculator</h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-12 max-w-2xl">
            Estimate your house cleaning costs based on your home's size, number of rooms, and cleaning requirements.
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Calculator Inputs */}
            <div className="lg:col-span-2">
              <Card className="border shadow-md hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <CardTitle className="text-2xl font-bold">Enter Your Home Details</CardTitle>
                  <CardDescription className="text-base">Input your home specifications to calculate cleaning costs</CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                  {/* Square Footage */}
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <Label htmlFor="squareFootage" className="flex items-center text-lg font-medium">
                        Square Footage 
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="text-sm">
                              <p className="max-w-xs">Total square footage of your home</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right text-lg font-semibold">{squareFootage} sq ft</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <Slider 
                        min={500} 
                        max={5000} 
                        step={100} 
                        defaultValue={[1500]} 
                        value={[squareFootage]}
                        onValueChange={(value) => setSquareFootage(value[0])}
                        className="py-1"
                      />
                      <Input 
                        type="number" 
                        id="squareFootage" 
                        value={squareFootage}
                        className="w-28 text-base" 
                        onChange={(e) => setSquareFootage(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Number of Bedrooms */}
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <Label htmlFor="numberOfBedrooms" className="flex items-center text-lg font-medium">
                        Bedrooms
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="text-sm">
                              <p className="max-w-xs">Number of bedrooms to clean</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right text-lg font-semibold">{numberOfBedrooms}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <Slider 
                        min={1} 
                        max={10} 
                        step={1} 
                        defaultValue={[2]} 
                        value={[numberOfBedrooms]}
                        onValueChange={(value) => setNumberOfBedrooms(value[0])}
                        className="py-1"
                      />
                      <Input 
                        type="number" 
                        id="numberOfBedrooms" 
                        value={numberOfBedrooms}
                        className="w-28 text-base" 
                        onChange={(e) => setNumberOfBedrooms(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Number of Bathrooms */}
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <Label htmlFor="numberOfBathrooms" className="flex items-center text-lg font-medium">
                        Bathrooms
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="text-sm">
                              <p className="max-w-xs">Number of bathrooms to clean</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right text-lg font-semibold">{numberOfBathrooms}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <Slider 
                        min={1} 
                        max={8} 
                        step={1} 
                        defaultValue={[2]} 
                        value={[numberOfBathrooms]}
                        onValueChange={(value) => setNumberOfBathrooms(value[0])}
                        className="py-1"
                      />
                      <Input 
                        type="number" 
                        id="numberOfBathrooms" 
                        value={numberOfBathrooms}
                        className="w-28 text-base" 
                        onChange={(e) => setNumberOfBathrooms(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Cleaning Frequency */}
                  <div className="space-y-3">
                    <Label htmlFor="cleaningFrequency" className="flex items-center text-lg font-medium">
                      Cleaning Frequency
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent className="text-sm">
                            <p className="max-w-xs">How often you want cleaning service</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Button
                        type="button"
                        variant={cleaningFrequency === "once" ? "default" : "outline"}
                        onClick={() => setCleaningFrequency("once")}
                        className="w-full text-base py-6"
                      >
                        One-Time
                      </Button>
                      <Button
                        type="button"
                        variant={cleaningFrequency === "weekly" ? "default" : "outline"}
                        onClick={() => setCleaningFrequency("weekly")}
                        className="w-full text-base py-6"
                      >
                        Weekly
                      </Button>
                      <Button
                        type="button"
                        variant={cleaningFrequency === "biweekly" ? "default" : "outline"}
                        onClick={() => setCleaningFrequency("biweekly")}
                        className="w-full text-base py-6"
                      >
                        Bi-Weekly
                      </Button>
                      <Button
                        type="button"
                        variant={cleaningFrequency === "monthly" ? "default" : "outline"}
                        onClick={() => setCleaningFrequency("monthly")}
                        className="w-full text-base py-6"
                      >
                        Monthly
                      </Button>
                    </div>
                  </div>
                  
                  {/* Additional Services */}
                  <div className="space-y-4">
                    <Label className="flex items-center text-lg font-medium">
                      Additional Services
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent className="text-sm">
                            <p className="max-w-xs">Select any additional cleaning services needed</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Label>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Checkbox id="deepCleaning" checked={deepCleaning} onCheckedChange={(checked) => setDeepCleaning(Boolean(checked))} className="h-5 w-5" />
                        <label
                          htmlFor="deepCleaning"
                          className="text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Deep Cleaning (+$75)
                        </label>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Checkbox id="windowCleaning" checked={windowCleaning} onCheckedChange={(checked) => setWindowCleaning(Boolean(checked))} className="h-5 w-5" />
                        <label
                          htmlFor="windowCleaning"
                          className="text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Window Cleaning (+$40)
                        </label>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Checkbox id="laundryService" checked={laundryService} onCheckedChange={(checked) => setLaundryService(Boolean(checked))} className="h-5 w-5" />
                        <label
                          htmlFor="laundryService"
                          className="text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Laundry Service (+$30)
                        </label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Results Card */}
            <div>
              <Card className="border shadow-md hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <CardTitle className="text-2xl font-bold">Cost Estimate</CardTitle>
                  <CardDescription className="text-base">Based on your inputs</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Total Cost */}
                  <div className="text-center py-6 bg-white/50 dark:bg-black/20 rounded-lg">
                    <div className="text-5xl font-bold text-primary mb-2">
                      ${totalCost.toFixed(2)}
                    </div>
                    <p className="text-lg text-muted-foreground">
                      {cleaningFrequency === "once" ? "One-Time Cleaning" : 
                       cleaningFrequency === "weekly" ? "Weekly Cleaning" :
                       cleaningFrequency === "biweekly" ? "Bi-Weekly Cleaning" : "Monthly Cleaning"}
                    </p>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-lg text-muted-foreground">Base Cleaning:</span>
                      <span className="text-lg font-semibold">${baseCost.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-lg text-muted-foreground">Additional Services:</span>
                      <span className="text-lg font-semibold">${additionalServicesCost.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-lg text-muted-foreground">Time Estimate:</span>
                      <span className="text-lg font-semibold flex items-center">
                        <Clock className="mr-1 h-4 w-4" /> 
                        {timeEstimate.toFixed(1)} hours
                      </span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex-col space-y-3 pt-2 pb-4">
                  {/* Updated primary button with proper styling to match the image */}
                  <Button 
                    className="w-full h-12 bg-blue-500 hover:bg-blue-600 text-white transition-all duration-300 shadow-md flex items-center justify-center"
                    onClick={handleGetQuotes}
                  >
                    <CheckCircle className="mr-2 h-5 w-5 shrink-0" />
                    <span>Get Free Cleaning Quotes</span>
                  </Button>
                  
                  {/* Updated secondary buttons to match the image */}
                  <div className="flex gap-2 w-full">
                    <Button 
                      className="w-1/2 h-12 bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 flex items-center justify-center" 
                      variant="outline"
                      onClick={handleSaveAsPDF}
                    >
                      <FileText className="mr-2 h-5 w-5 shrink-0" />
                      <span>Save as PDF</span>
                    </Button>
                    <Button 
                      className="w-1/2 h-12 bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 flex items-center justify-center" 
                      variant="outline"
                      onClick={handleShareResults}
                    >
                      <Share2 className="mr-2 h-5 w-5 shrink-0" />
                      <span>Share Results</span>
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            </div>
          </div>
          
          {/* Tips Section */}
          <div className="mt-16">
            <h2 className="text-3xl font-bold mb-6">House Cleaning Tips</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <Card className="hover:shadow-lg transition-shadow border">
                <CardHeader>
                  <CardTitle className="text-xl">Prepare Before Service</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-lg text-foreground/70">
                    Pick up clutter before your cleaners arrive to help them focus on deep cleaning rather than organizing. Remove valuable or fragile items for safekeeping.
                  </p>
                </CardContent>
              </Card>
              <Card className="hover:shadow-lg transition-shadow border">
                <CardHeader>
                  <CardTitle className="text-xl">Regular Maintenance</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-lg text-foreground/70">
                    Schedule regular cleanings to maintain a consistently clean home and potentially reduce costs over time. Recurring services often come with discounted rates.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default HouseCleaningCalculator;
