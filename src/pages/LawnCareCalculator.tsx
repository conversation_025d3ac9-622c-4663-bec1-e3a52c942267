import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { ArrowLeft, HelpCircle, FileText, Share2, Scissors, TreePine, Download, Leaf, Calendar, Ruler, Building, Settings, DollarSign } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import {SEO} from "@/components/SEO.tsx";

const LawnCareCalculator: React.FC = () => {
  // Company info
  const [companyName, setCompanyName] = useState<string>("");
  const [companyAddress, setCompanyAddress] = useState<string>("");
  
  // Customer info
  const [customerName, setCustomerName] = useState<string>("");
  const [customerEmail, setCustomerEmail] = useState<string>("");
  const [customerAddress, setCustomerAddress] = useState<string>("");
  
  // State for form inputs
  const [lawnSize, setLawnSize] = useState<number>(5000);
  const [serviceFrequency, setServiceFrequency] = useState<string>("biweekly");
  const [lawnType, setLawnType] = useState<string>("standard");
  const [terrainComplexity, setTerrainComplexity] = useState<string>("simple");
  
  // Pricing configuration
  const [baseCostPerSqFt, setBaseCostPerSqFt] = useState<number>(0.01); // $0.01 per sq ft, equals $10 per 1000 sq ft
  const [minBaseCost, setMinBaseCost] = useState<number>(30); // Minimum base cost
  const [edgingPrice, setEdgingPrice] = useState<number>(15);
  const [fertilizationPrice, setFertilizationPrice] = useState<number>(40);
  const [aerationPrice, setAerationPrice] = useState<number>(65);
  const [weedControlPrice, setWeedControlPrice] = useState<number>(30);
  const [lawnTypeMultipliers, setLawnTypeMultipliers] = useState({
    standard: 1,
    premium: 1.2,
    difficult: 1.1
  });
  const [terrainMultipliers, setTerrainMultipliers] = useState({
    simple: 1,
    moderate: 1.1,
    complex: 1.25
  });
  
  // Additional services
  const [edging, setEdging] = useState<boolean>(true);
  const [fertilization, setFertilization] = useState<boolean>(false);
  const [aeration, setAeration] = useState<boolean>(false);
  const [weedControl, setWeedControl] = useState<boolean>(false);
  
  // Calculated values
  const [baseCost, setBaseCost] = useState<number>(0);
  const [additionalServicesCost, setAdditionalServicesCost] = useState<number>(0);
  const [totalCost, setTotalCost] = useState<number>(0);
  const [annualCost, setAnnualCost] = useState<number>(0);
  const [currentDate] = useState<string>(new Date().toLocaleDateString());

  // Dialog state for price settings
  const [isPriceSettingsOpen, setIsPriceSettingsOpen] = useState<boolean>(false);

  // Calculate values when inputs change
  useEffect(() => {
    // Calculate base cost based on lawn size
    let calculatedBaseCost = minBaseCost; // Minimum base cost
    
    // Add cost based on lawn size
    calculatedBaseCost = Math.max(calculatedBaseCost, lawnSize * baseCostPerSqFt);
    
    // Apply lawn type multiplier
    let lawnTypeMultiplier = lawnTypeMultipliers[lawnType as keyof typeof lawnTypeMultipliers] || 1;
    calculatedBaseCost = calculatedBaseCost * lawnTypeMultiplier;
    
    // Apply terrain complexity multiplier
    let terrainMultiplier = terrainMultipliers[terrainComplexity as keyof typeof terrainMultipliers] || 1;
    calculatedBaseCost = calculatedBaseCost * terrainMultiplier;
    
    // Calculate additional services cost
    let calculatedAdditionalServicesCost = 0;
    if (edging) calculatedAdditionalServicesCost += edgingPrice;
    if (fertilization) calculatedAdditionalServicesCost += fertilizationPrice;
    if (aeration) calculatedAdditionalServicesCost += aerationPrice;
    if (weedControl) calculatedAdditionalServicesCost += weedControlPrice;
    
    // Calculate total cost per service
    const calculatedTotalCost = calculatedBaseCost + calculatedAdditionalServicesCost;
    
    // Calculate annual cost based on frequency
    let visitsPerYear = 0;
    switch (serviceFrequency) {
      case "weekly":
        visitsPerYear = 26; // Assuming 6-month season
        break;
      case "biweekly":
        visitsPerYear = 13; // Assuming 6-month season
        break;
      case "monthly":
        visitsPerYear = 6; // Assuming 6-month season
        break;
    }
    const calculatedAnnualCost = calculatedTotalCost * visitsPerYear;
    
    setBaseCost(calculatedBaseCost);
    setAdditionalServicesCost(calculatedAdditionalServicesCost);
    setTotalCost(calculatedTotalCost);
    setAnnualCost(calculatedAnnualCost);

  }, [
    lawnSize, 
    serviceFrequency, 
    lawnType, 
    terrainComplexity, 
    edging, 
    fertilization, 
    aeration, 
    weedControl,
    baseCostPerSqFt,
    minBaseCost,
    edgingPrice,
    fertilizationPrice,
    aerationPrice,
    weedControlPrice,
    lawnTypeMultipliers,
    terrainMultipliers
  ]);

  const handleSaveAsPDF = () => {
    toast.success("PDF downloaded successfully!", {
      description: "Your lawn care estimate has been saved as a PDF.",
      duration: 3000,
    });
  };

  const handleShare = () => {
    toast.success("Share link created!", {
      description: "A shareable link has been copied to your clipboard.",
      duration: 3000,
    });
  };

  const handleSaveSettings = () => {
    toast.success("Price settings updated!", {
      description: "Your custom pricing has been applied to the calculator.",
      duration: 3000,
    });
    setIsPriceSettingsOpen(false);
  };

  return (
    <Layout>
      <SEO
          title="Lawn Care Cost Calculator | Estimate Lawn Service Prices Free"
          description="Estimate your lawn care service costs easily with JobON’s free Lawn Care Cost Calculator. Enter your lawn size, service needs, and get an instant pricing estimate!"
          localBusinessSchema={true}
          serviceType="lawn care calculator"
          serviceSlug="lawn-care-calculator"
          canonicalUrl="/free-tools/lawn-care-calculator"
      />
      <div className="pt-24 pb-20 px-6 md:px-12 bg-gradient-to-b from-green-50 to-white dark:from-green-900/20 dark:to-background">
        <div className="container mx-auto max-w-6xl">
          {/* Header with back button */}
          <div className="mb-6">
            <Link to="/free-tools" className="flex items-center text-primary hover:underline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Free Tools
            </Link>
          </div>
          
          {/* Calculator Title */}
          <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold mb-2">Lawn Care Cost Calculator</h1>
              <p className="text-muted-foreground max-w-2xl">
                Estimate lawn care costs based on your property size and service requirements.
              </p>
              <div className="flex items-center gap-2 text-sm text-muted-foreground mt-2">
                <Leaf className="h-4 w-4 text-green-600" />
                <span>Professional estimator for homeowners and property managers</span>
              </div>
            </div>
            
            {/* Single pricing button in the header area */}
            <Dialog open={isPriceSettingsOpen} onOpenChange={setIsPriceSettingsOpen}>
              <DialogTrigger asChild>
                <Button 
                  className="gap-2 bg-green-600 hover:bg-green-700 text-white shadow-md hover:shadow-lg transition-all duration-300 mt-4 md:mt-0"
                  size="lg"
                >
                  <DollarSign className="h-5 w-5" />
                  Customize Pricing
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    Customize Pricing
                  </DialogTitle>
                  <DialogDescription>
                    Adjust your pricing structure for different services and factors.
                  </DialogDescription>
                </DialogHeader>

                <div className="grid gap-6 py-4">
                  <div>
                    <h3 className="font-medium mb-3">Base Pricing</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="minBaseCost">Minimum Base Cost ($)</Label>
                        <Input 
                          id="minBaseCost" 
                          type="number" 
                          value={minBaseCost}
                          onChange={(e) => setMinBaseCost(Number(e.target.value))}
                        />
                        <p className="text-xs text-muted-foreground">Minimum charge regardless of lawn size</p>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="baseCostPerSqFt">Cost per Sq Ft ($)</Label>
                        <Input 
                          id="baseCostPerSqFt" 
                          type="number" 
                          step="0.001"
                          value={baseCostPerSqFt}
                          onChange={(e) => setBaseCostPerSqFt(Number(e.target.value))}
                        />
                        <p className="text-xs text-muted-foreground">E.g., 0.01 = $10 per 1,000 sq ft</p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="font-medium mb-3">Additional Services Pricing</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edgingPrice">Edging & Trimming Price ($)</Label>
                        <Input 
                          id="edgingPrice" 
                          type="number" 
                          value={edgingPrice}
                          onChange={(e) => setEdgingPrice(Number(e.target.value))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="fertilizationPrice">Fertilization Price ($)</Label>
                        <Input 
                          id="fertilizationPrice" 
                          type="number" 
                          value={fertilizationPrice}
                          onChange={(e) => setFertilizationPrice(Number(e.target.value))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="aerationPrice">Aeration Price ($)</Label>
                        <Input 
                          id="aerationPrice" 
                          type="number" 
                          value={aerationPrice}
                          onChange={(e) => setAerationPrice(Number(e.target.value))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="weedControlPrice">Weed Control Price ($)</Label>
                        <Input 
                          id="weedControlPrice" 
                          type="number" 
                          value={weedControlPrice}
                          onChange={(e) => setWeedControlPrice(Number(e.target.value))}
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-medium mb-3">Lawn Type Multipliers</h3>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label htmlFor="standardMultiplier">Standard Lawn (multiplier)</Label>
                          <Input 
                            id="standardMultiplier" 
                            type="number" 
                            step="0.1"
                            value={lawnTypeMultipliers.standard}
                            onChange={(e) => setLawnTypeMultipliers({
                              ...lawnTypeMultipliers,
                              standard: Number(e.target.value)
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="premiumMultiplier">Premium Lawn (multiplier)</Label>
                          <Input 
                            id="premiumMultiplier" 
                            type="number" 
                            step="0.1"
                            value={lawnTypeMultipliers.premium}
                            onChange={(e) => setLawnTypeMultipliers({
                              ...lawnTypeMultipliers,
                              premium: Number(e.target.value)
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="difficultMultiplier">Difficult Lawn (multiplier)</Label>
                          <Input 
                            id="difficultMultiplier" 
                            type="number" 
                            step="0.1"
                            value={lawnTypeMultipliers.difficult}
                            onChange={(e) => setLawnTypeMultipliers({
                              ...lawnTypeMultipliers,
                              difficult: Number(e.target.value)
                            })}
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium mb-3">Terrain Complexity Multipliers</h3>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label htmlFor="simpleMultiplier">Simple Terrain (multiplier)</Label>
                          <Input 
                            id="simpleMultiplier" 
                            type="number" 
                            step="0.1"
                            value={terrainMultipliers.simple}
                            onChange={(e) => setTerrainMultipliers({
                              ...terrainMultipliers,
                              simple: Number(e.target.value)
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="moderateMultiplier">Moderate Terrain (multiplier)</Label>
                          <Input 
                            id="moderateMultiplier" 
                            type="number" 
                            step="0.1"
                            value={terrainMultipliers.moderate}
                            onChange={(e) => setTerrainMultipliers({
                              ...terrainMultipliers,
                              moderate: Number(e.target.value)
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="complexMultiplier">Complex Terrain (multiplier)</Label>
                          <Input 
                            id="complexMultiplier" 
                            type="number" 
                            step="0.1"
                            value={terrainMultipliers.complex}
                            onChange={(e) => setTerrainMultipliers({
                              ...terrainMultipliers,
                              complex: Number(e.target.value)
                            })}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsPriceSettingsOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSaveSettings} className="bg-green-600 hover:bg-green-700 text-white">
                    Save Changes
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Simplified Tabs Interface - More like the reference image */}
          <Tabs defaultValue="calculator" className="w-full">
            <TabsList className="mb-6 p-0 bg-transparent border-b space-x-4">
              <TabsTrigger value="calculator" className="flex items-center gap-2 data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-green-600 rounded-none px-2 pb-2">
                <Ruler className="h-4 w-4" />
                Calculate Costs
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2 data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-green-600 rounded-none px-2 pb-2">
                <FileText className="h-4 w-4" />
                PDF Preview
              </TabsTrigger>
              <TabsTrigger value="tips" className="flex items-center gap-2 data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-green-600 rounded-none px-2 pb-2">
                <TreePine className="h-4 w-4" />
                Lawn Care Tips
              </TabsTrigger>
            </TabsList>
            
            {/* Calculator Tab */}
            <TabsContent value="calculator" className="space-y-6">
              {/* Pricing settings info banner - without a duplicate button */}
              <Card className="bg-green-50/50 dark:bg-green-900/10 border-green-200 dark:border-green-700">
                <CardContent className="flex items-center justify-between p-4">
                  <div className="flex items-center gap-3">
                    <div className="bg-green-600 text-white p-2 rounded-full">
                      <Settings className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-medium text-green-800 dark:text-green-300">Customize Your Pricing</h3>
                      <p className="text-sm text-green-700/70 dark:text-green-400/70">Adjust base rates, service costs, and price multipliers</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Company Information */}
              <Card className="shadow-sm border-t-4 border-t-green-600 transition-all duration-300 hover:shadow-md">
                <CardHeader className="bg-green-50/50 dark:bg-green-900/10">
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-5 w-5 text-green-600" />
                    Company Information
                  </CardTitle>
                  <CardDescription>Add your lawn care company details for the estimate</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 pt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="companyName">Company Name</Label>
                      <Input 
                        id="companyName"
                        placeholder="Enter your company name"
                        value={companyName}
                        onChange={(e) => setCompanyName(e.target.value)}
                        className="border-green-200 focus-visible:ring-green-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="companyAddress">Company Address</Label>
                      <Input 
                        id="companyAddress"
                        placeholder="Enter company address"
                        value={companyAddress}
                        onChange={(e) => setCompanyAddress(e.target.value)}
                        className="border-green-200 focus-visible:ring-green-500"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Customer Information */}
              <Card className="shadow-sm border-t-4 border-t-green-600 transition-all duration-300 hover:shadow-md">
                <CardHeader className="bg-green-50/50 dark:bg-green-900/10">
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-green-600" />
                    Customer Information
                  </CardTitle>
                  <CardDescription>Add customer details for your estimate</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 pt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="customerName">Customer Name</Label>
                      <Input 
                        id="customerName"
                        placeholder="Enter customer name"
                        value={customerName}
                        onChange={(e) => setCustomerName(e.target.value)}
                        className="border-green-200 focus-visible:ring-green-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="customerEmail">Email Address</Label>
                      <Input 
                        id="customerEmail"
                        type="email"
                        placeholder="Enter email address"
                        value={customerEmail}
                        onChange={(e) => setCustomerEmail(e.target.value)}
                        className="border-green-200 focus-visible:ring-green-500"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="customerAddress">Service Address</Label>
                    <Input 
                      id="customerAddress"
                      placeholder="Enter property address"
                      value={customerAddress}
                      onChange={(e) => setCustomerAddress(e.target.value)}
                      className="border-green-200 focus-visible:ring-green-500"
                    />
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Calculator Inputs */}
                <div className="lg:col-span-2">
                  <Card className="shadow-md">
                    <CardHeader className="border-b bg-gradient-to-r from-green-50 to-transparent dark:from-green-900/20 dark:to-transparent">
                      <CardTitle className="flex items-center gap-2">
                        <Ruler className="h-5 w-5 text-green-600" />
                        Lawn Details
                      </CardTitle>
                      <CardDescription>Input your lawn specifications to calculate service costs</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-8 p-6">
                      {/* Lawn Size */}
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="lawnSize" className="flex items-center text-base">
                            Lawn Size 
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="max-w-xs">Total square footage of your lawn area</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </Label>
                          <span className="text-right font-medium text-green-700">{lawnSize.toLocaleString()} sq ft</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="w-full bg-green-100 h-2 rounded-full">
                            <Slider 
                              min={1000} 
                              max={20000} 
                              step={500} 
                              defaultValue={[5000]} 
                              value={[lawnSize]}
                              onValueChange={(value) => setLawnSize(value[0])}
                              className="[&>span]:bg-green-600"
                            />
                          </div>
                          <Input 
                            type="number" 
                            id="lawnSize" 
                            value={lawnSize}
                            className="w-24 border-green-200 focus-visible:ring-green-500" 
                            onChange={(e) => setLawnSize(Number(e.target.value))}
                          />
                        </div>
                      </div>
                      
                      {/* Service Frequency */}
                      <div className="space-y-3">
                        <Label htmlFor="serviceFrequency" className="text-base flex items-center">
                          Service Frequency
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">How often you want lawn care service</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </Label>
                        <div className="grid grid-cols-3 gap-4">
                          <Button
                            type="button"
                            variant={serviceFrequency === "weekly" ? "default" : "outline"}
                            onClick={() => setServiceFrequency("weekly")}
                            className={`w-full ${serviceFrequency === "weekly" ? "bg-green-600 hover:bg-green-700" : "border-green-200 hover:border-green-400"}`}
                          >
                            Weekly
                          </Button>
                          <Button
                            type="button"
                            variant={serviceFrequency === "biweekly" ? "default" : "outline"}
                            onClick={() => setServiceFrequency("biweekly")}
                            className={`w-full ${serviceFrequency === "biweekly" ? "bg-green-600 hover:bg-green-700" : "border-green-200 hover:border-green-400"}`}
                          >
                            Bi-Weekly
                          </Button>
                          <Button
                            type="button"
                            variant={serviceFrequency === "monthly" ? "default" : "outline"}
                            onClick={() => setServiceFrequency("monthly")}
                            className={`w-full ${serviceFrequency === "monthly" ? "bg-green-600 hover:bg-green-700" : "border-green-200 hover:border-green-400"}`}
                          >
                            Monthly
                          </Button>
                        </div>
                      </div>
                      
                      {/* Lawn Type */}
                      <div className="space-y-3">
                        <Label htmlFor="lawnType" className="text-base flex items-center">
                          Lawn Type
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">Type of grass in your lawn</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </Label>
                        <Select 
                          value={lawnType} 
                          onValueChange={setLawnType}
                        >
                          <SelectTrigger className="w-full border-green-200 focus:ring-green-500">
                            <SelectValue placeholder="Select lawn type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="standard">Standard (Fescue, Kentucky Bluegrass)</SelectItem>
                            <SelectItem value="premium">Premium (Bermuda, Zoysia)</SelectItem>
                            <SelectItem value="difficult">Difficult (St. Augustine, Centipede)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      {/* Terrain Complexity */}
                      <div className="space-y-3">
                        <Label htmlFor="terrainComplexity" className="text-base flex items-center">
                          Terrain Complexity
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">How complex your lawn terrain is to mow</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </Label>
                        <Select 
                          value={terrainComplexity} 
                          onValueChange={setTerrainComplexity}
                        >
                          <SelectTrigger className="w-full border-green-200 focus:ring-green-500">
                            <SelectValue placeholder="Select terrain complexity" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="simple">Simple (Flat, few obstacles)</SelectItem>
                            <SelectItem value="moderate">Moderate (Some slopes, garden beds)</SelectItem>
                            <SelectItem value="complex">Complex (Steep slopes, many obstacles)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      {/* Additional Services */}
                      <div className="space-y-4">
                        <Label className="text-base flex items-center">
                          Additional Services
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">Select any additional lawn care services needed</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </Label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div className="flex items-center space-x-2 p-3 rounded-lg border border-green-200 hover:bg-green-50/50 hover:border-green-300 transition-colors">
                            <Checkbox id="edging" checked={edging} onCheckedChange={(checked) => setEdging(Boolean(checked))} className="border-green-500 text-green-600" />
                            <label
                              htmlFor="edging"
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                            >
                              Edging & Trimming (+${edgingPrice})
                            </label>
                          </div>
                          <div className="flex items-center space-x-2 p-3 rounded-lg border border-green-200 hover:bg-green-50/50 hover:border-green-300 transition-colors">
                            <Checkbox id="fertilization" checked={fertilization} onCheckedChange={(checked) => setFertilization(Boolean(checked))} className="border-green-500 text-green-600" />
                            <label
                              htmlFor="fertilization"
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                            >
                              Fertilization (+${fertilizationPrice})
                            </label>
                          </div>
                          <div className="flex items-center space-x-2 p-3 rounded-lg border border-green-200 hover:bg-green-50/50 hover:border-green-300 transition-colors">
                            <Checkbox id="aeration" checked={aeration} onCheckedChange={(checked) => setAeration(Boolean(checked))} className="border-green-500 text-green-600" />
                            <label
                              htmlFor="aeration"
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                            >
                              Aeration (+${aerationPrice})
                            </label>
                          </div>
                          <div className="flex items-center space-x-2 p-3 rounded-lg border border-green-200 hover:bg-green-50/50 hover:border-green-300 transition-colors">
                            <Checkbox id="weedControl" checked={weedControl} onCheckedChange={(checked) => setWeedControl(Boolean(checked))} className="border-green-500 text-green-600" />
                            <label
                              htmlFor="weedControl"
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                            >
                              Weed Control (+${weedControlPrice})
                            </label>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Results Card */}
                <div>
                  <Card className="sticky top-24 border-2 border-green-600/20 shadow-lg shadow-green-100 dark:shadow-none bg-gradient-to-br from-white to-green-50/50 dark:from-card dark:to-green-900/5">
                    <CardHeader className="bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-t-lg border-b border-green-700">
                      <CardTitle className="flex justify-between items-center">
                        <span>Cost Estimate</span>
                        <DollarSign className="h-5 w-5 text-green-100" />
                      </CardTitle>
                      <CardDescription className="text-green-100">Based on your specifications</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6 pt-6">
                      {/* Total Cost Per Service */}
                      <div className="text-center py-6 bg-green-50 dark:bg-green-900/10 rounded-lg border border-green-200 dark:border-green-700 shadow-inner">
                        <div className="text-5xl font-bold text-green-700 dark:text-green-500 mb-2">
                          ${totalCost.toFixed(2)}
                        </div>
                        <p className="text-green-600 dark:text-green-400 font-medium">Per Service Cost</p>
                      </div>
                      
                      <Separator className="bg-green-200 dark:bg-green-800" />
                      
                      <div className="space-y-4">
                        <div className="flex justify-between items-center p-2 rounded hover:bg-green-50 dark:hover:bg-green-900/10 transition-colors">
                          <span className="text-muted-foreground flex items-center gap-1">
                            <Scissors className="h-3.5 w-3.5" /> Base Mowing:
                          </span>
                          <span className="font-medium">${baseCost.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between items-center p-2 rounded hover:bg-green-50 dark:hover:bg-green-900/10 transition-colors">
                          <span className="text-muted-foreground flex items-center gap-1">
                            <Leaf className="h-3.5 w-3.5" /> Additional Services:
                          </span>
                          <span className="font-medium">${additionalServicesCost.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between items-center p-2 rounded bg-green-50/50 dark:bg-green-900/5 font-medium">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3.5 w-3.5" /> Annual Cost:
                          </span>
                          <span className="text-green-700 dark:text-green-400">${annualCost.toFixed(2)}</span>
                        </div>
                      </div>

                      <div className="pt-2">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <Button variant="outline" className="w-full border-green-200 hover:border-green-400 flex gap-2 items-center justify-center" onClick={handleSaveAsPDF}>
                            <Download className="h-4 w-4" />
                            Save PDF
                          </Button>
                          <Button variant="outline" className="w-full border-green-200 hover:border-green-400 flex gap-2 items-center justify-center" onClick={handleShare}>
                            <Share2 className="h-4 w-4" />
                            Share
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="preview">
              <Card>
                <CardHeader>
                  <CardTitle>PDF Preview</CardTitle>
                  <CardDescription>Preview how your estimate will look as a PDF</CardDescription>
                </CardHeader>
                <CardContent className="flex justify-center">
                  <div className="border rounded-md p-8 w-full max-w-3xl bg-white shadow-lg">
                    {/* PDF Preview Content */}
                    <div className="text-center mb-6">
                      <h2 className="text-2xl font-bold">{companyName || "Your Lawn Care Company"}</h2>
                      <p className="text-muted-foreground">{companyAddress || "123 Main St, Anytown, USA"}</p>
                    </div>
                    
                    <div className="border-b pb-4 mb-4">
                      <h3 className="text-xl font-bold text-green-700 mb-2">Lawn Care Service Estimate</h3>
                      <p className="text-sm text-muted-foreground">Date: {currentDate}</p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-6 mb-6">
                      <div>
                        <h4 className="font-semibold mb-2">Client Information</h4>
                        <p>{customerName || "Client Name"}</p>
                        <p>{customerEmail || "<EMAIL>"}</p>
                        <p>{customerAddress || "456 Client Address, City, State"}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">Service Details</h4>
                        <div className="grid grid-cols-2 gap-1 text-sm">
                          <span>Lawn Size:</span>
                          <span className="font-medium">{lawnSize.toLocaleString()} sq ft</span>
                          
                          <span>Service Frequency:</span>
                          <span className="font-medium capitalize">{serviceFrequency}</span>
                          
                          <span>Lawn Type:</span>
                          <span className="font-medium capitalize">{lawnType}</span>
                          
                          <span>Terrain:</span>
                          <span className="font-medium capitalize">{terrainComplexity}</span>
                        </div>
                      </div>
                    </div>
                    
                    <table className="w-full mb-6">
                      <thead className="bg-green-50 text-left">
                        <tr>
                          <th className="p-2">Service</th>
                          <th className="p-2 text-right">Price</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="p-2">Base Lawn Mowing</td>
                          <td className="p-2 text-right">${baseCost.toFixed(2)}</td>
                        </tr>
                        {edging && (
                          <tr className="border-b">
                            <td className="p-2">Edging & Trimming</td>
                            <td className="p-2 text-right">${edgingPrice.toFixed(2)}</td>
                          </tr>
                        )}
                        {fertilization && (
                          <tr className="border-b">
                            <td className="p-2">Fertilization</td>
                            <td className="p-2 text-right">${fertilizationPrice.toFixed(2)}</td>
                          </tr>
                        )}
                        {aeration && (
                          <tr className="border-b">
                            <td className="p-2">Aeration</td>
                            <td className="p-2 text-right">${aerationPrice.toFixed(2)}</td>
                          </tr>
                        )}
                        {weedControl && (
                          <tr className="border-b">
                            <td className="p-2">Weed Control</td>
                            <td className="p-2 text-right">${weedControlPrice.toFixed(2)}</td>
                          </tr>
                        )}
                        <tr className="font-bold bg-green-50">
                          <td className="p-2">Total Per Service</td>
                          <td className="p-2 text-right">${totalCost.toFixed(2)}</td>
                        </tr>
                      </tbody>
                    </table>

                    <div className="border-t pt-4 mb-6">
                      <div className="flex justify-between items-center font-bold text-lg">
                        <span>Estimated Annual Cost:</span>
                        <span className="text-green-700">${annualCost.toFixed(2)}</span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        Based on {serviceFrequency === "weekly" ? "26" : serviceFrequency === "biweekly" ? "13" : "6"} services per season
                      </p>
                    </div>

                    <div className="text-center text-sm text-muted-foreground mt-12 pt-4 border-t">
                      <p>Thank you for your business!</p>
                      <p>This estimate is valid for 30 days from the date issued.</p>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-center">
                  <Button onClick={handleSaveAsPDF} className="bg-green-600 hover:bg-green-700">
                    <Download className="mr-2 h-4 w-4" /> Download PDF
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="tips">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TreePine className="h-5 w-5 text-green-600" />
                    Lawn Care Tips & Best Practices
                  </CardTitle>
                  <CardDescription>Professional advice to maintain a healthy, beautiful lawn</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="border rounded-lg p-5 bg-green-50/50 dark:bg-green-900/10 shadow-sm">
                        <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                          <Scissors className="h-4 w-4 text-green-600" /> Mowing Guidelines
                        </h3>
                        <ul className="space-y-2 text-sm">
                          <li className="flex gap-2">
                            <span className="text-green-600 font-medium">✓</span>
                            <span>Mow regularly, cutting no more than 1/3 of the grass height at once</span>
                          </li>
                          <li className="flex gap-2">
                            <span className="text-green-600 font-medium">✓</span>
                            <span>Keep mower blades sharp for clean cuts that heal quickly</span>
                          </li>
                          <li className="flex gap-2">
                            <span className="text-green-600 font-medium">✓</span>
                            <span>Vary mowing patterns to prevent soil compaction and grass lean</span>
                          </li>
                          <li className="flex gap-2">
                            <span className="text-green-600 font-medium">✓</span>
                            <span>Adjust cutting height seasonally (higher in summer, lower in spring/fall)</span>
                          </li>
                        </ul>
                      </div>

                      <div className="border rounded-lg p-5 bg-green-50/50 dark:bg-green-900/10 shadow-sm">
                        <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                          <Leaf className="h-4 w-4 text-green-600" /> Watering Advice
                        </h3>
                        <ul className="space-y-2 text-sm">
                          <li className="flex gap-2">
                            <span className="text-green-600 font-medium">✓</span>
                            <span>Water deeply but infrequently to encourage deep root growth</span>
                          </li>
                          <li className="flex gap-2">
                            <span className="text-green-600 font-medium">✓</span>
                            <span>Morning watering (5-10am) is ideal to reduce evaporation and fungal growth</span>
                          </li>
                          <li className="flex gap-2">
                            <span className="text-green-600 font-medium">✓</span>
                            <span>Aim for 1-1.5 inches of water per week, including rainfall</span>
                          </li>
                          <li className="flex gap-2">
                            <span className="text-green-600 font-medium">✓</span>
                            <span>Adjust watering schedule based on season, weather, and soil type</span>
                          </li>
                        </ul>
                      </div>
                    </div>

                    <Separator />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="font-semibold text-lg mb-3">Seasonal Lawn Care Calendar</h3>
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-medium text-green-700">Spring (March-May)</h4>
                            <ul className="list-disc list-inside text-sm space-y-1 mt-1 text-gray-700">
                              <li>Apply pre-emergent weed control</li>
                              <li>Begin regular mowing as grass starts growing</li>
                              <li>Test soil pH and nutrients</li>
                              <li>Overseed bare patches</li>
                              <li>Apply spring fertilizer</li>
                            </ul>
                          </div>
                          <div>
                            <h4 className="font-medium text-green-700">Summer (June-August)</h4>
                            <ul className="list-disc list-inside text-sm space-y-1 mt-1 text-gray-700">
                              <li>Raise mowing height to reduce heat stress</li>
                              <li>Water deeply early in the morning</li>
                              <li>Monitor for pest and disease issues</li>
                              <li>Apply light fertilizer if needed</li>
                              <li>Spot-treat weeds</li>
                            </ul>
                          </div>
                          <div>
                            <h4 className="font-medium text-green-700">Fall (September-November)</h4>
                            <ul className="list-disc list-inside text-sm space-y-1 mt-1 text-gray-700">
                              <li>Core aeration</li>
                              <li>Overseed the entire lawn</li>
                              <li>Apply fall fertilizer</li>
                              <li>Continue mowing until growth stops</li>
                              <li>Remove leaves regularly</li>
                            </ul>
                          </div>
                          <div>
                            <h4 className="font-medium text-green-700">Winter (December-February)</h4>
                            <ul className="list-disc list-inside text-sm space-y-1 mt-1 text-gray-700">
                              <li>Minimize foot traffic on dormant grass</li>
                              <li>Plan for spring equipment maintenance</li>
                              <li>Apply winter weed control if needed</li>
                              <li>Keep lawn clear of debris</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-6">
                        <div className="border rounded-lg p-5 bg-amber-50/50 dark:bg-amber-900/10 shadow-sm">
                          <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                            <HelpCircle className="h-4 w-4 text-amber-600" /> Common Lawn Problems
                          </h3>
                          <div className="space-y-3 text-sm">
                            <div>
                              <h4 className="font-medium text-amber-700">Brown Patches</h4>
                              <p>May indicate fungal disease, insect damage, or drought stress. Check for grubs and ensure proper watering.</p>
                            </div>
                            <div>
                              <h4 className="font-medium text-amber-700">Thin Areas</h4>
                              <p>Often caused by compacted soil, shade, or improper mowing. Consider aeration, overseeding, or shade-tolerant grass varieties.</p>
                            </div>
                            <div>
                              <h4 className="font-medium text-amber-700">Weeds</h4>
                              <p>Indicate weak turf. Improve overall lawn health through proper mowing, fertilizing, and watering to crowd out weeds.</p>
                            </div>
                          </div>
                        </div>

                        <div className="border rounded-lg p-5 bg-blue-50/50 dark:bg-blue-900/10 shadow-sm">
                          <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                            <DollarSign className="h-4 w-4 text-blue-600" /> Cost-Saving Tips
                          </h3>
                          <ul className="space-y-2 text-sm">
                            <li className="flex gap-2">
                              <span className="text-blue-600 font-medium">✓</span>
                              <span>Maintain your mower regularly for optimal performance</span>
                            </li>
                            <li className="flex gap-2">
                              <span className="text-blue-600 font-medium">✓</span>
                              <span>Use smart irrigation controllers to prevent overwatering</span>
                            </li>
                            <li className="flex gap-2">
                              <span className="text-blue-600 font-medium">✓</span>
                              <span>Practice grasscycling (leaving clippings) to return nutrients to soil</span>
                            </li>
                            <li className="flex gap-2">
                              <span className="text-blue-600 font-medium">✓</span>
                              <span>Consider seasonal lawn care packages for better rates</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default LawnCareCalculator;
