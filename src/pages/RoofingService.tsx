import React, { useState, useEffect } from 'react';
import { ServicePageTemplate } from '../components/ServicePageTemplate';
import { ArrowRight, Check } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Link, useLocation } from 'react-router-dom';
import { Hammer, ShieldCheck, Clock, Award, Handshake, Users, Home, Briefcase, Building, CheckCheck, MessageSquare, ThumbsUp, Wrench, HardHat, Cloud, Layers, Sun, Shield, Umbrella, Ruler, PaintBucket, Factory, Paintbrush, Lightbulb } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { ServiceNeeds } from '@/components/ServiceNeeds';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { SEO } from '@/components/SEO';
import { fetchPosts } from '@/services/wordpressApi';
import {SERVICE_CATEGORY} from "@/types/enum.ts";

const RoofingService: React.FC = () => {
  const isMobile = useIsMobile();
  const [roofingBlogPosts, setRoofingBlogPosts] = useState<BlogPost[]>([]);
  const location = useLocation();

  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const { posts, totalPages: pages } = await fetchPosts(1, 3, SERVICE_CATEGORY.ROOFING);
        setRoofingBlogPosts(posts);
      } catch (err) {
        console.log(err);
      }
    };
    loadInitialData();
  }, []);

  const benefits = [{
    title: "Certified Roofing Contractors",
    description: "Our roofing professionals are fully certified, insured, and undergo thorough background checks before joining our trusted network.",
    icon: <ShieldCheck className="h-6 w-6 text-primary" />
  }, {
    title: "Emergency Response",
    description: "Fast response times for leaks and storm damage with 24/7 emergency services available when you need them most.",
    icon: <Clock className="h-6 w-6 text-primary" />
  }, {
    title: "Quality Materials",
    description: "We use only premium roofing materials from trusted manufacturers with excellent warranties and long-lasting performance.",
    icon: <Award className="h-6 w-6 text-primary" />
  }, {
    title: "Satisfaction Guaranteed",
    description: "Our work is backed by strong warranties and we're not satisfied until you're completely happy with your new roof.",
    icon: <Handshake className="h-6 w-6 text-primary" />
  }, {
    title: "Expert Inspections",
    description: "Thorough roof inspections to identify existing or potential issues before they become major problems.",
    icon: <Users className="h-6 w-6 text-primary" />
  }, {
    title: "Customized Solutions",
    description: "We tailor our roofing services to your specific home style, climate needs, and budget considerations.",
    icon: <Hammer className="h-6 w-6 text-primary" />
  }];
  const faqs = [{
    question: "How do I know if I need a roof repair or complete replacement?",
    answer: "Several factors determine whether repair or replacement is best: age of your roof (most asphalt roofs last 20-25 years), extent of damage, missing shingles, leaks, and overall roof condition. Our professional inspection can help determine the most cost-effective approach for your situation."
  }, {
    question: "How long does a typical roof installation take?",
    answer: "For an average residential home (1,500-2,200 sq ft), a complete roof replacement typically takes 1-3 days. Larger homes, complex roof designs, or adverse weather conditions may extend this timeline. Our team works efficiently to minimize disruption to your daily life."
  }, {
    question: "What types of roofing materials do you offer?",
    answer: "We offer a comprehensive range of roofing materials including asphalt shingles, metal roofing, tile, slate, wood shakes, and flat roofing systems like TPO and EPDM. During your consultation, we'll discuss the advantages of each option for your specific climate, home style, and budget."
  }, {
    question: "Do you provide warranties on roofing work?",
    answer: "Yes, we provide manufacturer warranties on materials (typically 25-50 years depending on the product) plus our own workmanship warranty covering installation (typically 5-10 years). The specific terms will be clearly outlined in your contract before work begins."
  }, {
    question: "What should I do if my roof is leaking?",
    answer: "For active leaks, place buckets to catch water and move valuables away from the area. If possible, take photos of the damage and water intrusion points. Then contact us immediately for emergency service. We'll tarp the area if needed before permanent repairs begin."
  }, {
    question: "Can you work with my insurance for storm damage claims?",
    answer: "Absolutely. We have extensive experience working with insurance companies on storm damage claims. We can perform a free inspection, document the damage, and work directly with your insurance adjuster to ensure you receive the coverage you're entitled to under your policy."
  }];
  const roofingEstimates = [{
    tier: "Roof Repair",
    price: "$350-800",
    description: "For minor to moderate repairs",
    features: ["Professional inspection", "Leak repair", "Shingle replacement", "Flashing repair", "Minor water damage repair", "90-day workmanship warranty"]
  }, {
    tier: "Roof Replacement",
    price: "$8,000-16,000",
    description: "Complete roof system installation",
    features: ["Complete tear-off and disposal", "Premium materials", "New underlayment and flashing", "Enhanced ventilation system", "Complete cleanup", "25-50 year material warranty", "10-year workmanship warranty"],
    recommended: true
  }, {
    tier: "Commercial Roofing",
    price: "$15,000-75,000+",
    description: "For business properties",
    features: ["Large-scale installations", "Flat/low-slope systems", "Commercial-grade materials", "Minimal business disruption", "Scheduled maintenance plans", "Code compliance guaranteed", "Extended commercial warranties"]
  }];
  const commonRoofingNeeds = [{
    icon: <Home className="h-8 w-8 text-primary" />,
    label: "Roof Replacement"
  }, {
    icon: <Umbrella className="h-8 w-8 text-primary" />,
    label: "Leak Repair"
  }, {
    icon: <Cloud className="h-8 w-8 text-primary" />,
    label: "Storm Damage"
  }, {
    icon: <Layers className="h-8 w-8 text-primary" />,
    label: "Shingle Installation"
  }, {
    icon: <HardHat className="h-8 w-8 text-primary" />,
    label: "Roof Inspection"
  }, {
    icon: <Wrench className="h-8 w-8 text-primary" />,
    label: "Gutter Services"
  }, {
    icon: <Building className="h-8 w-8 text-primary" />,
    label: "Commercial Roofing"
  }, {
    icon: <Sun className="h-8 w-8 text-primary" />,
    label: "Solar Roof"
  }, {
    icon: <Ruler className="h-8 w-8 text-primary" />,
    label: "Roof Measurement"
  }, {
    icon: <PaintBucket className="h-8 w-8 text-primary" />,
    label: "Roof Coating"
  }];

  return <>
      <SEO title="Hire Roofers Near You – Compare Trusted Roofing Repair Bids" description="Get your roof fixed fast by certified, insured contractors. Compare bids for storm damage, replacements, inspections, and more—only on JobON." localBusinessSchema={true} serviceType="Roofing" serviceSlug="roofing" canonicalUrl='/services/roofing' />
      <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-background pt-16 pb-4 md:pt-24 md:pb-12">
        <div className="container mx-auto px-3 lg:px-8">
          {isMobile ? <div className="w-full">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-3">
                <div className="relative h-40 overflow-hidden">
                  <img src="/lovable-uploads/d6bfc109-ab1b-4457-8fc3-adee546c51ca.png" alt="Professional roofing contractor installing roof" className="w-full h-full object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent flex items-end">
                    <div className="p-3 text-white w-full">
                      <h1 className="text-xl font-bold mb-0.5 text-left">
                        Professional Roofing
                      </h1>
                      <h2 className="text-base font-medium text-white mb-1 text-left">
                        <span className="block">Expert Installation</span>
                        <span className="block">Quality Materials</span>
                      </h2>
                      <div className="flex items-center space-x-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((_, i) => <svg key={i} className="w-3 h-3 text-amber-400 fill-amber-400" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>)}
                        </div>
                        <span className="text-xs font-medium text-white">4.9/5 · 1,256 reviews</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-3">
                  <p className="text-sm text-gray-800 dark:text-gray-300 mb-3 font-medium">
                    Expert roofing services for residential, commercial, and storm damage repairs
                  </p>

                  <div className="mb-2">
                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Link to="/create-job" state={{ from: location.pathname }} className="flex-1">
                      <Button variant="default" size="default" className="w-full rounded-md font-semibold text-sm py-2">
                        Post a Project
                      </Button>
                    </Link>
                    <Link to="/professionals/roofing" state={{ from: location.pathname }} className="flex-1">
                      <Button variant="outline" size="default" className="w-full rounded-md font-semibold text-sm py-2 text-gray-800 dark:text-white border-gray-400">
                        Browse Roofers
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-2 mb-2 flex items-center justify-between">
                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-green-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Licensed
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">
                      Contractors
                    </span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">Response</span>
                    <span className="font-bold text-xs text-black dark:text-white">&#60; 1hr</span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Award className="h-5 w-5 text-purple-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Warranty
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">Guaranteed</span>
                  </div>
                </div>
              </div>
            </div> :
            <div className="flex flex-col lg:flex-row gap-12 items-center">
              <div className="w-full lg:w-1/2 space-y-8">
                <div className="space-y-4">
                  <span className="inline-block bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-hover px-4 py-1.5 rounded-full font-medium text-sm">Professional Roofing Services</span>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark-heading leading-tight">
                    Expert Roof Solutions,
                    <br className="hidden md:inline" />
                    <span className="text-primary mt-2 block dark:text-primary-hover dark:dark-mode-text-shadow">
                      Built to Last.
                    </span>
                  </h1>
                  <div className="text-xl font-semibold text-gray-700 dark-subheading">
                    <div className="flex flex-col space-y-3 text-left">
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Residential & Commercial Roofing</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Certified & Insured Contractors</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Emergency Repairs & Storm Damage</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Premium Materials & Warranties</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Link to="/create-job" state={{ from: location.pathname }} className="w-full sm:w-auto">
                    <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Post a Project
                      <ArrowRight size={20} className="ml-2" />
                    </Button>
                  </Link>
                  <Link to="/professionals/roofing" state={{ from: location.pathname }} className="w-full sm:w-auto">
                    <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Browse Roofers
                    </Button>
                  </Link>
                </div>

                <div className="pt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Free Inspections</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Licensed Contractors</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Warranty Protection</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Insurance Claims</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Financing Available</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Emergency Service</span>
                  </div>
                </div>
              </div>

              <div className="w-full lg:w-1/2 relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/5 rounded-xl filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/d6bfc109-ab1b-4457-8fc3-adee546c51ca.png" alt="Professional roofer installing wooden roof frame" className="w-full h-full object-cover" />
                    </div>
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/c4ffeb83-419f-4504-bcf7-d1d4505fc87b.png" alt="Roofing professional with safety harness" className="w-full h-full object-cover" />
                    </div>
                    <div className="col-span-2 aspect-[16/9] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/28b095d6-47a5-4566-a04a-1fd663ffad83.png" alt="Roofer installing metal roof with safety equipment" className="w-full h-full object-cover" />
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <div className="flex justify-center mb-2">
                      <div className="flex text-amber-400">
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">4.9/5 (1,256 reviews)</span>
                    </div>
                    <h3 className="font-bold text-xl mb-1 dark:text-white">Find Top Local Roofers</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Enter your ZIP code to get matched with professional roofing contractors in your area</p>

                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                      <div className="flex-1 flex items-center bg-gray-50 dark:bg-gray-700 p-4">
                        <input type="text" placeholder="Enter your ZIP code" className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-300 text-lg" />
                      </div>
                      <Button type="submit" className="px-8 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all">
                        <span>Search</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>}
        </div>
      </section>

      <section className="py-6 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Common Roofing Services</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
              Select from our most requested roofing services
            </p>
          </div>

          <ServiceNeeds serviceId="roofing" needs={commonRoofingNeeds} estimates={roofingEstimates} />
        </div>
      </section>

      <section className="py-6 md:py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Recently Completed Roofing Projects</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Browse through recently completed roofing projects by our network of professionals
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1595514535215-8a5b9fcf9e9a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" alt="New roof installation" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 days ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Full Roof Replacement</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Complete tear-off and installation of architectural shingles on a 2,800 sq ft home with new gutters and downspouts.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Palo Alto, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$14,500</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1631198902233-c8abf4a0e551?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" alt="Commercial roof repair" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">1 week ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Commercial Flat Roof Repair</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Fixed multiple leaks and installed a new TPO membrane system on a 5,000 sq ft office building with minimal business disruption.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Francisco, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$22,800</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1625963350184-599d0994d9f0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" alt="Storm damage repair" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">3 weeks ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Emergency Storm Damage Repair</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Quickly repaired roof and siding damage after severe windstorm, including replacement of missing shingles and damaged flashing.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Jose, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$3,850</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-6 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Latest Roofing Tips & Guides</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Expert advice and helpful resources for maintaining your roof and home
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {roofingBlogPosts.map(post => <BlogCard key={post.id} post={post} />)}
          </div>

          <div className="text-center mt-6 md:mt-12">
            <Link to="/blog">
              <Button variant="default" size={isMobile ? "default" : "lg"} className="font-medium">
                View All Roofing Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <ServicePageTemplate serviceId="roofing" title="Professional Roofing Services" subtitle="Get reliable, high-quality roofing services from licensed and insured professionals" description="From complete roof replacements to emergency repairs and storm damage restoration, our experienced contractors deliver durable, weather-tight solutions for your home or business." heroImage="/lovable-uploads/d6bfc109-ab1b-4457-8fc3-adee546c51ca.png" benefits={benefits} faqs={faqs} estimates={roofingEstimates} commonNeeds={[]} hideEstimator={false} hideHero={true} professionalTitle="Roofers" seoTitle="Hire Roofers Near You | Compare Trusted Repair Bids" customCta={<div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/create-job" state={{ from: location.pathname }} className="w-full sm:w-auto">
              <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Post a Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/professionals/roofing" state={{ from: location.pathname }} className="w-full sm:w-auto">
              <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Browse Roofers
              </Button>
            </Link>
          </div>} />
    </>;
};
export default RoofingService;
