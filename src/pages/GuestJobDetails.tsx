
import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, MapPin, LockIcon, EyeOff, ArrowRight, ArrowLeft } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Separator } from '@/components/ui/separator';
import { toast } from "@/hooks/use-toast";
import { format } from 'date-fns';
import { useIsMobile } from '@/hooks/use-mobile';
import { Helmet } from 'react-helmet-async';
import { Layout } from '@/components/Layout';

// Mock job data for demonstration
const MOCK_JOB = {
  id: '123',
  title: 'Kitchen Faucet Repair',
  category: 'plumbing',
  description: 'My kitchen faucet is leaking and needs to be repaired or replaced. The water is dripping from the base when turned on, and the handle is loose. I would prefer to repair it if possible, but I am open to replacement if necessary.',
  datePosted: '2023-10-15',
  location: {
    city: 'Denver',
    state: 'CO',
    zipCode: '80202',
  },
  budget: '$150-$250',
  customer: {
    firstName: 'John',
    lastInitial: 'D',
    rating: 4.8,
    jobsPosted: 7,
  },
  attachments: [
    '/lovable-uploads/c78bdc9f-7294-4aa6-a0a1-4da41162b0b0.png',
    '/lovable-uploads/9b223582-543e-4d67-bdeb-38c5a3054a83.png',
    '/lovable-uploads/264cb19a-cf3e-4f08-bc7d-9837c95b4b27.png',
  ],
  offers: 3,
  status: 'open',
  urgency: 'high',
  details: [
    { label: 'Type', value: 'Repair' },
    { label: 'Timeframe', value: 'Within 3 days' },
    { label: 'Property Type', value: 'Apartment' },
  ],
};

export default function GuestJobDetails() {
  const { jobId } = useParams<{ jobId: string }>();
  const [job, setJob] = useState<typeof MOCK_JOB | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('details');
  const [isAuthDialogOpen, setIsAuthDialogOpen] = useState<boolean>(false);
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  useEffect(() => {
    // In a real app, this would fetch the job details from an API
    setLoading(true);
    setTimeout(() => {
      setJob(MOCK_JOB);
      setLoading(false);
    }, 500);
  }, [jobId]);

  const handleSignInClick = () => {
    setIsAuthDialogOpen(true);
  };

  const handleBack = () => {
    navigate('/jobs');
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </Layout>
    );
  }

  if (!job) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold text-red-500">Job not found</h1>
          <p className="mt-4">
            The job you're looking for might have been removed or doesn't exist.
          </p>
        </div>
      </Layout>
    );
  }

  const formattedDate = format(new Date(job.datePosted), 'MMMM d, yyyy');

  // Mobile View
  if (isMobile) {
    return (
      <Layout>
        <Helmet>
          <title>{job.title} | JobON - Guest View</title>
          <meta name="description" content={`View details of ${job.title} job - Sign in to make an offer`} />
        </Helmet>
        
        {/* Header for Mobile */}
        <div className="bg-white shadow-sm px-4 py-2 mb-4 flex items-center">
          <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-lg font-semibold truncate max-w-[200px]">{job.title}</h1>
            <div className="flex items-center text-xs text-gray-500 mt-0.5">
              <MapPin className="h-3 w-3 mr-1" />
              <span className="truncate">{job.location.city}, {job.location.state}</span>
            </div>
          </div>
        </div>
        
        <div className="pb-16 bg-gray-50 min-h-screen">
          <Tabs defaultValue="details" className="bg-white" onValueChange={setActiveTab}>
            <TabsList className="w-full justify-start px-2 bg-white border-b">
              <TabsTrigger value="details" className="flex-1">Details</TabsTrigger>
              <TabsTrigger value="photos" className="flex-1">Photos</TabsTrigger>
              <TabsTrigger value="customer" className="flex-1">Customer</TabsTrigger>
            </TabsList>
            
            <TabsContent value="details" className="p-4">
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Posted</span>
                  <span className="text-sm text-gray-500 flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    {formattedDate}
                  </span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Budget Range</span>
                  <span className="text-sm text-gray-500">{job.budget}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Status</span>
                  <Badge variant={job.status === 'open' ? 'success' : 'default'} className="capitalize">
                    {job.status}
                  </Badge>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Urgency</span>
                  <Badge variant={job.urgency === 'high' ? 'destructive' : 'outline'} className="capitalize">
                    {job.urgency}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Offers</span>
                  <span className="text-sm text-gray-500">{job.offers} submitted</span>
                </div>
              </div>
              
              <Separator className="my-4" />
              
              <div className="mb-6">
                <h3 className="text-md font-medium mb-2">Description</h3>
                <p className="text-sm text-gray-700">{job.description}</p>
              </div>
              
              <div className="space-y-3">
                <h3 className="text-md font-medium">Additional Details</h3>
                {job.details.map((detail, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-600">{detail.label}</span>
                    <span className="text-sm text-gray-700">{detail.value}</span>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 space-y-4">
                <div className="bg-blue-50 border border-blue-100 rounded-md p-4">
                  <div className="flex items-start">
                    <LockIcon className="h-5 w-5 text-blue-500 mr-3 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-800">Sign in to submit an offer</h4>
                      <p className="text-sm text-blue-600 mt-1">Create a free account to bid on this job and connect with customers.</p>
                    </div>
                  </div>
                </div>
                
                <Button 
                  className="w-full" 
                  onClick={handleSignInClick}
                >
                  Sign in to Make an Offer
                </Button>
                
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => navigate('/provider-signup')}
                >
                  Create Provider Account
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="photos" className="p-4">
              <div className="grid grid-cols-1 gap-4">
                {job.attachments.map((photo, index) => (
                  <div key={index} className="rounded-lg overflow-hidden relative">
                    <div className="relative">
                      <img 
                        src={photo} 
                        alt={`Job photo ${index + 1}`}
                        className="w-full h-48 object-cover blur-sm" 
                      />
                      <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/40 text-white p-4">
                        <EyeOff className="h-8 w-8 mb-2" />
                        <p className="text-center font-medium">
                          Sign in to see clear photos
                        </p>
                        <p className="text-sm text-center mt-1">
                          Create a free account to access all job details
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <Button 
                variant="outline" 
                className="w-full mt-4"
                onClick={handleSignInClick}
              >
                Sign in to view full photos
              </Button>
            </TabsContent>
            
            <TabsContent value="customer" className="p-4">
              <Card className="border-none shadow-none">
                <CardHeader className="px-0 pt-0">
                  <CardTitle className="text-lg">Customer Information</CardTitle>
                </CardHeader>
                <CardContent className="px-0 space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Name</span>
                    <span>{job.customer.firstName} {job.customer.lastInitial}.</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Rating</span>
                    <div className="flex items-center">
                      <span className="mr-1">{job.customer.rating}</span>
                      <div className="flex">
                        {Array(5).fill(0).map((_, i) => (
                          <svg key={i} className={`w-4 h-4 ${i < Math.floor(job.customer.rating) ? 'text-yellow-400' : 'text-gray-300'}`} fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                          </svg>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Jobs posted</span>
                    <span>{job.customer.jobsPosted}</span>
                  </div>
                </CardContent>
              </Card>
              
              <div className="mt-6 text-center text-sm text-gray-500 space-y-2">
                <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
                  <p className="mb-2 font-medium text-gray-700">Keep all communication on JobON</p>
                  <p>Our secure payment system protects both providers and customers</p>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Fixed footer button for mobile */}
          {activeTab === 'details' && (
            <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-3 z-10">
              <Button 
                className="w-full" 
                onClick={handleSignInClick}
              >
                Sign in to Make an Offer
              </Button>
            </div>
          )}
        </div>
        
        {/* AuthDialog */}
        <Dialog open={isAuthDialogOpen} onOpenChange={setIsAuthDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Sign in to access full details</DialogTitle>
              <DialogDescription>
                Create an account or sign in to make offers and see complete job details including clear photos.
              </DialogDescription>
            </DialogHeader>
            <div className="flex flex-col space-y-4 mt-4">
              <Button onClick={() => navigate('/auth?mode=signin&redirect=' + encodeURIComponent(`/job/${jobId}`))}>
                Sign In
              </Button>
              <Button variant="outline" onClick={() => navigate('/provider-signup?redirect=' + encodeURIComponent(`/job/${jobId}`))}>
                Create Provider Account
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </Layout>
    );
  }

  // Desktop view
  return (
    <Layout>
      <Helmet>
        <title>{job.title} | JobON - Guest View</title>
        <meta name="description" content={`View details of ${job.title} job - Sign in to make an offer`} />
      </Helmet>
      
      {/* Header for Desktop */}
      <div className="bg-white border-b mb-6">
        <div className="container mx-auto px-4 py-4 max-w-7xl">
          <div className="flex items-center">
            <Button variant="ghost" size="sm" className="mr-4" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Jobs
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">{job.title}</h1>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-4 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left column - Job details */}
          <div className="lg:col-span-8 space-y-6">
            <div>
              <div className="flex items-center text-gray-600 mb-4">
                <MapPin className="h-4 w-4 mr-1" />
                <span>{`${job.location.city}, ${job.location.state} ${job.location.zipCode}`}</span>
                <span className="mx-2">•</span>
                <CalendarIcon className="h-4 w-4 mr-1" />
                <span>Posted {formattedDate}</span>
              </div>
            </div>
            
            <Card>
              <CardContent className="p-6">
                <h2 className="text-lg font-semibold mb-4">Description</h2>
                <p className="text-gray-700">{job.description}</p>
                
                <div className="mt-6 grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Budget Range</h3>
                    <p className="text-gray-900 font-medium">{job.budget}</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Status</h3>
                    <Badge variant={job.status === 'open' ? 'success' : 'default'} className="capitalize">
                      {job.status}
                    </Badge>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Urgency</h3>
                    <Badge variant={job.urgency === 'high' ? 'destructive' : 'outline'} className="capitalize">
                      {job.urgency}
                    </Badge>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Offers</h3>
                    <p className="text-gray-900 font-medium">{job.offers} submitted</p>
                  </div>
                </div>
                
                <Separator className="my-6" />
                
                <h2 className="text-lg font-semibold mb-4">Additional Details</h2>
                <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {job.details.map((detail, index) => (
                    <div key={index} className="sm:col-span-1">
                      <dt className="text-sm font-medium text-gray-500">{detail.label}</dt>
                      <dd className="mt-1 text-gray-900">{detail.value}</dd>
                    </div>
                  ))}
                </dl>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Photos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                  {job.attachments.map((photo, index) => (
                    <div key={index} className="aspect-square rounded-md overflow-hidden relative">
                      <img 
                        src={photo} 
                        alt={`Job photo ${index + 1}`} 
                        className="w-full h-full object-cover blur-sm"
                      />
                      <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/40 text-white p-4">
                        <EyeOff className="h-6 w-6 mb-2" />
                        <p className="text-center text-sm font-medium">
                          Sign in to view
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Right column - Customer info & actions */}
          <div className="lg:col-span-4 space-y-6">
            <Card className="sticky top-24">
              <CardHeader>
                <CardTitle className="text-lg">Customer</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Name</span>
                  <span>{job.customer.firstName} {job.customer.lastInitial}.</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Rating</span>
                  <div className="flex items-center">
                    <span className="mr-1">{job.customer.rating}</span>
                    <div className="flex">
                      {Array(5).fill(0).map((_, i) => (
                        <svg key={i} className={`w-4 h-4 ${i < Math.floor(job.customer.rating) ? 'text-yellow-400' : 'text-gray-300'}`} fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Jobs posted</span>
                  <span>{job.customer.jobsPosted}</span>
                </div>
                
                <Separator className="my-4" />
                
                <div className="bg-blue-50 border border-blue-100 rounded-md p-4 text-sm">
                  <div className="flex items-start">
                    <LockIcon className="h-5 w-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-blue-800">Access restricted</p>
                      <p className="text-blue-600 mt-1">Sign in to see complete job details and submit your bid</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3 text-sm text-center text-gray-600 bg-gray-50 p-4 rounded-md border border-gray-100">
                  <p className="font-medium text-gray-700">Keep all communication on JobON</p>
                  <p>Our secure payment system protects both providers and customers</p>
                </div>
                
                <Button 
                  className="w-full mt-4" 
                  onClick={handleSignInClick}
                >
                  Sign in to Make an Offer
                </Button>
                
                <Button 
                  variant="outline" 
                  className="w-full" 
                  onClick={() => navigate('/provider-signup')}
                >
                  Create Provider Account <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      
      {/* AuthDialog */}
      <Dialog open={isAuthDialogOpen} onOpenChange={setIsAuthDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Sign in to access full details</DialogTitle>
            <DialogDescription>
              Create an account or sign in to make offers and see complete job details including clear photos.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col space-y-4 mt-4">
            <Button onClick={() => navigate('/auth?mode=signin&redirect=' + encodeURIComponent(`/job/${jobId}`))}>
              Sign In
            </Button>
            <Button variant="outline" onClick={() => navigate('/provider-signup?redirect=' + encodeURIComponent(`/job/${jobId}`))}>
              Create Provider Account
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Layout>
  );
}
