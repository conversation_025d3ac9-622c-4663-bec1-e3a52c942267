import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronRight, Star, Shield, Clock, CheckCircle, ArrowRight, ClipboardIcon, MessageSquare, CheckCheck, ThumbsUp, Building, Home, Store } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Layout } from '@/components/Layout';
import { Hero } from '@/components/Hero';
import { CategoryList } from '@/components/CategoryList';
import { TestimonialsCarousel } from '@/components/TestimonialsCarousel';
import { TrustAuthority } from '@/components/TrustAuthority';
import { Link } from 'react-router-dom';
import { formatDate } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { BlogCard } from '@/components/BlogCard';
import { SEO } from '@/components/SEO';
import { LatestBlogPosts } from '@/components/home/<USER>';
import { AuroraBackground } from '@/components/ui/aurora-background';

const features = [{
  icon: <Shield className="h-8 w-8 text-primary" />,
  title: 'Verified Providers',
  description: 'All service providers go through a verification process to ensure quality and reliability.'
}, {
  icon: <Star className="h-8 w-8 text-primary" />,
  title: 'Rated & Reviewed',
  description: 'Browse ratings and reviews to find the perfect provider for your job.'
}, {
  icon: <Clock className="h-8 w-8 text-primary" />,
  title: 'Quick Responses',
  description: 'Get offers from providers within minutes of posting your job.'
}, {
  icon: <CheckCircle className="h-8 w-8 text-primary" />,
  title: 'Satisfaction Guaranteed',
  description: 'We ensure your job is completed to your satisfaction.'
}];

const HomePage: React.FC = () => {
  const [votes, setVotes] = useState<Record<string, number>>({});
  const [votedVisible, setVotedVisible] = useState<Record<string, boolean>>({});
  const isMobile = useIsMobile();

  const handleVote = (postId: string) => {
    setVotes(prev => ({
      ...prev,
      [postId]: (prev[postId] || 0) + 1
    }));
    setVotedVisible(prev => ({
      ...prev,
      [postId]: true
    }));
  };

  return <Layout>
    <SEO
        title="Hire Trusted Local Service Pros – Compare Bids & Get Jobs Done Fast"
        description="Post a job, compare bids, and hire trusted local pros fast. Verified providers. No signup required. Get your home or office project done right with JobON."
        localBusinessSchema={true}
        serviceType="jobon"
        serviceSlug="jobon"
        canonicalUrl="/"
    />

      <Hero title={isMobile ? undefined : (
            <>
            <span className="text-gray-900 dark:text-white">
              Find Local Service Pros.
            </span>
            <br className="hidden md:inline" />
            <span className="text-primary mt-1 block dark:text-blue-300">
              Compare Bids & Hire the Best.
            </span>
          </>
      )} subtitle={<span className="block">
            Connect with qualified professionals for all your home, office, and business service needs
          </span>} />

      <TrustAuthority />

      <section className="py-12 md:py-20 px-4 md:px-6 lg:px-12">
        <div className="container mx-auto">
          <div className="text-center mb-8 md:mb-12">
            <h5 className="text-xs md:text-sm font-medium text-primary/80 uppercase tracking-wider">
              SIMPLE PROCESS
            </h5>
            <h2 className="text-2xl md:text-4xl font-bold mt-2 mb-2 md:mb-4 text-gray-900 dark:text-white">
              How JobON works
            </h2>
            <p className="text-sm md:text-lg text-gray-700 dark:text-gray-200 max-w-2xl mx-auto">
              Getting help is easy. Follow these simple steps to get started.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8 mt-6 md:mt-12">
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 md:p-8 text-center relative shadow-sm">
              <div className="flex flex-col items-center">
                <ClipboardIcon className="h-10 w-10 md:h-12 md:w-12 text-primary mb-3 md:mb-4" strokeWidth={1.5} />
                <h3 className="text-lg md:text-xl font-semibold mb-2 md:mb-3 text-gray-900 dark:text-white">Post a job</h3>
                <p className="text-sm md:text-base text-gray-700 dark:text-gray-200">
                  Describe what you need, when you need it, and your budget.
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 md:p-8 text-center relative shadow-sm">
              <div className="flex flex-col items-center">
                <MessageSquare className="h-10 w-10 md:h-12 md:w-12 text-primary mb-3 md:mb-4" strokeWidth={1.5} />
                <h3 className="text-lg md:text-xl font-semibold mb-2 md:mb-3 text-gray-900 dark:text-white">Get offers</h3>
                <p className="text-sm md:text-base text-gray-700 dark:text-gray-200">
                  Receive offers from skilled providers ready to help.
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 md:p-8 text-center relative shadow-sm">
              <div className="flex flex-col items-center">
                <CheckCheck className="h-10 w-10 md:h-12 md:w-12 text-primary mb-3 md:mb-4" strokeWidth={1.5} />
                <h3 className="text-lg md:text-xl font-semibold mb-2 md:mb-3 text-gray-900 dark:text-white">Get it done</h3>
                <p className="text-sm md:text-base text-gray-700 dark:text-gray-200">
                  Choose a provider and get your job completed to your satisfaction.
                </p>
              </div>
            </div>
          </div>

          <div className="text-center mt-8 md:mt-12 flex flex-col sm:flex-row justify-center gap-3 md:gap-4">
            <Button asChild size={isMobile ? "default" : "lg"} variant="default" className={`text-sm md:text-lg font-medium w-full sm:w-auto px-4 md:px-8 ${isMobile ? 'py-5' : 'py-6'} h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all`}>
              <Link to="/create-job">
                Post a Project
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Link>
            </Button>
            <Button asChild size={isMobile ? "default" : "lg"} variant="secondary" className={`text-sm md:text-lg font-medium w-full sm:w-auto px-4 md:px-8 ${isMobile ? 'py-5' : 'py-6'} h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all`}>
              <Link to="/professionals">
                Browse Pros
              </Link>
            </Button>
          </div>
        </div>
      </section>

      <section className="py-12 md:py-20 px-4 md:px-6 lg:px-12">
        <div className="container mx-auto">
          <div className="text-center mb-8 md:mb-12">
            <h5 className="text-xs md:text-sm font-medium text-primary/80 uppercase tracking-wider">
              Success Stories
            </h5>
            <h2 className="text-2xl md:text-4xl font-bold mt-2 mb-2 md:mb-4">
              What our customers say
            </h2>
            <p className="text-sm md:text-lg text-foreground/70 max-w-2xl mx-auto">
              Discover how JobON has helped thousands of people get their jobs done.
            </p>
          </div>

          <TestimonialsCarousel />
        </div>
      </section>

      <LatestBlogPosts />

      <section className="py-12 md:py-20 px-4 md:px-6 lg:px-12">
        <div className="container mx-auto">
          <div className="text-center mb-8 md:mb-12">
            <h5 className="text-xs md:text-sm font-medium text-primary/80 uppercase tracking-wider">
              Why Choose Us
            </h5>
            <h2 className="text-2xl md:text-4xl font-bold mt-2 mb-2 md:mb-4">
              The JobON advantage
            </h2>
            <p className="text-sm md:text-lg text-foreground/70 max-w-2xl mx-auto">
              We make getting help simple, safe, and satisfying.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6">
            {features.map((feature, index) => <div key={index} className="bg-white dark:bg-gray-800 rounded-xl md:rounded-2xl p-4 md:p-6 shadow-sm h-full flex flex-col items-center text-center">
                <div className="mb-3 md:mb-4">
                  {isMobile ? React.cloneElement(feature.icon as React.ReactElement, {
                className: "h-6 w-6 text-primary"
              }) : feature.icon}
                </div>
                <h3 className="text-sm md:text-xl font-semibold mb-1 md:mb-2 text-gray-900 dark:text-white">{feature.title}</h3>
                <p className="text-xs md:text-base text-gray-700 dark:text-gray-200">
                  {isMobile ? "" : feature.description}
                </p>
              </div>)}
          </div>
        </div>
      </section>

      <section className="py-12 md:py-20 px-4 md:px-6 lg:px-12 bg-primary/5 dark:bg-gray-900">
        <div className="container mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-xl md:rounded-2xl p-6 md:p-12 text-center max-w-4xl mx-auto shadow-sm">
            <h2 className="text-xl md:text-4xl font-bold mb-3 md:mb-6 text-gray-900 dark:text-white">
              Ready to get your job done?
            </h2>
            <p className="text-sm md:text-lg text-gray-700 dark:text-gray-200 mb-6 md:mb-8 max-w-2xl mx-auto">
              Join thousands of people who use JobON to get things done quickly and efficiently.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-3 md:gap-4">
              <Link to="/create-job" className="w-full sm:w-auto">
                <Button variant="default" size={isMobile ? "default" : "lg"} className={`text-sm md:text-lg font-medium w-full sm:w-auto px-4 md:px-8 ${isMobile ? 'py-5' : 'py-6'} h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all`}>
                  Post a Job
                  <ArrowRight size={isMobile ? 16 : 20} className="ml-2" />
                </Button>
              </Link>
              <Link to="/professionals" className="w-full sm:w-auto">
                <Button variant="secondary" size={isMobile ? "default" : "lg"} className={`text-sm md:text-lg font-medium w-full sm:w-auto px-4 md:px-8 ${isMobile ? 'py-5' : 'py-6'} h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all`}>
                  Browse Pros
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </Layout>;
};

export default HomePage;
