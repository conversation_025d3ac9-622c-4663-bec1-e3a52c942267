import React, { useState, useEffect } from 'react';
import { ServicePageTemplate } from '../components/ServicePageTemplate';
import { ArrowRight, Check } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Link, useLocation } from 'react-router-dom';
import { PaintBucket, ShieldCheck, Clock, Award, Handshake, Users, Hammer, Drill, Wrench, Ruler, GalleryVerticalEnd, Lightbulb, Home, DoorOpen, MoreHorizontal, Settings, Shield, Building, Building2 } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { ServiceNeeds } from '@/components/ServiceNeeds';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { SEO } from '@/components/SEO';
import { fetchPosts } from '@/services/wordpressApi';
import {SERVICE_CATEGORY} from "@/types/enum.ts";

const HandymanService: React.FC = () => {
  const isMobile = useIsMobile();
  const location = useLocation();
  const [handymanBlogPosts, setHandymanBlogPosts] = useState<BlogPost[]>([])

    useEffect(() => {
        const loadInitialData = async () => {
            try {
                const { posts, totalPages: pages } = await fetchPosts(1, 10, SERVICE_CATEGORY.HANDYMAN);
                setHandymanBlogPosts(posts)
            } catch (err) {
                console.log(err);
            }
        };
        loadInitialData();
    }, []);

  const benefits = [{
    title: "Skilled Professionals",
    description: "Our handymen are experienced in a wide range of home repair and maintenance tasks, ensuring quality work every time.",
    icon: <Hammer className="h-6 w-6 text-primary" />
  }, {
    title: "Licensed & Insured",
    description: "All our handymen are fully licensed, insured, and undergo thorough background checks for your peace of mind.",
    icon: <ShieldCheck className="h-6 w-6 text-primary" />
  }, {
    title: "Quick Response",
    description: "We understand your time is valuable, so we offer flexible scheduling including same-day service when available.",
    icon: <Clock className="h-6 w-6 text-primary" />
  }, {
    title: "Upfront Pricing",
    description: "Get transparent, no-surprise pricing before any work begins. We provide detailed quotes upfront.",
    icon: <Award className="h-6 w-6 text-primary" />
  }, {
    title: "Satisfaction Guaranteed",
    description: "We stand behind our work with a 100% satisfaction guarantee on all handyman services.",
    icon: <Handshake className="h-6 w-6 text-primary" />
  }, {
    title: "Versatile Expertise",
    description: "From minor repairs to larger home improvement projects, our handymen have the skills to tackle a wide range of tasks.",
    icon: <Users className="h-6 w-6 text-primary" />
  }];
  const faqs = [{
    question: "What types of handyman services do you offer?",
    answer: "We offer a comprehensive range of handyman services including furniture assembly, fixture installation, drywall repair, painting, tile work, deck repair, door installation, and much more. If you have a specific task in mind, just ask!"
  }, {
    question: "How quickly can you schedule a handyman visit?",
    answer: "In most service areas, we can schedule a handyman visit within 1-3 days. For urgent issues, we often have same-day availability depending on our current schedule."
  }, {
    question: "What is your minimum service charge?",
    answer: "Our minimum service charge is typically for a one-hour visit. Many small tasks can be completed within this timeframe, and for larger projects, we provide upfront estimates based on the expected time and materials."
  }, {
    question: "Do you provide the materials needed for repairs?",
    answer: "In most cases, our handymen bring common materials and supplies. For specific items or customer preferences, we can either purchase materials for you (with the cost added to your bill) or you can purchase them in advance."
  }, {
    question: "Are your handymen employees or contractors?",
    answer: "All of our handymen are employees who have undergone thorough background checks, skills assessments, and training to ensure they meet our high standards for workmanship and customer service."
  }, {
    question: "Do you offer warranties on handyman work?",
    answer: "Yes, we provide a 1-year warranty on labor for most handyman services. This warranty covers any issues that arise due to the quality of our workmanship."
  }];
  const handymanEstimates = [{
    tier: "Small Jobs",
    price: "$65-125/hr",
    description: "For quick repairs",
    features: ["Skilled handyman", "Furniture assembly", "Picture hanging", "Minor repairs", "Small painting jobs", "Minimum 1-hour service"]
  }, {
    tier: "Medium Projects",
    price: "$125-250",
    description: "For standard maintenance",
    features: ["Experienced handyman", "Drywall repair", "Door installation", "Fixture replacement", "Deck repair", "Basic carpentry work", "2-4 hour projects"],
    recommended: true
  }, {
    tier: "Large Projects",
    price: "$250-500+",
    description: "For comprehensive improvements",
    features: ["Master handyman", "Bathroom updates", "Kitchen upgrades", "Custom shelving", "Multiple room painting", "Complex installations", "Full-day service options"]
  }];
  const commonHandymanNeeds = [{
    icon: <GalleryVerticalEnd className="h-8 w-8 text-primary" />,
    label: "Furniture Assembly"
  }, {
    icon: <PaintBucket className="h-8 w-8 text-primary" />,
    label: "Painting"
  }, {
    icon: <DoorOpen className="h-8 w-8 text-primary" />,
    label: "Door Repair"
  }, {
    icon: <Drill className="h-8 w-8 text-primary" />,
    label: "TV Mounting"
  }, {
    icon: <Lightbulb className="h-8 w-8 text-primary" />,
    label: "Light Fixtures"
  }, {
    icon: <Settings className="h-8 w-8 text-primary" />,
    label: "Drywall Repair"
  }, {
    icon: <Wrench className="h-8 w-8 text-primary" />,
    label: "Faucet Install"
  }, {
    icon: <Ruler className="h-8 w-8 text-primary" />,
    label: "Shelving"
  }, {
    icon: <Home className="h-8 w-8 text-primary" />,
    label: "Home Repairs"
  }, {
    icon: <MoreHorizontal className="h-8 w-8 text-primary" />,
    label: "Other"
  }];

  const handymanServiceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Handyman Services",
    "serviceType": "Home Repair and Maintenance",
    "provider": {
      "@type": "Organization",
      "name": "JobON",
      "url": "https://jobon.app"
    },
    "description": "Professional handyman services for homes, offices, and commercial properties, including furniture assembly, drywall, painting, and more.",
    "areaServed": "United States",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Handyman Services",
      "itemListElement": [{
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Furniture Assembly"
        }
      }, {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "TV Mounting"
        }
      }, {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Drywall Repair"
        }
      }, {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Door Installation"
        }
      }]
    }
  };
  return (
    <>
      <SEO title="Skilled Handyman Help Near You – Get Quick Local Bids" description="Need small fixes or larger home projects? Compare quotes from vetted handymen for furniture assembly, drywall, painting, and more—only on JobON." localBusinessSchema={true} serviceType="Handyman" serviceSlug="handyman" canonicalUrl="/services/handyman" />

      <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-background pt-16 pb-6 md:pt-24 md:pb-12">
        <div className="container mx-auto px-3 lg:px-8">
          {isMobile ? <div className="w-full">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-3">
                <div className="relative h-44 overflow-hidden">
                  <img 
                    src="/lovable-uploads/da9a5bc7-b0b5-491d-9915-bf4d664e87c8.png" 
                    alt="Professional handyman service" 
                    className="w-full h-full object-cover object-center"
                    style={{ objectPosition: "center 30%" }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent flex items-end">
                    <div className="p-3 text-white w-full">
                      <h1 className="text-xl font-bold mb-0.5 text-left">
                        Professional Handyman
                      </h1>
                      <h2 className="text-base font-medium text-white mb-1 text-left">
                        <span className="block">Expert Service</span>
                        <span className="block">Quality Results</span>
                      </h2>
                      <div className="flex items-center space-x-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((_, i) => <svg key={i} className="w-3 h-3 text-amber-400 fill-amber-400" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>)}
                        </div>
                        <span className="text-xs font-medium text-white">4.9/5 · 2,164 reviews</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-3">
                  <p className="text-sm text-gray-800 dark:text-gray-300 mb-3 font-medium">
                    Professional handyman services for homes, offices, and commercial projects
                  </p>

                  <div className="mb-3">
                    <div className="flex shadow-lg rounded-xl overflow-hidden">


                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Link to="/create-job" state={{ from: location.pathname }} className="flex-1">
                      <Button variant="default" size="default" className="w-full rounded-md font-semibold text-sm py-2">
                        Post a Job
                      </Button>
                    </Link>
                    <Link to="/professionals/handyman" className="flex-1">
                      <Button variant="outline" size="default" className="w-full rounded-md font-semibold text-sm py-2 text-gray-800 dark:text-white border-gray-400">
                        Browse Handymen
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-2 mb-2 flex items-center justify-between">
                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-green-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Vetted
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">
                      Handymen
                    </span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">Response</span>
                    <span className="font-bold text-xs text-black dark:text-white">&#60; 1hr</span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Award className="h-5 w-5 text-purple-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Satisfaction
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">Guaranteed</span>
                  </div>
                </div>
              </div>
            </div> : 
            <div className="flex flex-col lg:flex-row gap-12 items-center">
              <div className="w-full lg:w-1/2 space-y-8">
                <div className="space-y-4">
                  <span className="inline-block bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-hover px-4 py-1.5 rounded-full font-medium text-sm">Professional Handyman Services</span>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark-heading leading-tight">
                    Expert Handyman Services,
                    <br className="hidden md:inline" />
                    <span className="text-primary mt-2 block dark:text-primary-hover dark:dark-mode-text-shadow">
                      For Every Project.
                    </span>
                  </h1>
                  <div className="text-xl font-semibold text-gray-700 dark-subheading">
                    <div className="flex flex-col space-y-3 text-left">
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Home, Office & Commercial Services</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Licensed & Insured Professionals</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Same-Day Service Available</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">100% Satisfaction Guarantee</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Link to="/create-job" state={{ from: location.pathname }} className="w-full sm:w-auto">
                    <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Post a Project
                      <ArrowRight size={20} className="ml-2" />
                    </Button>
                  </Link>
                  <Link to="/professionals/handyman" className="w-full sm:w-auto">
                    <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Browse Handymen
                    </Button>
                  </Link>
                </div>

                <div className="pt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Instant Quotes</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Verified Handymen</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>100% Satisfaction</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>1-Year Warranty</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Transparent Pricing</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Bonded & Insured</span>
                  </div>
                </div>
              </div>

              <div className="w-full lg:w-1/2 relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/5 rounded-xl filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/cfb3e84d-6e02-41e2-9851-47eb7e6c9170.png" alt="Handyman shaking hands with happy clients" className="w-full h-full object-cover" />
                    </div>
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/bc6e2a3d-206f-4738-b872-c8c2a1d77aec.png" alt="Couple looking at color swatches for home improvement" className="w-full h-full object-cover" />
                    </div>
                    <div className="col-span-2 aspect-[16/9] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/5d8f1541-99e9-4653-9fcc-6f3c1052cd73.png" alt="Couple relaxing on couch in modern home" className="w-full h-full object-cover" />
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <div className="flex justify-center mb-2">
                      <div className="flex text-amber-400">
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">4.9/5 (2,164 reviews)</span>
                    </div>
                    <h3 className="font-bold text-xl mb-1 dark:text-white">Find Top Local Handymen</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Enter your ZIP code to get matched with professional handymen in your area</p>

                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                      <div className="flex-1 flex items-center bg-gray-50 dark:bg-gray-700 p-4">
                        <input type="text" placeholder="Enter your ZIP code" className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-300 text-lg" />
                      </div>
                      <Button type="submit" className="px-8 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all">
                        <span>Search</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>}
        </div>
      </section>

      <section className="py-6 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Common Handyman Services</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
              Select from our most requested handyman services
            </p>
          </div>

          <ServiceNeeds serviceId="handyman" needs={commonHandymanNeeds} estimates={handymanEstimates} />
        </div>
      </section>

      <section className="py-6 md:py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Recently Completed Handyman Projects</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Browse through recently completed handyman jobs by our network of professionals
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1513694203232-719a280e022f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Furniture assembly" className="w-full h-32 md:h-48 object-cover" />
              <div className="p-3 md:p-4">
                <div className="flex justify-between items-center mb-1 md:mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 days ago</span>
                </div>
                <h3 className="font-bold text-sm md:text-lg mb-1 md:mb-2 dark:text-white">Furniture Assembly & TV Mounting</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-2 md:mb-4">Assembly of 4 IKEA furniture pieces and mounting a 65" TV with cable management.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Oakland, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$285</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80" alt="Office maintenance" className="w-full h-32 md:h-48 object-cover" />
              <div className="p-3 md:p-4">
                <div className="flex justify-between items-center mb-1 md:mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">1 week ago</span>
                </div>
                <h3 className="font-bold text-sm md:text-lg mb-1 md:mb-2 dark:text-white">Office Workspace Setup</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-2 md:mb-4">Assembly and installation of 12 workstations including cable management and monitor mounts.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Francisco, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$1,250</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="/lovable-uploads/da9a5bc7-b0b5-491d-9915-bf4d664e87c8.png" alt="Commercial repair" className="w-full h-32 md:h-48 object-cover" />
              <div className="p-3 md:p-4">
                <div className="flex justify-between items-center mb-1 md:mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 weeks ago</span>
                </div>
                <h3 className="font-bold text-sm md:text-lg mb-1 md:mb-2 dark:text-white">Retail Store Renovation</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-2 md:mb-4">Complete shelving installation, lighting upgrades, and display setup for retail space.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Jose, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$3,500</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-6 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Latest Handyman Tips & Guides</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Expert advice and helpful resources for home maintenance and repairs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
            {handymanBlogPosts.map(post => <BlogCard key={post.id} post={post} />)}
          </div>

          <div className="text-center mt-6 md:mt-12">
            <Link to="/blog">
              <Button variant="default" size={isMobile ? "default" : "lg"} className="font-medium">
                View All Handyman Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <ServicePageTemplate 
        serviceId="handyman" 
        title="Professional Handyman Services" 
        subtitle="Skilled help for all your home repair and improvement needs" 
        description="From minor repairs to home improvements, our experienced handymen provide reliable, quality service for a wide range of household projects." 
        heroImage="https://images.unsplash.com/photo-1581579438747-1dc8d17bbce4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80" 
        benefits={benefits} 
        faqs={faqs} 
        estimates={handymanEstimates} 
        commonNeeds={[]} 
        hideEstimator={false} 
        hideHero={true} 
        professionalTitle="Handymen" 
        seoTitle="Skilled Handyman Help Near You | Get Quick Local Bids" 
        customCta={
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/create-job" state={{ from: location.pathname }} className="w-full sm:w-auto">
              <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Request a Handyman
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/professionals/handyman" className="w-full sm:w-auto">
              <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Browse Handymen
              </Button>
            </Link>
          </div>
        } 
      />
    </>
  );
};

export default HandymanService;
