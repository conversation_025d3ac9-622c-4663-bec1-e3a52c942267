import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { BlogCard } from '@/components/BlogCard';
import { Link } from 'react-router-dom';
import { ArrowRight, Clock, TrendingUp, Bookmark, Building, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Card, CardContent } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";
import { useIsMobile } from '@/hooks/use-mobile';
import { SEO } from "@/components/SEO.tsx";
import { fetchPosts, fetchFeaturedPosts, fetchCategories, BlogPost, WordPressCategory } from '@/services/wordpressApi';
const Blog: React.FC = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [featuredPosts, setFeaturedPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<WordPressCategory[]>([]);
  const [activeCategory, setActiveCategory] = useState<number | null>(null);
  const [activeCategoryName, setActiveCategoryName] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const isMobile = useIsMobile();
  console.log(categories);
  // Fetch posts and categories on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading(true);

        // Fetch featured posts
        const featured = await fetchFeaturedPosts(3);
        setFeaturedPosts(featured);

        // Fetch categories
        const cats = await fetchCategories();
        setCategories(cats);

        // Fetch initial posts
        const {
          posts: initialPosts,
          totalPages: pages
        } = await fetchPosts(1, 3, activeCategory || undefined);
        setPosts(initialPosts);
        setTotalPages(pages);
        setLoading(false);
      } catch (err) {
        setError('Failed to load blog data. Please try again later.');
        setLoading(false);
      }
    };
    loadInitialData();
  }, []);

  // Fetch posts when category or page changes
  useEffect(() => {
    const loadPosts = async () => {
      try {
        setLoading(true);
        const {
          posts: newPosts,
          totalPages: pages
        } = await fetchPosts(page, 10, activeCategory || undefined);
        setPosts(newPosts);
        setTotalPages(pages);
        setLoading(false);
      } catch (err) {
        setError('Failed to load blog posts. Please try again later.');
        setLoading(false);
      }
    };
    loadPosts();
  }, [activeCategory, page]);

  // Handle category change
  const handleCategoryChange = (categoryId: number | null, categoryName: string | null) => {
    setActiveCategory(categoryId);
    setActiveCategoryName(categoryName);
    setPage(1); // Reset to first page when changing category
  };

  // Load more posts
  const loadMorePosts = () => {
    if (page < totalPages) {
      setPage(page + 1);
    }
  };

  // Filter posts by category if one is selected
  const filteredPosts = posts;
  return <Layout>
    <SEO title="Home Service Tips, Trends & Guides – Blog for Pros & Customers" description="Explore expert tips and trends on hiring service pros, home maintenance, and growing your business. Updated weekly by the JobON editorial team." localBusinessSchema={true} serviceType="Blog" serviceSlug="blog" canonicalUrl="/blog" />
      {/* Modern News-style Hero with Carousel */}
      <section className="py-8 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl md:text-4xl font-bold">Latest Articles</h1>
            <div className="hidden md:flex items-center space-x-2">
              <Link to="/blog-template/choose-right-service-provider">
                <Button variant="outline" className="flex items-center gap-2">
                  Article Template
                  <Bookmark size={16} />
                </Button>
              </Link>
              <Link to="/blog/business-services">
                <Button variant="outline" className="flex items-center gap-2">
                  Business Services
                  <Building size={16} />
                </Button>
              </Link>
            </div>
          </div>

          {/* Error Message */}
          {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
              <p>{error}</p>
            </div>}

          {/* Loading State for Featured Posts */}
          {loading && featuredPosts.length === 0 ? <div className="flex justify-center items-center py-20">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading featured articles...</span>
            </div> : (/* Featured Articles Carousel */
        <div className="mb-12">
              <Carousel opts={{
            loop: true
          }} className="w-full">
                <CarouselContent>
                  {featuredPosts.map(post => <CarouselItem key={post.id} className="md:basis-1/1 lg:basis-1/1">
                      <Link to={`/blog/${post.slug}`} className="block">
                        <div className="relative overflow-hidden rounded-xl aspect-[16/9] md:aspect-[21/9]">
                          <img src={post.image} alt={post.title} className="w-full h-full object-cover transition-transform hover:scale-105 duration-500" />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent flex flex-col justify-end p-4 md:p-8">
                            <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">{post.title}</h2>
                            <p className="text-white/80 line-clamp-2 mb-4 max-w-2xl">{post.excerpt}</p>
                            <div className="flex items-center text-white/60 text-sm">
                              <Clock size={14} className="mr-1" />
                              <span>
                                {formatDate(post.date)} • {Math.ceil(post.excerpt.length / 20)} min read
                              </span>
                            </div>
                          </div>
                        </div>
                      </Link>
                    </CarouselItem>)}
                </CarouselContent>
                <div className="flex justify-center mt-4 gap-2">
                  <CarouselPrevious className="relative inset-0 translate-y-0 bg-white/80 dark:bg-gray-800/80 border border-gray-200 dark:border-gray-700" />
                  <CarouselNext className="relative inset-0 translate-y-0 bg-white/80 dark:bg-gray-800/80 border border-gray-200 dark:border-gray-700" />
                </div>
              </Carousel>
            </div>)}

          {/* Category Filter */}
          

          {/* Trending Section - Featured on mobile */}
          <div className="mb-12">
            <div className="flex items-center gap-2 mb-6">
              <TrendingUp size={24} className="text-primary" />
              <h2 className="text-2xl font-bold">Trending Now</h2>
            </div>

            {loading && posts.length === 0 ? <div className="flex justify-center items-center py-20">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Loading trending articles...</span>
              </div> : isMobile ?
          // Mobile version: Larger, full-width cards
          <div className="space-y-6">
                {filteredPosts.slice(0, 3).map(post => <Card key={post.id} className="overflow-hidden border-none shadow-lg hover:shadow-xl transition-shadow duration-300 w-full">
                    <Link to={`/blog/${post.slug}`} className="block">
                      <div className="aspect-[4/3] overflow-hidden">
                        <img src={post.image} alt={post.title} className="w-full h-full object-cover transition-transform hover:scale-105 duration-500" />
                      </div>
                    </Link>
                    <CardContent className="p-5">
                      <div className="flex justify-between items-center mb-3">
                        <span className="text-sm font-medium text-primary">
                          {post.category}
                        </span>
                        <div className="flex items-center text-muted-foreground text-xs">
                          <Clock size={12} className="mr-1" />
                          <span>{Math.ceil(post.excerpt.length / 20)} min read</span>
                        </div>
                      </div>

                      <Link to={`/blog/${post.slug}`} className="block">
                        <h3 className="text-xl font-bold mb-3 hover:text-primary transition-colors">
                          {post.title}
                        </h3>
                      </Link>

                      <p className="text-muted-foreground mb-4">
                        {post.excerpt}
                      </p>

                      <Link to={`/blog/${post.slug}`} className="inline-flex items-center text-primary hover:underline">
                        Read more <ArrowRight size={16} className="ml-1" />
                      </Link>
                    </CardContent>
                  </Card>)}
              </div> :
          // Desktop version: Grid layout
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredPosts.slice(0, 3).map(post => <Card key={post.id} className="overflow-hidden border-none shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <Link to={`/blog/${post.slug}`} className="block">
                      <div className="aspect-[16/9] overflow-hidden">
                        <img src={post.image} alt={post.title} className="w-full h-full object-cover transition-transform hover:scale-105 duration-500" />
                      </div>
                    </Link>
                    <CardContent className="p-5">
                      <div className="flex justify-between items-center mb-3">
                        <span className="text-xs font-medium text-primary">
                          {post.category}
                        </span>
                        <div className="flex items-center text-muted-foreground text-xs">
                          <Clock size={12} className="mr-1" />
                          <span>{Math.ceil(post.excerpt.length / 20)} min read</span>
                        </div>
                      </div>

                      <Link to={`/blog/${post.slug}`} className="block">
                        <h3 className="text-xl font-bold mb-2 line-clamp-2 hover:text-primary transition-colors">
                          {post.title}
                        </h3>
                      </Link>

                      <p className="text-muted-foreground text-sm line-clamp-2 mb-4">
                        {post.excerpt}
                      </p>

                      <Link to={`/blog/${post.slug}`} className="inline-flex items-center text-primary hover:underline">
                        Read more <ArrowRight size={16} className="ml-1" />
                      </Link>
                    </CardContent>
                  </Card>)}
              </div>}
          </div>

          {/* All Articles Section */}
          <div className="mb-16">
            <h2 className="text-2xl font-bold mb-6">All Articles</h2>

            {loading && posts.length === 0 ? <div className="flex justify-center items-center py-20">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Loading articles...</span>
              </div> : isMobile ?
          // Mobile version: stack cards vertically with larger thumbnails
          <div className="space-y-6">
                {filteredPosts.map(post => <BlogCard key={post.id} post={post} compact={false} />)}
              </div> :
          // Desktop version: Grid layout
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredPosts.map(post => <BlogCard key={post.id} post={post} />)}
              </div>}

            {/* Pagination */}
            {!loading && page < totalPages && <div className="flex justify-center mt-10 mb-8">
                <Button onClick={loadMorePosts} variant="outline" size="lg" className="flex items-center gap-2">
                  Load More Articles
                  {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                </Button>
              </div>}
          </div>
        </div>
      </section>
    </Layout>;
};
export default Blog;