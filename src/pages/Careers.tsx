import React, { useRef, useState } from 'react';
import { Layout } from '@/components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Briefcase, Users, Award, Rocket, Building, ArrowRight, Heart, Coffee, Lightbulb, Zap, ChevronRight, Clock, Flame, ChevronDown, ChevronUp } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import ApplicationForm from '@/components/ApplicationForm';
import {SEO} from "@/components/SEO.tsx";
import { useIsMobile } from '@/hooks/use-mobile';

const PRIMARY_COLOR = "#9b87f5";

const Careers = () => {
  const jobApplicationRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const scrollToJobApplication = (e?: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e?.preventDefault?.();
    const el = document.getElementById('job-application');
    if (el) {
      el.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  const benefits = [
    {
      icon: <Rocket className="h-8 w-8 text-primary" />,
      title: "Growth Opportunities",
      description: "Continuous learning and advancement within a rapidly growing startup environment."
    },
    {
      icon: <Users className="h-8 w-8 text-primary" />,
      title: "Collaborative Culture",
      description: "Work with talented individuals who share your passion for excellence and innovation."
    },
    {
      icon: <Lightbulb className="h-8 w-8 text-primary" />,
      title: "Impact-Driven Work",
      description: "Make meaningful contributions where your ideas directly shape our products and future."
    },
    {
      icon: <Coffee className="h-8 w-8 text-primary" />,
      title: "Flexible Environment",
      description: "Enjoy a dynamic workspace with the freedom to work in ways that suit your style."
    }
  ];

  if (isMobile) {
    return (
      <Layout>
        <SEO
          title="Careers at JobON | Join Our Mission to Revolutionize Home Services"
          description="Explore exciting career opportunities at JobON. Join a passionate, fast-growing team building tools that empower service professionals and homeowners nationwide."
          localBusinessSchema={true}
          serviceType="careers"
          serviceSlug="careers"
          canonicalUrl="/careers"
        />
        
        {/* Mobile Hero Section - Fixed spacing */}
        <section className="pt-4 pb-6">
          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold leading-tight">
              Join our team and <span className="text-primary">shape the future</span>
            </h1>
            <p className="text-lg text-foreground/70 px-4">
              We're looking for passionate individuals who thrive in fast-paced environments.
            </p>
            <div className="flex flex-col gap-3 px-4">
              <Button 
                size="lg" 
                className="w-full text-lg font-medium py-4 rounded-xl"
                onClick={scrollToJobApplication}
              >
                Apply Now
              </Button>
            </div>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4 mt-6 px-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">15+</div>
                <p className="text-sm text-foreground/70">Open Positions</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">24h</div>
                <p className="text-sm text-foreground/70">Response Time</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">100%</div>
                <p className="text-sm text-foreground/70">Remote Friendly</p>
              </div>
            </div>
          </div>
        </section>

        {/* Mobile Navigation - Fixed position */}
        <div className="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 rounded-xl p-3 mb-6 sticky top-4 z-10 mx-4">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <button
              onClick={() => toggleSection('benefits')}
              className="flex items-center justify-center gap-1 text-primary hover:underline font-medium py-2 px-3 rounded-lg bg-primary/5"
            >
              Why Join Us
            </button>
            <button
              onClick={() => toggleSection('culture')}
              className="flex items-center justify-center gap-1 text-primary hover:underline font-medium py-2 px-3 rounded-lg bg-primary/5"
            >
              Our Culture
            </button>
            <button
              onClick={() => toggleSection('reality')}
              className="flex items-center justify-center gap-1 text-primary hover:underline font-medium py-2 px-3 rounded-lg bg-primary/5"
            >
              Startup Life
            </button>
            <button
              onClick={scrollToJobApplication}
              className="flex items-center justify-center gap-1 text-primary hover:underline font-medium py-2 px-3 rounded-lg bg-primary/5"
            >
              Apply Now
            </button>
          </div>
        </div>

        {/* Expandable Content Sections */}
        <div className="px-4 space-y-4">
          
          {/* Why Join Us - Collapsible */}
          <Card className="overflow-hidden">
            <CardHeader 
              className="cursor-pointer"
              onClick={() => toggleSection('benefits')}
            >
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl">Why Join Us</CardTitle>
                {expandedSection === 'benefits' ? <ChevronUp /> : <ChevronDown />}
              </div>
            </CardHeader>
            {expandedSection === 'benefits' && (
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 gap-4">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                      <div className="flex-shrink-0 mt-1">{benefit.icon}</div>
                      <div>
                        <h3 className="font-semibold mb-1">{benefit.title}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-300">{benefit.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            )}
          </Card>

          {/* Our Culture - Collapsible */}
          <Card className="overflow-hidden">
            <CardHeader 
              className="cursor-pointer"
              onClick={() => toggleSection('culture')}
            >
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl">Our Culture</CardTitle>
                {expandedSection === 'culture' ? <ChevronUp /> : <ChevronDown />}
              </div>
            </CardHeader>
            {expandedSection === 'culture' && (
              <CardContent className="pt-0">
                <div className="space-y-4">
                  <p className="text-gray-600 dark:text-gray-300">
                    At JobON, we believe in building not just a company, but a community of passionate individuals who want 
                    to create something meaningful together.
                  </p>
                  
                  <div className="space-y-3">
                    <div className="flex items-start gap-3 p-3 bg-primary/5 rounded-lg">
                      <Coffee className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold">Startup Energy</h3>
                        <p className="text-sm text-foreground/70">Experience the thrill of building something from the ground up.</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3 p-3 bg-primary/5 rounded-lg">
                      <Users className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold">Family Feel</h3>
                        <p className="text-sm text-foreground/70">Join a close-knit team that supports each other.</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3 p-3 bg-primary/5 rounded-lg">
                      <Lightbulb className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold">Creative Freedom</h3>
                        <p className="text-sm text-foreground/70">We value fresh ideas and give you autonomy to experiment.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>

          {/* Startup Reality - Collapsible */}
          <Card className="overflow-hidden">
            <CardHeader 
              className="cursor-pointer"
              onClick={() => toggleSection('reality')}
            >
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl">The Startup Reality</CardTitle>
                {expandedSection === 'reality' ? <ChevronUp /> : <ChevronDown />}
              </div>
            </CardHeader>
            {expandedSection === 'reality' && (
              <CardContent className="pt-0">
                <div className="space-y-4">
                  <p className="text-foreground/70 text-sm">
                    We believe in transparency about what startup life really means. It's not for everyone, and that's okay.
                  </p>
                  
                  <div className="space-y-3">
                    <div className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border-l-4 border-orange-400">
                      <div className="flex items-center gap-2 mb-2">
                        <Clock className="h-4 w-4 text-orange-600" />
                        <h3 className="font-semibold text-orange-800 dark:text-orange-200">Beyond 9-to-5</h3>
                      </div>
                      <p className="text-sm text-orange-700 dark:text-orange-300">
                        When inspiration strikes or deadlines approach, you might work late. But you'll have flexibility and see your ideas come to life.
                      </p>
                    </div>
                    
                    <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border-l-4 border-red-400">
                      <div className="flex items-center gap-2 mb-2">
                        <Flame className="h-4 w-4 text-red-600" />
                        <h3 className="font-semibold text-red-800 dark:text-red-200">High Stakes, High Rewards</h3>
                      </div>
                      <p className="text-sm text-red-700 dark:text-red-300">
                        If you thrive under pressure and love solving problems others haven't cracked, this is your playground.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        </div>

        {/* Application Section */}
        <div id="job-application" ref={jobApplicationRef} className="px-4 mt-8 mb-6">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold mb-3">Ready to Join Us?</h2>
            <p className="text-foreground/70">
              Apply now and be part of our journey to revolutionize home services.
            </p>
          </div>
          <div
            style={{
              border: `1.5px solid ${PRIMARY_COLOR}`
            }}
            className="rounded-lg shadow-sm p-4 bg-white dark:bg-background"
          >
            <ApplicationForm />
          </div>
        </div>

        {/* Final CTA */}
        <div className="bg-primary/5 dark:bg-gray-800 p-6 rounded-2xl text-center mx-4 mb-6">
          <h2 className="text-xl font-bold mb-3">Ready for the challenge?</h2>
          <p className="text-foreground/70 mb-4 text-sm">
            If you're excited about building something meaningful and don't mind the intensity of startup life, we want to hear from you!
          </p>
          <Button 
            className="w-full py-3"
            onClick={scrollToJobApplication}
          >
            Accept the Challenge
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <SEO
          title="Careers at JobON | Join Our Mission to Revolutionize Home Services"
          description="Explore exciting career opportunities at JobON. Join a passionate, fast-growing team building tools that empower service professionals and homeowners nationwide."
          localBusinessSchema={true}
          serviceType="careers"
          serviceSlug="careers"
          canonicalUrl="/careers"
      />
      <section className="relative pt-32 pb-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-white to-primary/10 dark:from-primary/10 dark:via-background dark:to-primary/5 z-0"></div>
        
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute -top-24 -right-24 w-96 h-96 bg-primary/10 rounded-full filter blur-3xl opacity-70 animate-pulse" style={{ animationDuration: '25s' }}></div>
          <div className="absolute top-1/3 -left-24 w-80 h-80 bg-primary/5 rounded-full filter blur-3xl opacity-60 animate-pulse" style={{ animationDuration: '20s' }}></div>
          <div className="absolute -bottom-40 left-1/4 w-72 h-72 bg-primary/8 rounded-full filter blur-3xl opacity-50 animate-pulse" style={{ animationDuration: '30s' }}></div>
          
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMyMDIwMjAiIGZpbGwtb3BhY2l0eT0iMC4wMSI+PHBhdGggZD0iTTM2IDM0aDR2MWgtNHYtMXptMC0yaDF2NEgzMXYtNHptMi0xaDR2MWgtNHYtMXptMCAzaDR2MWgtNHYtMXptLTQtMWgxdjJoLTF2LTJ6bTUtNGg0djFoLTR2LTF6bTAgM2g0djFoLTR2LTF6bS00LTFoMXYyaC0xdi0yek0xMSAxOWg0djFoLTR2LTF6bTAtMmgxdjRoLTF2LTR6bTItMWg0djFoLTR2LTF6bTAgM2g0djFoLTR2LTF6bS00LTFoMXYyaC0xdi0yek01MSAyOWg0djFoLTR2LTF6bTAtMmgxdjRoLTF2LTR6bTItMWg0djFoLTR2LTF6bTAgM2g0djFoLTR2LTF6bS00LTFoMXYyaC0xdi0yek01MSA0OWg0djFoLTR2LTF6bTAtMmgxdjRoLTF2LTR6bTItMWg0djFoLTR2LTF6bTAgM2g0djFoLTR2LTF6bS00LTFoMXYyaC0xdi0yek01MSA5aDR2MWgtNHYtMXptMC0yaDF2NEgzMXYtNHptMi0xaDR2MWgtNHYtMXptMCAzaDR2MWgtNHYtMXptLTQtMWgxdjJoLTF2LTJ6bS0yMC0yaDF2NEgzMXYtNHptMi0xaDR2MWgtNHYtMXptMCAzaDR2MWgtNHYtMXptLTQtMWgxdjJoLTF2LTJ6Ii8+PC9nPjwvZz48L3N2Zz4=')] opacity-30 dark:opacity-10"></div>
        </div>

        <div className="container mx-auto px-6 md:px-12 relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="w-full lg:w-1/2 space-y-8 text-center lg:text-left animate-fade-in opacity-0" style={{ animationDelay: '0.2s', animationFillMode: 'forwards' }}>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight">
                Join our team and <span className="text-primary block mt-2">shape the future</span>
              </h1>
              <p className="text-xl text-foreground/70 max-w-2xl mx-auto lg:mx-0">
                We're looking for passionate individuals who thrive in fast-paced environments and aren't afraid of the 
                intensity that comes with building something revolutionary from scratch.
              </p>
              <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                <Button 
                  size="lg" 
                  className="text-lg font-medium px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl transition-all"
                  onClick={scrollToJobApplication}
                  scrollToTop={false}
                >
                  Apply Now
                </Button>
                <Button 
                  variant="outline" 
                  size="lg" 
                  className="text-lg font-medium px-8 py-6 h-auto rounded-xl shadow hover:shadow-lg transition-all"
                  onClick={scrollToJobApplication}
                  scrollToTop={false}
                >
                  Our Culture
                </Button>
              </div>
              <div className="flex flex-wrap gap-6 justify-center lg:justify-start pt-4">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <Heart className="h-5 w-5 text-primary" />
                  </div>
                  <span className="font-medium">Supportive Team</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <Rocket className="h-5 w-5 text-primary" />
                  </div>
                  <span className="font-medium">Fast-Growing</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <Flame className="h-5 w-5 text-primary" />
                  </div>
                  <span className="font-medium">High-Intensity</span>
                </div>
              </div>
            </div>
            
            <div className="w-full lg:w-1/2 animate-fade-in opacity-0" style={{ animationDelay: '0.4s', animationFillMode: 'forwards' }}>
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/5 rounded-2xl filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 rounded-2xl p-5 shadow-lg border border-gray-100 dark:border-gray-700">
                  <img 
                    src="https://images.unsplash.com/photo-1603201667141-5a2d4c673378?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2096&q=80" 
                    alt="JobON Team" 
                    className="w-full h-auto rounded-xl object-cover aspect-[4/3] mb-6"
                  />
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary">15+</div>
                      <p className="text-sm text-foreground/70">Open Positions</p>
                    </div>
                    <div className="text-center border-x border-gray-200 dark:border-gray-700">
                      <div className="text-3xl font-bold text-primary">24h</div>
                      <p className="text-sm text-foreground/70">Response Time</p>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary">100%</div>
                      <p className="text-sm text-foreground/70">Remote Friendly</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 rounded-xl p-4 mb-10 sticky top-20 z-10 container mx-auto px-6 md:px-12">
        <nav>
          <ul className="flex flex-wrap justify-center gap-3 md:gap-8 text-sm md:text-base">
            <li>
              <a
                href="#job-application"
                onClick={scrollToJobApplication}
                className="flex items-center gap-1 text-primary hover:underline font-medium py-1"
              >
                Why Join Us
              </a>
            </li>
            <li>
              <a
                href="#job-application"
                onClick={scrollToJobApplication}
                className="flex items-center gap-1 text-primary hover:underline font-medium py-1"
              >
                Our Culture
              </a>
            </li>
            <li>
              <a
                href="#job-application"
                onClick={scrollToJobApplication}
                className="flex items-center gap-1 text-primary hover:underline font-medium py-1"
              >
                Our Values
              </a>
            </li>
            <li>
              <a
                href="#job-application"
                onClick={scrollToJobApplication}
                className="flex items-center gap-1 text-primary hover:underline font-medium py-1"
              >
                Apply Now
              </a>
            </li>
          </ul>
        </nav>
      </div>

      <div className="container mx-auto px-6 md:px-12">
        <div className="flex items-center mb-16">
          <Separator className="flex-grow" />
          <span className="px-4 text-foreground/50 text-sm font-medium uppercase tracking-wider">Join Our Team</span>
          <Separator className="flex-grow" />
        </div>

        <div id="why-join" className="mb-20 scroll-mt-24">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900 dark:text-white">Why Join Us</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="border border-gray-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="mb-4">{benefit.icon}</div>
                  <CardTitle className="text-xl">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 dark:text-gray-300">
                    {benefit.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div id="our-culture" className="mb-20 scroll-mt-24">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-block mb-4 bg-gradient-to-r from-primary/10 to-primary/20 py-2 px-4 rounded-md text-primary font-medium">
                Our Culture
              </div>
              <h2 className="text-3xl font-semibold mb-6 text-gray-900 dark:text-white">A place where you belong</h2>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                At JobON, we believe in building not just a company, but a community of passionate individuals who want 
                to create something meaningful together. Our startup culture is built on trust, creativity, and initiative.
              </p>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We're a small team with big ambitions, which means you'll wear many hats and tackle diverse challenges.
                Everyone's voice is heard here, and we celebrate the unique perspectives that each team member brings to the table.
              </p>
              <Link to="/about-us">
                <Button variant="outline" className="group">
                  Learn more about us
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>
            <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-none shadow-md">
              <CardContent className="p-10">
                <div className="space-y-10">
                  <div>
                    <div className="flex items-center mb-4">
                      <Coffee className="h-6 w-6 text-primary mr-2" />
                      <h3 className="text-2xl font-semibold">Startup Energy</h3>
                    </div>
                    <p className="text-foreground/70 text-lg">
                      Experience the thrill of building something from the ground up, where your contributions have immediate and visible impact.
                    </p>
                  </div>
                  <div>
                    <div className="flex items-center mb-4">
                      <Users className="h-6 w-6 text-primary mr-2" />
                      <h3 className="text-2xl font-semibold">Family Feel</h3>
                    </div>
                    <p className="text-foreground/70 text-lg">
                      Join a close-knit team that supports each other through challenges and celebrates every win together.
                    </p>
                  </div>
                  <div>
                    <div className="flex items-center mb-4">
                      <Lightbulb className="h-6 w-6 text-primary mr-2" />
                      <h3 className="text-2xl font-semibold">Creative Freedom</h3>
                    </div>
                    <p className="text-foreground/70 text-lg">
                      We value fresh ideas and give you the autonomy to experiment, learn, and implement innovative solutions.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div id="make-or-break" className="mb-20 scroll-mt-24">
          <div className="text-center mb-16">
            <div className="inline-block mb-4 bg-gradient-to-r from-primary/10 to-primary/20 py-2 px-4 rounded-md text-primary font-medium">
              The Startup Reality
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Make or Break: The Startup Experience</h2>
            <p className="text-lg md:text-xl text-foreground/70 max-w-3xl mx-auto">
              We believe in transparency about what startup life really means. It's not for everyone, and that's okay.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
            <Card className="bg-white dark:bg-gray-800 shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
              <div className="h-2 bg-gradient-to-r from-primary to-primary/70"></div>
              <CardContent className="p-8">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center">
                    <Clock className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold">Beyond 9-to-5</h3>
                    <p className="text-foreground/70">Passion doesn't clock out</p>
                  </div>
                </div>
                <p className="text-foreground/80 mb-4">
                  Let's be honest—we don't have traditional work-life balance here. What we have is something 
                  more exciting: a chance to pour your energy into work that genuinely matters to you.
                </p>
                <p className="text-foreground/80">
                  When deadlines approach or inspiration strikes, you might find yourself working late or on weekends. 
                  But you'll also have the flexibility to take time when you need it, and the satisfaction of seeing your 
                  ideas come to life in real-time.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
              <div className="h-2 bg-gradient-to-r from-primary to-primary/70"></div>
              <CardContent className="p-8">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center">
                    <Flame className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold">High Stakes, High Rewards</h3>
                    <p className="text-foreground/70">For those who thrive under pressure</p>
                  </div>
                </div>
                <p className="text-foreground/80 mb-4">
                  Startup environments are make-or-break by nature. We face real challenges, tight deadlines, and 
                  the exhilarating uncertainty that comes with building something new.
                </p>
                <p className="text-foreground/80">
                  If you're the kind of person who comes alive when the pressure is on, who loves solving 
                  problems that others haven't cracked yet, and who wants to look back and say "I helped build that 
                  from scratch"—this is your playground.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="bg-primary/5 rounded-xl p-8 mt-10">
            <div className="flex flex-col md:flex-row items-center gap-6">
              <div className="w-14 h-14 md:w-16 md:h-16 rounded-full bg-primary/10 flex-shrink-0 flex items-center justify-center">
                <Zap className="h-8 w-8 md:h-10 md:w-10 text-primary" />
              </div>
              <div>
                <h3 className="text-xl md:text-2xl font-semibold mb-2">Is this right for you?</h3>
                <p className="text-foreground/80">
                  If you're looking for predictable hours and well-defined responsibilities, we might not be the right fit. 
                  But if you're excited by the idea of building something extraordinary, having outsized impact, and growing 
                  alongside a passionate team—even if it means some late nights and weekend work—we can't wait to meet you.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div id="our-values" className="mb-20 scroll-mt-24">
          <div className="text-center mb-16">
            <div className="inline-block mb-4 bg-gradient-to-r from-primary/10 to-primary/20 py-2 px-4 rounded-md text-primary font-medium">
              Our Values
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">What drives us daily</h2>
            <p className="text-lg md:text-xl text-foreground/70 max-w-3xl mx-auto">
              These core principles guide how we work, make decisions, and grow together as a team.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-md transition-all duration-300 h-full">
              <CardContent className="p-8">
                <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  <Zap className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Agility</h3>
                <p className="text-foreground/70">
                  We move fast, adapt quickly, and embrace change as an opportunity to innovate and improve.
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-md transition-all duration-300 h-full">
              <CardContent className="p-8">
                <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  <Users className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Collaboration</h3>
                <p className="text-foreground/70">
                  We achieve more together, sharing knowledge openly and supporting each other to reach common goals.
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-md transition-all duration-300 h-full">
              <CardContent className="p-8">
                <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  <Heart className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Passion</h3>
                <p className="text-foreground/70">
                  We care deeply about what we build, bringing enthusiasm and dedication to every task, no matter how small.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        <div id="application" ref={jobApplicationRef} className="mb-20 scroll-mt-24">
          <div className="text-center mb-16">
            <div className="inline-block mb-4 bg-gradient-to-r from-primary/10 to-primary/20 py-2 px-4 rounded-md text-primary font-medium">
              Apply Now
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Be part of our journey</h2>
            <p className="text-lg md:text-xl text-foreground/70 max-w-3xl mx-auto">
              We're looking for talented individuals who thrive in startup environments and want to make an impact.
            </p>
          </div>
          <div
            id="job-application"
            style={{
              border: `1.5px solid ${PRIMARY_COLOR}`
            }}
            className="max-w-3xl mx-auto rounded-lg shadow-sm p-8 bg-white dark:bg-background"
          >
            <ApplicationForm />
          </div>
        </div>

        <div className="bg-primary/5 dark:bg-gray-800 p-12 rounded-2xl text-center mb-20">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready for the challenge?</h2>
          <p className="text-lg md:text-xl text-foreground/70 max-w-2xl mx-auto mb-4">
            If you're excited about building something meaningful from the ground up, solving interesting problems,
            and don't mind the intensity that comes with startup life, we want to hear from you!
          </p>
          <p className="text-foreground/70 max-w-2xl mx-auto mb-10">
            This journey isn't for everyone—and that's okay. But for those who thrive in high-energy environments
            and want their work to matter, there's no more exciting place to be.
          </p>
          <Button 
            size="lg" 
            className="px-10 py-6 text-lg h-auto"
            onClick={scrollToJobApplication}
            scrollToTop={false}
          >
            Accept the Challenge
          </Button>
        </div>
      </div>
    </Layout>
  );
};

export default Careers;
