
import React, {useEffect, useState} from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {<PERSON>, CardContent, CardHeader, CardTitle} from "@/components/ui/card.tsx";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table.tsx";
import {Badge} from "@/components/ui/badge.tsx";
import {useAuth} from "@/features/auth/hooks/useAuth.ts";
import {Pagination} from "@/components/ui/pagination.tsx";
import BusinessDetailsDialog from "@/components/BusinessDetailsDialog";
import {Eye, Filter, Search, X, Building2} from "lucide-react";
import {apiService} from "@/services/api.ts";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { Input } from "@/components/ui/input";
import { useDebounceValue } from "@/hooks/use-debounce";

export interface BusinessType { // Added export keyword
    businessId: string;
    name: string;
    category: string;
    location: string;
    address: string;
    phone: string;
    website: string;
    email: string;
    lat?: string; // Added lat
    lng?: string; // Added lng
    hours: {
        monday: string;
        tuesday: string;
        wednesday: string;
        thursday: string;
        friday: string;
        saturday: string;
        sunday: string;
    };
    photos: string[];
    services: never[];
    reviews: {
        text: string; rating: string; author: string; date: string }[];
    createdAt: string;
    updatedAt: string;
}

interface ResponseType {
    success: boolean;
    message?: string;
    data: BusinessType[];
    pagination: {
        current_page: number;
        per_page: number;
        total: number;
        last_page: number;
    };
}

const Business = () => {
    const { token } = useAuth();
    const [dataTable, setDataTable] = useState<BusinessType[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current_page: 1,
        per_page: 15,
        total: 0,
        last_page: 1
    });
    const [selectedBusiness, setSelectedBusiness] = useState<BusinessType | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [zipCode, setZipCode] = useState('');
    const { isMobile } = useUIHelpers();

    // Create debounced versions of the search inputs
    const debouncedSearchQuery = useDebounceValue(searchQuery, 800);
    const debouncedZipCode = useDebounceValue(zipCode, 800);

    useEffect(() => {
      setCurrentPage(1);
    }, [debouncedSearchQuery, debouncedZipCode]);

    useEffect(() => {
        const url = new URL(window.location.href);

        // Get page parameter
        const pageParam = url.searchParams.get('page');
        if (pageParam) {
            const page = parseInt(pageParam, 10);
            if (!isNaN(page) && page > 0) {
                setCurrentPage(page);
            }
        }

        // Get search parameter
        const searchParam = url.searchParams.get('search');
        if (searchParam) {
            setSearchQuery(searchParam);
        }

        // Get zip code parameter
        const zipParam = url.searchParams.get('zip_code');
        if (zipParam) {
            setZipCode(zipParam);
        }
    }, []);

    useEffect(() => {
        async function getData() {
            setIsLoading(true);
            try {
                // Build the API endpoint with filter parameters
                let endpoint = `/api/businesses?page=${currentPage}`;

                // Add search parameter if it exists
                if (debouncedSearchQuery) {
                    endpoint += `&search=${encodeURIComponent(debouncedSearchQuery)}`;
                }

                // Add zip code parameter if it exists
                if (debouncedZipCode) {
                    endpoint += `&zip_code=${encodeURIComponent(debouncedZipCode)}`;
                }

                const response = await apiService<ResponseType>(endpoint, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization' : token || ''
                    },
                });

                if (!response.data?.success) {
                    throw new Error(`Response status: ${response.status}`);
                }

                const json = response.data;
                if (!json) {
                    throw new Error('No data received');
                }

                // Store pagination metadata
                setPagination({
                    current_page: json.pagination?.current_page || 1,
                    per_page: json.pagination?.per_page || 15,
                    total: json.pagination?.total || 0,
                    last_page: json.pagination?.last_page || 1
                });

                const data = json.data || [];
                setDataTable(data)
            } catch (error) {
                console.error((error as Error).message || 'Unknown error');
            } finally {
                setIsLoading(false);
            }
        }
        getData()

        // Update URL parameters to reflect current filters
        const url = new URL(window.location.href);

        // Update page parameter
        url.searchParams.set('page', currentPage.toString());

        // Update or remove search parameter
        if (debouncedSearchQuery) {
            url.searchParams.set('search', debouncedSearchQuery);
        } else {
            url.searchParams.delete('search');
        }

        // Update or remove zip code parameter
        if (debouncedZipCode) {
            url.searchParams.set('zip_code', debouncedZipCode);
        } else {
            url.searchParams.delete('zip_code');
        }

        // Update URL without reloading the page
        window.history.pushState({}, '', url);

    }, [currentPage, token, debouncedSearchQuery, debouncedZipCode])

    // Handler for page changes
    const handlePageChange = (page: number) => {
        // Update URL parameter without reload
        const url = new URL(window.location.href);

        // Update page parameter
        url.searchParams.set('page', page.toString());

        // Preserve search parameter if it exists
        if (debouncedSearchQuery) {
            url.searchParams.set('search', debouncedSearchQuery);
        } else {
            url.searchParams.delete('search');
        }

        // Preserve zip code parameter if it exists
        if (debouncedZipCode) {
            url.searchParams.set('zip_code', debouncedZipCode);
        } else {
            url.searchParams.delete('zip_code');
        }

        window.history.pushState({}, '', url);

        // Update state
        setCurrentPage(page);
        window.scrollTo(0, 0);
    };

    // We're now using server-side filtering, so we don't need to filter the data client-side
    const filteredData = dataTable;

    const getBusinessInitials = (name: string) => {
        const words = name.split(' ');
        if (words.length >= 2) {
            return (words[0][0] + words[1][0]).toUpperCase();
        }
        return name.charAt(0).toUpperCase();
    };

    const getStatusBadge = (business: BusinessType) => {
        // Simple logic to determine status based on available data
        const hasWebsite = business.website && business.website !== '';
        const hasPhone = business.phone && business.phone !== '';
        const hasEmail = business.email && business.email !== '';

        if (hasWebsite && hasPhone && hasEmail) {
            return <Badge className="bg-green-500 hover:bg-green-600 text-white font-medium">Active</Badge>;
        } else if (hasPhone || hasEmail) {
            return <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white font-medium">Partial</Badge>;
        } else {
            return <Badge className="bg-red-500 hover:bg-red-600 text-white font-medium">Inactive</Badge>;
        }
    };

    // Function to get gradient class based on category
    const getGradientClass = (category: string) => {
        if (!category) return "bg-gradient-to-r from-gray-200 to-gray-300";

        const serviceCategory = category.toLowerCase();
        if (serviceCategory.includes('plumb')) {
            return "bg-gradient-to-r from-blue-100 to-blue-200 border-l-4 border-blue-500";
        } else if (serviceCategory.includes('electric')) {
            return "bg-gradient-to-r from-yellow-100 to-yellow-200 border-l-4 border-yellow-500";
        } else if (serviceCategory.includes('clean')) {
            return "bg-gradient-to-r from-green-100 to-green-200 border-l-4 border-green-500";
        } else if (serviceCategory.includes('repair')) {
            return "bg-gradient-to-r from-orange-100 to-orange-200 border-l-4 border-orange-500";
        } else {
            return "bg-gradient-to-r from-purple-100 to-purple-200 border-l-4 border-purple-500";
        }
    };

    // Render mobile business card
    const BusinessCard = ({ business }: { business: BusinessType }) => {
        const serviceGradient = getGradientClass(business?.category);

        return (
            <div className={`mb-4 rounded-lg shadow-sm overflow-hidden ${serviceGradient}`}>
                <div className="p-4">
                    <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                            <h3 className="font-medium text-gray-900 truncate">{business.name || "Unnamed Business"}</h3>
                            <p className="text-xs text-gray-600 mt-0.5 truncate">ID: {business.businessId.substring(0, 8)}...</p>
                        </div>
                        <div>
                            {getStatusBadge(business)}
                        </div>
                    </div>

                    <div className="flex items-center gap-2 mb-3 bg-white/50 p-2 rounded-md">
                        <Avatar className="h-8 w-8 border border-gray-200">
                            <AvatarImage src={business.photos?.[0]} />
                            <AvatarFallback>{getBusinessInitials(business.name)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{business.category}</p>
                            <div className="flex items-center text-xs text-gray-500">
                                <Building2 className="h-3 w-3 mr-1" />
                                <span>{business.location}</span>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-between items-center">
                        <div>
                            <span className="text-xs bg-white/70 px-2 py-1 rounded-full font-medium">
                                {business.category}
                            </span>
                        </div>
                        <Button
                            size="sm"
                            className="bg-indigo-600 hover:bg-indigo-700 text-white flex items-center gap-1"
                            onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                                e.stopPropagation();
                                setSelectedBusiness(business);
                                setIsDialogOpen(true);
                            }}
                        >
                            <Eye className="w-3 h-3" />
                            <span>View</span>
                        </Button>
                    </div>
                </div>
            </div>
        );
    };

    return <>
        <Card className="shadow-md">
            <CardHeader className="pb-3">
                <CardTitle className="text-xl md:text-2xl flex justify-between items-center">
                    <span>Business Management</span>
                    {isMobile && (
                        <Button variant="outline" size="icon" className="h-8 w-8">
                            <Filter className="h-4 w-4" />
                        </Button>
                    )}
                </CardTitle>

                {isMobile && (
                    <div className="space-y-2 mt-2">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            <Input
                                className="pl-9 pr-8 py-2 rounded-full bg-gray-50 border-gray-200"
                                placeholder="Search businesses..."
                                value={searchQuery}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                            />
                            {searchQuery && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7"
                                    onClick={() => setSearchQuery('')}
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            )}
                        </div>
                        <div className="relative">
                            <Input
                                className="pl-3 pr-8 py-2 rounded-full bg-gray-50 border-gray-200"
                                placeholder="Filter by zip code..."
                                value={zipCode}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setZipCode(e.target.value)}
                            />
                            {zipCode && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7"
                                    onClick={() => setZipCode('')}
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            )}
                        </div>
                    </div>
                )}
                {!isMobile && (
                    <div className="flex gap-4 mt-4">
                        <div className="relative flex-1">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            <Input
                                className="pl-9 pr-8 py-2 bg-gray-50 border-gray-200"
                                placeholder="Search businesses..."
                                value={searchQuery}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                            />
                            {searchQuery && (
                                <div
                                    className="absolute right-1 top-1/2 transform -translate-y-1/2 cursor-pointer mr-3"
                                    onClick={() => setSearchQuery('')}
                                >
                                    <X className="h-4 w-4" />
                                </div>
                            )}
                        </div>
                        <div className="relative w-1/3">
                            <Input
                                className="pl-3 pr-8 py-2 bg-gray-50 border-gray-200"
                                placeholder="Filter by zip code..."
                                value={zipCode}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setZipCode(e.target.value)}
                            />
                            {zipCode && (
                                <div
                                  className="absolute right-1 top-1/2 transform -translate-y-1/2 cursor-pointer mr-3"
                                    onClick={() => setZipCode('')}
                                >
                                    <X className="h-4 w-4" />
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </CardHeader>
            <CardContent>
                {isLoading ? (
                    <div className="flex justify-center items-center py-8">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                    </div>
                ) : isMobile ? (
                    // Mobile view with cards
                    <div className="space-y-1">
                        {filteredData.length === 0 ? (
                            <div className="text-center py-10 bg-gray-50 rounded-lg">
                                <p className="text-gray-500">No businesses found</p>
                                {searchQuery && (
                                    <Button
                                        variant="link"
                                        onClick={() => setSearchQuery('')}
                                        className="mt-2"
                                    >
                                        Clear search
                                    </Button>
                                )}
                            </div>
                        ) : (
                            filteredData.map((business) => (
                                <BusinessCard key={business.businessId} business={business} />
                            ))
                        )}
                    </div>
                ) : (
                    // Desktop view with table
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Business</TableHead>
                                <TableHead>Category</TableHead>
                                <TableHead>Location</TableHead>
                                <TableHead>Contact</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredData.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="text-center py-8">
                                        No businesses found
                                    </TableCell>
                                </TableRow>
                            ) : (
                                filteredData.map((business) => {
                                    return <TableRow key={business.businessId}>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <Avatar className="h-8 w-8">
                                                    <AvatarImage src={business.photos?.[0]} />
                                                    <AvatarFallback>{getBusinessInitials(business.name)}</AvatarFallback>
                                                </Avatar>
                                                <div>
                                                    <div className="font-medium">{business.name}</div>
                                                    <div className="text-sm text-muted-foreground">{business.businessId}</div>
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <div>{business.category}</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>{business.location}</TableCell>
                                        <TableCell>
                                            <div className="text-sm">
                                                {business.phone && <div>📞 {business.phone}</div>}
                                                {business.email && <div>✉️ {business.email}</div>}
                                            </div>
                                        </TableCell>
                                        <TableCell>{getStatusBadge(business)}</TableCell>
                                        <TableCell className="flex justify-end" onClick={(e) => e.stopPropagation()}>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                className="flex items-center gap-2"
                                                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                                                    e.stopPropagation();
                                                    setSelectedBusiness(business);
                                                    setIsDialogOpen(true);
                                                }}
                                            >
                                                <Eye className="w-4 h-4" />
                                                View Details
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                })
                            )}
                        </TableBody>
                    </Table>
                )}
            </CardContent>
        </Card>
        {pagination.total > 0 && !isLoading && (
            <div className="mt-4">
                <Pagination
                    totalItems={pagination.total}
                    itemsPerPage={pagination.per_page}
                    currentPage={pagination.current_page}
                    onPageChange={handlePageChange}
                />
            </div>
        )}

        {/* Business Details Dialog */}
        <BusinessDetailsDialog
            business={selectedBusiness || null}
            isOpen={isDialogOpen}
            onClose={() => setIsDialogOpen(false)}
        />
    </>
};

export default Business;
