
import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle, 
  CardFooter 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerDescription } from '@/components/ui/drawer';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import { <PERSON>, AlertCircle, CreditCard, HandHeart, Users, <PERSON>hake, <PERSON>, ArrowRight, ThumbsUp } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Layout } from '@/components/Layout';

const formSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  phone: z.string().min(10, { message: "Please enter a valid phone number" }),
});

const FinancingWaitlist = () => {
  const [submitted, setSubmitted] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const isMobile = useIsMobile();
  const { toast } = useToast();
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      name: "",
      phone: "",
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    setSubmitted(true);
    setOpenDialog(true);
    toast({
      title: "Added to waitlist!",
      description: "We'll notify you as soon as financing options are available.",
    });
  };

  const totalSignups = 350;
  const spotsRemaining = 50;
  const progressPercentage = Math.round(((totalSignups - spotsRemaining) / totalSignups) * 100);

  return (
    <Layout>
      <div className="flex-1 max-w-7xl mx-auto w-full px-4 md:px-6 pt-4 md:pt-12 pb-16">
        <div className="grid gap-10 lg:grid-cols-2 mt-8 md:mt-16 items-center">
          <div className="space-y-8">
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200 px-3 py-1.5">
                <Clock className="h-3.5 w-3.5 mr-1.5" />
                Coming Soon
              </Badge>
              <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200 px-3 py-1.5">
                <AlertCircle className="h-3.5 w-3.5 mr-1.5" />
                Only {spotsRemaining} spots left
              </Badge>
            </div>
            
            <div className="w-full bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-gray-900 dark:text-white">Waitlist Status</h3>
                <Badge className="bg-emerald-500 text-white">
                  <Users className="h-3.5 w-3.5 mr-1.5" />
                  {totalSignups} Members
                </Badge>
              </div>
              <Progress value={progressPercentage} className="h-3 w-full bg-gray-100 dark:bg-gray-700" />
              <div className="flex justify-between items-center mt-2 text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">{spotsRemaining} spots remaining</span>
                <span className="font-medium">{progressPercentage}% full</span>
              </div>
            </div>
            
            <div>
              <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
                Join our <span className="text-primary">exclusive</span> <span className="text-primary block">waitlist</span> for special financing rates
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-300 mt-6">
                We're partnering with leading financial institutions to bring you affordable payment options for your home improvement projects.
              </p>
            </div>
            
            <div className="space-y-6">
              <div className="flex items-start">
                <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-4 flex-shrink-0 shadow-sm">
                  <HandHeart className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">We're working hard for you</h3>
                  <p className="text-gray-600 dark:text-gray-300">Our team is finalizing partnerships that will get you the best rates possible for your home services.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-4 flex-shrink-0 shadow-sm">
                  <Handshake className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Exclusive early access</h3>
                  <p className="text-gray-600 dark:text-gray-300">Waitlist members will get early access to financing options before they're available to the general public.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-4 flex-shrink-0 shadow-sm">
                  <ThumbsUp className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Better rates than anywhere else</h3>
                  <p className="text-gray-600 dark:text-gray-300">We've negotiated special financing terms exclusively for our community members.</p>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <Card className="shadow-lg border-t-4 border-t-primary animate-fade-in mx-auto max-w-md">
              <CardHeader className="pb-2">
                <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-2 mx-auto">
                  <CreditCard className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-center text-2xl">Reserve Your Spot</CardTitle>
                <CardDescription className="text-center">
                  Be among the first to access our flexible financing options when they launch.
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Full Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter your name" className="h-12" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Address</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" className="h-12" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <Input placeholder="(*************" className="h-12" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button 
                      type="submit" 
                      className="w-full mt-4 bg-primary hover:bg-primary/90 h-12 text-base group" 
                      size="lg"
                    >
                      Join the Waitlist
                      <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                    </Button>
                  </form>
                </Form>
                
                <div className="text-sm text-gray-600 dark:text-gray-300 mt-6 space-y-2">
                  <div className="flex items-start">
                    <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                    <span>No obligation to purchase</span>
                  </div>
                  <div className="flex items-start">
                    <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                    <span>No credit check at this stage</span>
                  </div>
                  <div className="flex items-start">
                    <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                    <span>Priority access when we launch</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="block pt-0 pb-6 px-6">
                <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg text-sm">
                  <div className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                    <p className="text-gray-700 dark:text-gray-300">
                      <span className="font-medium">Limited availability:</span> Due to high demand, we're limiting initial access to financing options.
                    </p>
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
      
      {isMobile ? (
        <Drawer open={openDialog} onOpenChange={setOpenDialog}>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>You're on the waitlist!</DrawerTitle>
              <DrawerDescription>
                Thanks for joining. We'll notify you as soon as financing options are available.
              </DrawerDescription>
            </DrawerHeader>
            <div className="p-6 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-green-500" />
              </div>
              <p className="mb-4 text-gray-600 dark:text-gray-300">
                Your spot is secured! We've received your information and will be in touch soon with exclusive financing offers.
              </p>
              <p className="mb-6 font-medium text-gray-800 dark:text-white">
                A confirmation email will be sent to your registered email address.
              </p>
              <Button asChild variant="secondary" className="w-full" scrollToTop={true} onClick={() => setOpenDialog(false)}>
                <Link to="/">Return to Home</Link>
              </Button>
            </div>
          </DrawerContent>
        </Drawer>
      ) : (
        <Dialog open={openDialog} onOpenChange={setOpenDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>You're on the waitlist!</DialogTitle>
              <DialogDescription>
                Thanks for joining. We'll notify you as soon as financing options are available.
              </DialogDescription>
            </DialogHeader>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-green-500" />
              </div>
              <p className="mb-4 text-gray-600 dark:text-gray-300">
                Your spot is secured! We've received your information and will be in touch soon with exclusive financing offers.
              </p>
              <p className="mb-6 font-medium text-gray-800 dark:text-white">
                A confirmation email will be sent to your registered email address.
              </p>
              <Button asChild variant="secondary" scrollToTop={true} onClick={() => setOpenDialog(false)}>
                <Link to="/">Return to Home</Link>
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </Layout>
  );
};

export default FinancingWaitlist;
