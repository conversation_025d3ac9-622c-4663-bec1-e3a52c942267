import React, {useEffect, useState} from 'react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Clock, Shield, Check, Heart, Star, ArrowRight, ChevronRight, Wrench, Droplets, Settings, Pipette, BookCheck, Waves, Home, Award, X, ShieldCheck, Handshake, Users, DollarSign } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { ServicePageTemplate } from '@/components/ServicePageTemplate';
import { ServiceNeeds } from '@/components/ServiceNeeds';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { SEO } from '@/components/SEO';
import {fetchPosts} from "@/services/wordpressApi.ts";
import {SERVICE_CATEGORY} from "@/types/enum.ts";

const plumbingServices = [{
  icon: <Pipette className="h-8 w-8 text-primary" />,
  label: "Leak Repair"
}, {
  icon: <Droplets className="h-8 w-8 text-primary" />,
  label: "Drain Cleaning"
}, {
  icon: <Settings className="h-8 w-8 text-primary" />,
  label: "Pipe Repair"
}, {
  icon: <Wrench className="h-8 w-8 text-primary" />,
  label: "Fixture Installation"
}, {
  icon: <Home className="h-8 w-8 text-primary" />,
  label: "Water Heater Service"
}, {
  icon: <Waves className="h-8 w-8 text-primary" />,
  label: "Bathroom Plumbing"
}, {
  icon: <Settings className="h-8 w-8 text-primary" />,
  label: "Sewer Line Service"
}, {
  icon: <Wrench className="h-8 w-8 text-primary" />,
  label: "Gas Line Work"
}, {
  icon: <Droplets className="h-8 w-8 text-primary" />,
  label: "Sump Pump Service"
}, {
  icon: <BookCheck className="h-8 w-8 text-primary" />,
  label: "Commercial Services"
}];
const plumbingEstimates = [{
  tier: "Basic",
  price: "$89-149",
  description: "Simple plumbing repairs",
  features: ["Licensed plumber", "30-day warranty", "Common repairs", "Basic diagnostic"]
}, {
  tier: "Standard",
  price: "$179-299",
  description: "Complex repairs & installations",
  features: ["Licensed & insured pros", "90-day warranty", "Full diagnostic", "Parts included", "Emergency service"],
  recommended: true
}, {
  tier: "Premium",
  price: "$299-599+",
  description: "Major plumbing work",
  features: ["Master plumbers", "1-year warranty", "Priority service", "Complete inspection", "Extended coverage", "24/7 support"]
}];

const benefits = [{
  title: "Licensed & Insured Pros",
  description: "All our plumbers are fully licensed, insured, and undergo thorough background checks for your peace of mind.",
  icon: <ShieldCheck className="h-6 w-6 text-primary" />
}, {
  title: "24/7 Emergency Service",
  description: "Plumbing emergencies don't wait for business hours. Our team is available round the clock for urgent situations.",
  icon: <Clock className="h-6 w-6 text-primary" />
}, {
  title: "Upfront, Transparent Pricing",
  description: "Get clear pricing quotes before any work begins—no surprise fees or hidden charges on your final bill.",
  icon: <DollarSign className="h-6 w-6 text-primary" />
}, {
  title: "100% Satisfaction Guaranteed",
  description: "We stand behind our work with a complete satisfaction guarantee on all plumbing services and repairs.",
  icon: <Handshake className="h-6 w-6 text-primary" />
}, {
  title: "Expert Service Technicians",
  description: "Our skilled plumbers bring years of experience and ongoing training to solve any plumbing issue.",
  icon: <Users className="h-6 w-6 text-primary" />
}, {
  title: "Quality Parts & Materials",
  description: "We use only high-quality parts and materials that meet or exceed industry standards for lasting repairs.",
  icon: <Award className="h-6 w-6 text-primary" />
}];
const faqs = [{
  question: "How quickly can a plumber arrive for an emergency?",
  answer: "For emergency plumbing issues, we typically have plumbers available to arrive within 1-2 hours. Our 24/7 emergency service ensures you can get help any time, day or night."
}, {
  question: "Do you provide free estimates?",
  answer: "Yes, we provide free estimates for most plumbing services. Our plumbers will assess the situation and provide a clear, written estimate before beginning any work."
}, {
  question: "Are your plumbers licensed and insured?",
  answer: "Yes, all our plumbers are fully licensed, bonded, and insured. We maintain strict standards for our professionals and regularly verify their credentials."
}, {
  question: "What types of payment do you accept?",
  answer: "We accept all major credit cards, checks, and cash. For larger projects, we also offer financing options with approved credit."
}, {
  question: "Do you offer warranties on your services?",
  answer: "Yes, we stand behind our work with warranties ranging from 30 days to 1 year depending on the service provided. Our standard repairs come with a 90-day warranty on both parts and labor."
}, {
  question: "How can I prevent plumbing emergencies?",
  answer: "Regular maintenance is key to preventing emergencies. We recommend annual plumbing inspections, avoiding chemical drain cleaners, being careful what you flush, and knowing where your main water shut-off valve is located."
}];
const PlumbingService: React.FC = () => {
  const isMobile = useIsMobile();
  const [plumbingBlogPosts, setPlumbingBlogPosts] = useState<BlogPost[]>([])
    useEffect(() => {
        const loadInitialData = async () => {
            try {
                const { posts, totalPages: pages } = await fetchPosts(1, 3, SERVICE_CATEGORY.PLUMBING);
                setPlumbingBlogPosts(posts)
            } catch (err) {
                console.log(err);
            }
        };
        loadInitialData();
    }, []);

  const plumbingServiceImages = ['https://3bdab9c8-7438-4e75-917e-1e4d13365554.lovableproject.com/lovable-uploads/9d95927d-fbdc-46fc-94b3-f09c2c276cc6.png',
  // Dishwasher installation
  'https://3bdab9c8-7438-4e75-917e-1e4d13365554.lovableproject.com/lovable-uploads/68bcef68-14c9-4aea-9c33-e4b2defd5ff4.png',
  // Water filtration system
  'https://3bdab9c8-7438-4e75-917e-1e4d13365554.lovableproject.com/lovable-uploads/39de719a-f24d-4af1-ad5e-6fb799a1c378.png' // Water heater repair
  ];
  const fallbackImage = "https://images.unsplash.com/photo-1575517111839-3a3843ee7c5b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80";
  return <>
      <SEO title="Hire Trusted Plumbers Near You – Compare Bids & Book Same-Day Service" description="Need plumbing help fast? Get quotes from licensed local plumbers near you. Compare bids, schedule service, and enjoy transparent pricing—only on JobON." localBusinessSchema={true} serviceType="Plumbing" serviceSlug="plumbing" canonicalUrl="/services/plumbing" />

      <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-background pt-20 pb-8 md:pt-24 md:pb-12">
        <div className="container mx-auto px-4 lg:px-8">
          {isMobile ? <div className="w-full">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
                <div className="relative h-52 overflow-hidden">
                  <img src={plumbingServiceImages[0]} alt="Professional plumbing service" className="w-full h-full object-cover object-center" style={{objectPosition: "center 30%"}} />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent flex items-end">
                    <div className="p-4 text-white w-full">
                      <h1 className="text-2xl font-bold mb-1 text-left">
                        Professional Plumbing
                      </h1>
                      <h2 className="text-lg font-medium text-blue-300 mb-1 text-left">
                        <span className="block">Expert Service</span>
                        <span className="block">Reliable Solutions</span>
                      </h2>
                      <div className="flex items-center space-x-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((_, i) => <svg key={i} className="w-3 h-3 text-amber-400 fill-amber-400" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>)}
                        </div>
                        <span className="text-xs font-medium text-white">4.9/5 · 1,642 reviews</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4">
                  <p className="text-sm text-gray-800 dark:text-gray-300 mb-3 font-medium">
                    Professional plumbing services for homes, businesses, and emergency repairs
                  </p>

                  <div className="flex space-x-2 mb-3">
                    <Link to="/create-job?category=plumbing" className="flex-1">
                      <Button variant="default" size="default" className="w-full rounded-md font-semibold text-sm py-2">
                        Post a Job
                      </Button>
                    </Link>
                    <Link to="/professionals/plumbing" className="flex-1">
                      <Button variant="outline" size="default" className="w-full rounded-md font-semibold text-sm py-2 text-gray-800 dark:text-white border-gray-400">
                        Browse Plumbers
                      </Button>
                    </Link>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Shield className="h-5 w-5 text-green-500 mr-1" />
                      <div className="text-left">
                        <span className="text-xs text-black dark:text-white font-medium">
                          Licensed & Insured
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-5 w-5 text-blue-500 mr-1" />
                      <div className="text-left">
                        <span className="text-xs text-black dark:text-white font-medium">
                          Available 24/7
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Award className="h-5 w-5 text-purple-500 mr-1" />
                      <div className="text-left">
                        <span className="text-xs text-black dark:text-white font-medium">
                          100% Guaranteed
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div> : <div className="flex flex-col lg:flex-row gap-12 items-center">
              <div className="w-full lg:w-1/2 space-y-8">
                <div className="space-y-4">
                  <span className="inline-block bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-hover px-4 py-1.5 rounded-full font-medium text-sm">Professional Plumbing Services</span>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark-heading leading-tight">
                    Expert Plumbing Services,
                    <br className="hidden md:inline" />
                    <span className="text-primary mt-2 block dark:text-primary-hover dark:dark-mode-text-shadow">
                      For Every Need.
                    </span>
                  </h1>
                  <div className="text-xl font-semibold text-gray-700 dark-subheading">
                    <div className="flex flex-col space-y-3 text-left">
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Residential & Commercial Services</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Licensed & Insured Plumbers</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">24/7 Emergency Response</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Upfront, Transparent Pricing</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Link to="/create-job?category=plumbing" className="w-full sm:w-auto">
                    <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Post a Project
                      <ArrowRight size={20} className="ml-2" />
                    </Button>
                  </Link>
                  <Link to="/professionals/plumbing" className="w-full sm:w-auto">
                    <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Browse Plumbers
                    </Button>
                  </Link>
                </div>

                <div className="pt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Instant Quotes</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Verified Plumbers</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>100% Satisfaction</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Expert Technicians</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Same-Day Service</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Bonded & Insured</span>
                  </div>
                </div>
              </div>

              <div className="w-full lg:w-1/2 relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/5 rounded-xl filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src={plumbingServiceImages[0]} alt="Plumber installing dishwasher" className="w-full h-full object-cover" />
                    </div>
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src={plumbingServiceImages[1]} alt="Plumber installing water filtration system" className="w-full h-full object-cover" />
                    </div>
                    <div className="col-span-2 aspect-[16/9] rounded-lg overflow-hidden">
                      <img src={plumbingServiceImages[2]} alt="Plumber fixing water heater" className="w-full h-full object-cover" />
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <div className="flex justify-center mb-2">
                      <div className="flex text-amber-400">
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">4.9/5 (1,642 reviews)</span>
                    </div>
                    <h3 className="font-bold text-xl mb-1 dark:text-white">Find Top Local Plumbers</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Enter your ZIP code to get matched with professional plumbers in your area</p>

                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                      <div className="flex-1 flex items-center bg-gray-50 dark:bg-gray-700 p-4">
                        <input type="text" placeholder="Enter your ZIP code" className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-300 text-lg" />
                      </div>
                      <Button type="submit" className="px-8 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all">
                        <span>Search</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>}
        </div>
      </section>

      <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Common Professional Plumbing Services</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
              Select from our most requested plumbing services
            </p>
          </div>

          <ServiceNeeds serviceId="plumbing" needs={plumbingServices} estimates={plumbingEstimates} />
        </div>
      </section>

      <section className="py-8 md:py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Recently Completed Plumbing Projects</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Browse through recently completed plumbing jobs by our network of professionals
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src={plumbingServiceImages[2]} alt="Water heater installation" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 days ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Water Heater Installation</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Complete replacement of 50-gallon water heater with energy efficient model, including new pipes and fittings.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Francisco, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$1,250</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src={plumbingServiceImages[0]} alt="Appliance plumbing services" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">1 week ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Dishwasher Installation</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Complete installation of new dishwasher including connection to water supply and drain lines.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Oakland, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$250</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src={plumbingServiceImages[1]} alt="Water filtration system" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 weeks ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Water Filter Installation</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Installation of whole-home water filtration system with professional setup and testing.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Jose, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$875</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Latest Plumbing Tips & Guides</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Expert advice and helpful resources for maintaining your plumbing systems
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {
              plumbingBlogPosts.length &&
              plumbingBlogPosts.map((post) => (
                  <BlogCard key={post.id} post={post} />
              ))
            }
          </div>

          <div className="text-center mt-8 md:mt-12">
            <Link to="/blog?category=plumbing">
              <Button variant="default" size={isMobile ? "default" : "lg"} className="font-medium">
                View All Plumbing Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <ServicePageTemplate serviceId="plumbing" title="Professional Plumbing Services" subtitle="Get reliable plumbing solutions from licensed professionals for your home or business" description="From leak repairs to fixture installations and drain cleaning, our experienced plumbing professionals deliver quality workmanship backed by satisfaction guarantees." heroImage="https://images.unsplash.com/photo-1606800052052-a08af7148866?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80" benefits={benefits} faqs={faqs} estimates={plumbingEstimates} commonNeeds={[]} // Emptied the commonNeeds since we're now displaying it separately
    hideEstimator={false} hideHero={true} professionalTitle="Plumbers" seoTitle="Hire Trusted Plumbers Near You – Compare Bids & Book Same-Day Service" serviceImages={plumbingServiceImages} // Pass the updated images to ServicePageTemplate
    customCta={<div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/create-job?category=plumbing" className="w-full sm:w-auto">
              <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Post a Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/professionals/plumbing" className="w-full sm:w-auto">
              <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Browse Plumbers
              </Button>
            </Link>
          </div>} />
    </>;
};
export default PlumbingService;
