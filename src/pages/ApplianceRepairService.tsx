
import React, { useEffect, useState } from 'react';
import { ServicePageTemplate } from '../components/ServicePageTemplate';
import { Link, useLocation } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { ServiceNeeds } from '@/components/ServiceNeeds';
import { ArrowRight } from 'lucide-react';
import { SEO } from '@/components/SEO';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { fetchPosts } from "@/services/wordpressApi.ts";
import { useIsMobile } from '@/hooks/use-mobile';

// Import components
import HeroSection from '@/features/appliance-repair/components/HeroSection';
import RecentProjects from '@/features/appliance-repair/components/RecentProjects';

// Import data
import benefits from '@/features/appliance-repair/data/benefits';
import faqs from '@/features/appliance-repair/data/faqs';
import applianceRepairEstimates from '@/features/appliance-repair/data/applianceRepairEstimates';
import commonApplianceRepairNeeds from '@/features/appliance-repair/data/commonApplianceRepairNeeds';
import {SERVICE_CATEGORY} from "@/types/enum.ts";

const ApplianceRepairService: React.FC = () => {
  const isMobile = useIsMobile();
  const location = useLocation();
  const [applianceBlogPosts, setApplianceBlogPosts] = useState<BlogPost[]>([]);

  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const { posts, totalPages: pages } = await fetchPosts(1, 3, SERVICE_CATEGORY.APPLIANCE_REPAIR);
        setApplianceBlogPosts(posts);
      } catch (err) {
        console.log(err);
      }
    };
    loadInitialData();
  }, []);

  return (
    <>
      <SEO 
        title="Appliance Repair Pros Near You – Compare Fast, Trusted Bids"
        description="Fix your appliances fast with help from certified local technicians. Compare quotes, get transparent pricing, and enjoy a 90-day warranty—only on JobON."
        localBusinessSchema={true}
        serviceType="Appliance Repair"
        serviceSlug="appliance-repair"
        canonicalUrl = '/services/appliance-repair'
      />

      <HeroSection />

      <section className="py-6 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-1.5 md:mb-3 text-black dark:text-white">Common Professional Appliance Repair Needs</h2>
            <p className="text-base md:text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Select from our most requested appliance repair services below
            </p>
          </div>

          <ServiceNeeds serviceId="appliance-repair" needs={commonApplianceRepairNeeds} estimates={applianceRepairEstimates} />
        </div>
      </section>

      <RecentProjects />

      <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Latest Appliance Repair Tips & Guides</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Expert advice and helpful resources for maintaining your home appliances
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {
              applianceBlogPosts.length > 0 &&
              applianceBlogPosts.slice(0, 3).map((post) => (
                <BlogCard key={post.id} post={post} />
              ))
            }
          </div>

          <div className="text-center mt-8 md:mt-12">
            <Link to="/blog?category=appliance-repair">
              <Button
                variant="default"
                size={isMobile ? "default" : "lg"}
                className="font-medium"
              >
                View All Appliance Repair Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <ServicePageTemplate
        serviceId="appliance-repair"
        title="Professional Appliance Repair Services"
        subtitle="Expert repair for all your household and commercial appliances"
        description="From refrigerators and washers to ovens and dishwashers, our skilled technicians provide fast, reliable repairs for all major appliance brands."
        heroImage="https://images.unsplash.com/photo-1581092160607-ee22621dd758?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80"
        benefits={benefits}
        faqs={faqs}
        estimates={applianceRepairEstimates}
        commonNeeds={[]}
        hideEstimator={false}
        hideHero={true}
        professionalTitle="Appliance Pros"
        seoTitle="Appliance Repair Pros Near You | Fast, Trusted Bids"
        customCta={
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/create-job" state={{ from: location.pathname }} className="w-full sm:w-auto">
              <Button 
                variant="default" 
                size="lg" 
                className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all"
              >
                Post a Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/professionals/appliance-repair" className="w-full sm:w-auto">
              <Button 
                variant="secondary" 
                size="lg" 
                className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all"
              >
                Browse Technicians
              </Button>
            </Link>
          </div>
        }
      />
    </>
  );
};

export default ApplianceRepairService;
