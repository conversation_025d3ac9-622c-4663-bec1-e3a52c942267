import React, { useState, useEffect, useRef, ChangeEvent } from 'react';
import useAuthHeader from 'react-auth-kit/hooks/useAuthHeader';
import {
  Circle,
  Camera,
  Search,
  PlusCircle,
  Heart,
  User,
  Home,
  X,
  ChevronLeft,
  ArrowRight,
  Upload,
  MapPin,
  Clock,
  Mail,
  Phone,
  Wrench,
  Zap,
  Trash2,
  Bug,
  Leaf,
  Hammer,
  Snowflake,
  HomeIcon,
  Sun,
  Droplet,
  ArrowDownWideNarrow,
  ShowerHead,
  Flame,
  FoldHorizontal,
  Waves,
  Filter,
  Repeat,
  Lightbulb,
  Plug,
  Fan,
  Power,
  Battery,
  Router,
  BatteryCharging,
  Shield,
  Sparkles,
  RotateCw,
  Boxes,
  SquareDashedBottom,
  Wind,
  WashingMachine,
  FolderArchive,
  SprayCan,
  Shirt,
  Antenna,
  BugOff,
  Egg,
  Worm,
  Rat,
  Banana,
  Fish,
  Bird,
  Microscope,
  Scissors,
  Trees,
  Sprout,
  Shovel,
  Wand2,
  GalleryVerticalEnd,
  Drill,
  SlidersHorizontal,
  DoorOpen,
  PaintBucket,
  Frame,
  Fence,
  Grid3X3,
  Cloud,
  Ruler,
  Refrigerator,
  Container,
  Utensils,
  Timer,
  ArrowDownUp,
  HardDrive,
  Tv,
  ArrowUpCircle,
  CheckCircle2,
  Building,
  Briefcase,
  Calendar,
  Loader2
} from 'lucide-react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { InputWithIcon } from '@/components/ui/input-with-icon';
import { Label } from '@/components/ui/label';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format, isValid } from 'date-fns';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { ImageUploader } from '@/components/ImageUploader';
import {useLocation, useNavigate} from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { timeOptions } from "@/data/createJob/dateTime";
import { frequencyOptions } from "@/data/createJob/frequencyOptions";
import { Progress } from "@/components/ui/progress";
import { getPreviousPage, setPreviousPage } from '@/utils/navigationUtils';
import { useSwipeGesture } from '@/hooks/use-swipe-gesture';
import { MobileCreateJobHeader } from '@/components/mobile/MobileCreateJobHeader';
import { MobileUserInfoForm } from '@/components/mobile/MobileUserInfoForm';
import { motion } from 'framer-motion';
import {SEO} from "@/components/SEO.tsx";
import { MobilePhotoUploadStep } from '@/components/mobile/MobilePhotoUploadStep';
import {property} from "@/data/createJob/property.ts";
import { apiService } from '@/services/api';
import { useAuth } from '@/features/auth/hooks/useAuth';

interface AssetUploadResponse {
  success: boolean;
  message?: string;
  data: Array<{
    uuid: string;
    [key: string]: any;
  }>;
}

interface JobBookingResponse {
  success: boolean;
  message?: string;
  data: {
    id: string;
    [key: string]: any;
  };
}

const STEPS = {
  JOB_TYPE: -1,
  PROPERTY_TYPE: 0,
  SERVICE_CATEGORY: 1,
  SERVICE_TASKS: 2,
  PHOTOS: 3,
  DATE_TIME: 4,
  FREQUENCY: 5,
  BUDGET_LOCATION: 6,
  REVIEW: 7
};

const serviceCategories = [{
  id: 'plumbing',
  name: 'Plumbing',
  icon: Wrench
}, {
  id: 'electrical',
  name: 'Electrical',
  icon: Zap
}, {
  id: 'cleaning',
  name: 'Cleaning',
  icon: Trash2
}, {
  id: 'landscaping',
  name: 'Landscaping',
  icon: Leaf
},
  // {
  //   id: 'pest-control',
  //   name: 'Pest Control',
  //   icon: Bug
  // },{
  //   id: 'handyman',
  //   name: 'Handyman',
  //   icon: Hammer
  // }, {
  //   id: 'appliance-repair',
  //   name: 'Appliance',
  //   icon: Wrench
  // }, {
  //   id: 'hvac',
  //   name: 'HVAC',
  //   icon: Snowflake
  // }, {
  //   id: 'roofing',
  //   name: 'Roofing',
  //   icon: HomeIcon
  // }, {
  //   id: 'solar',
  //   name: 'Solar',
  //   icon: Sun
  // }
];

const serviceTaskOptions = {
  'plumbing': [{
    name: 'Leaky Faucet',
    icon: Droplet
  }, {
    name: 'Clogged Drain',
    icon: ArrowDownWideNarrow
  }, {
    name: 'Toilet',
    icon: ShowerHead
  }, {
    name: 'Water Heater',
    icon: Flame
  }, {
    name: 'Pipe',
    icon: FoldHorizontal
  }, {
    name: 'Fixture Installation',
    icon: Wrench
  }, {
    name: 'Sewer Line',
    icon: Waves
  }, {
    name: 'Garbage Disposal',
    icon: Trash2
  }, {
    name: 'Water Pressure',
    icon: Filter
  }, {
    name: 'New Installation',
    icon: Repeat
  }, {
    name: 'Other',
    icon: Wrench
  }],
  'electrical': [{
    name: 'Light Fixture',
    icon: Lightbulb
  }, {
    name: 'Outlet',
    icon: Plug
  }, {
    name: 'Ceiling Fan',
    icon: Fan
  }, {
    name: 'Panel Upgrade',
    icon: Power
  }, {
    name: 'Wiring',
    icon: Zap
  }, {
    name: 'Generator',
    icon: Battery
  }, {
    name: 'Smart Home',
    icon: Router
  }, {
    name: 'EV Charger',
    icon: BatteryCharging
  }, {
    name: 'Inspection',
    icon: Wrench
  }, {
    name: 'Surge Protection',
    icon: Shield
  }, {
    name: 'Other',
    icon: Zap
  }],
  'cleaning': [{
    name: 'Deep Clean',
    icon: Sparkles
  }, {
    name: 'Regular Maintenance',
    icon: RotateCw
  }, {
    name: 'Move-in/Move-out',
    icon: Boxes
  }, {
    name: 'Carpet',
    icon: SquareDashedBottom
  }, {
    name: 'Windows',
    icon: Wind
  }, {
    name: 'Appliances',
    icon: WashingMachine
  }, {
    name: 'Post-Construction',
    icon: Hammer
  }, {
    name: 'Organization',
    icon: FolderArchive
  }, {
    name: 'Disinfection',
    icon: SprayCan
  }, {
    name: 'Laundry',
    icon: Shirt
  }, {
    name: 'Other',
    icon: Sparkles
  }],
  'pest-control': [{
    name: 'Ants',
    icon: Antenna
  }, {
    name: 'Roaches',
    icon: BugOff
  }, {
    name: 'Bed Bugs',
    icon: Egg
  }, {
    name: 'Termites',
    icon: Worm
  }, {
    name: 'Mosquitoes',
    icon: Bug
  }, {
    name: 'Rodents',
    icon: Rat
  }, {
    name: 'Fleas & Ticks',
    icon: Banana
  }, {
    name: 'Wasps & Bees',
    icon: Fish
  }, {
    name: 'Wildlife Removal',
    icon: Bird
  }, {
    name: 'Pest Inspection',
    icon: Microscope
  }, {
    name: 'Other',
    icon: Bug
  }],
  'landscaping': [{
    name: 'Lawn Mowing',
    icon: Scissors
  }, {
    name: 'Tree Trimming',
    icon: Trees
  }, {
    name: 'Garden Planting',
    icon: Sprout
  }, {
    name: 'Irrigation',
    icon: Droplet
  }, {
    name: 'Mulching',
    icon: Shovel
  }, {
    name: 'Hedge Trimming',
    icon: Scissors
  }, {
    name: 'Leaf Removal',
    icon: Leaf
  }, {
    name: 'Weed Control',
    icon: Sprout
  }, {
    name: 'Landscape Design',
    icon: Wand2
  }, {
    name: 'Hardscaping',
    icon: Shovel
  }, {
    name: 'Other',
    icon: Leaf
  }],
  'handyman': [{
    name: 'Furniture Assembly',
    icon: GalleryVerticalEnd
  }, {
    name: 'Mounting',
    icon: Drill
  }, {
    name: 'Drywall',
    icon: SlidersHorizontal
  }, {
    name: 'Door',
    icon: DoorOpen
  }, {
    name: 'Painting',
    icon: PaintBucket
  }, {
    name: 'Deck',
    icon: Frame
  }, {
    name: 'Fence',
    icon: Fence
  }, {
    name: 'Tile Work',
    icon: Grid3X3
  }, {
    name: 'Weather Stripping',
    icon: Cloud
  }, {
    name: 'Gutter Cleaning',
    icon: Ruler
  }, {
    name: 'Other',
    icon: Hammer
  }],
  'appliance-repair': [{
    name: 'Refrigerator',
    icon: Refrigerator
  }, {
    name: 'Washer/Dryer',
    icon: WashingMachine
  }, {
    name: 'Dishwasher',
    icon: Container
  }, {
    name: 'Oven/Stove',
    icon: Utensils
  }, {
    name: 'Microwave',
    icon: Timer
  }, {
    name: 'Garbage Disposal',
    icon: ArrowDownUp
  }, {
    name: 'Air Conditioner',
    icon: Snowflake
  }, {
    name: 'Water Heater',
    icon: Flame
  }, {
    name: 'Freezer',
    icon: HardDrive
  }, {
    name: 'Small Appliances',
    icon: Tv
  }, {
    name: 'Other',
    icon: Wrench
  }],
  'hvac': [{
    name: 'AC',
    icon: Snowflake
  }, {
    name: 'Heating',
    icon: Flame
  }, {
    name: 'Installation',
    icon: Drill
  }, {
    name: 'Maintenance',
    icon: RotateCw
  }, {
    name: 'Duct Cleaning',
    icon: Wind
  }, {
    name: 'Thermostat',
    icon: SlidersHorizontal
  }, {
    name: 'Air Quality',
    icon: Wind
  }, {
    name: 'Filter Replacement',
    icon: Filter
  }, {
    name: 'Refrigerant Check',
    icon: Droplet
  }, {
    name: 'Energy Efficiency',
    icon: Zap
  }, {
    name: 'Other',
    icon: Snowflake
  }],
  'roofing': [{
    name: 'Roof Inspection',
    icon: HomeIcon
  }, {
    name: 'Roof Repair',
    icon: Hammer
  }, {
    name: 'Roof Replacement',
    icon: Repeat
  }, {
    name: 'Gutter Installation',
    icon: Waves
  }, {
    name: 'Gutter Cleaning',
    icon: Sparkles
  }, {
    name: 'Leak Repair',
    icon: Droplet
  }, {
    name: 'Shingle Repair',
    icon: Grid3X3
  }, {
    name: 'Flashing Repair',
    icon: Frame
  }, {
    name: 'Skylight Installation',
    icon: Sun
  }, {
    name: 'Storm Damage',
    icon: Cloud
  }, {
    name: 'Other',
    icon: HomeIcon
  }],
  'solar': [{
    name: 'Solar Panel Installation',
    icon: Sun
  }, {
    name: 'Solar Consultation',
    icon: Lightbulb
  }, {
    name: 'Battery Storage',
    icon: BatteryCharging
  }, {
    name: 'System Repair',
    icon: Wrench
  }, {
    name: 'System Upgrade',
    icon: ArrowUpCircle
  }, {
    name: 'Panel Cleaning',
    icon: Sparkles
  }, {
    name: 'Energy Audit',
    icon: Zap
  }, {
    name: 'Roof Assessment',
    icon: HomeIcon
  }, {
    name: 'Electrical Work',
    icon: Power
  }, {
    name: 'Inverter Service',
    icon: RotateCw
  }, {
    name: 'Other',
    icon: Sun
  }]
};

interface FormErrors {
  fullName?: string;
  address?: string;
  email?: string;
  phone?: string;
}

interface addressDetailType {
  address: string;
}

const sampleAddresses = ["123 Main St, Anytown, CA 12345", "456 Oak Ave, Somecity, CA 67890", "789 Pine Rd, Othercity, CA 54321", "101 Maple Dr, Newtown, CA 98765", "202 Cedar Ln, Oldtown, CA 13579", "303 Elm Blvd, Centertown, CA 24680", "404 Birch Way, Edgetown, CA 35791", "505 Spruce Ct, Bordertown, CA 86420", "606 Ash St, Riverside, CA 97531", "707 Walnut Ave, Hillside, CA 75319", "808 Cherry Rd, Valley, CA 24681"];

const CreateJob: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    toast
  } = useToast();
  const isMobile = useIsMobile();
  const [step, setStep] = useState(STEPS.PROPERTY_TYPE);
  const [propertyType, setPropertyType] = useState<string>('');
  const [serviceCategory, setServiceCategory] = useState<string>('');
  const [serviceTasks, setServiceTasks] = useState<string[]>([]);
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [timeSlot, setTimeSlot] = useState<string>('');
  const [selectedFrequency, setSelectedFrequency] = useState<string>('one-time');
  const [budget, setBudget] = useState<string>('');
  const [photoURLs, setPhotoURLs] = useState<string[]>([]);
  const [assetUuids, setAssetUuids] = useState<string[]>([]);
  const [photoUploadError, setPhotoUploadError] = useState<string | null>(null);
  const [description, setDescription] = useState<string>('');
  const [addressDetail, setAddressDetail] = useState<addressDetailType>({
    address: ''
  });
  const [filteredAddresses, setFilteredAddresses] = useState<string[]>([]);
  const [showAddressSuggestions, setShowAddressSuggestions] = useState<boolean>(false);
  const [contactInfo, setContactInfo] = useState({
    fullName: '',
    email: '',
    phone: '',
    address: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const form = useForm();
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const videoRef = React.useRef<HTMLVideoElement>(null);
  const canvasRef = React.useRef<HTMLCanvasElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [showCamera, setShowCamera] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setPreviousPage();
  }, []);

  useSwipeGesture({
    onSwipeRight: () => {
      if (isMobile && step > STEPS.PROPERTY_TYPE) {
        handleBack();
      } else if (isMobile && step === STEPS.PROPERTY_TYPE) {
        if (location.state && location.state.from) {
          navigate(location.state.from);
        } else {
          const previousPage = getPreviousPage();
          navigate(previousPage);
        }
      }
    }
  });

  const getTotalSteps = () => Object.keys(STEPS).length - 1;
  const getCurrentStepNumber = () => step + 1;
  const progressPercentage = Math.round(getCurrentStepNumber() / getTotalSteps() * 100);

  const getStepTitle = () => {
    switch (step) {
      case STEPS.PROPERTY_TYPE:
        return "What type of property is this for?";
      case STEPS.SERVICE_CATEGORY:
        return "What service do you need?";
      case STEPS.SERVICE_TASKS:
        return "What specific tasks do you need help with?";
      case STEPS.PHOTOS:
        return "Upload photos or video (optional)";
      case STEPS.DATE_TIME:
        return "When do you need this service?";
      case STEPS.FREQUENCY:
        return "How often do you need this service?";
      case STEPS.BUDGET_LOCATION:
        return "Project details";
      case STEPS.REVIEW:
        return "Just a few more details";
      default:
        return "Create a service request";
    }
  };

  const handlePropertyTypeSelect = (type: string) => {
    setPropertyType(type);
    setStep(STEPS.SERVICE_CATEGORY);
  };

  const handleServiceCategorySelect = (category: string) => {
    setServiceCategory(category);
    setStep(STEPS.SERVICE_TASKS);
  };

  const handleTaskSelect = (task: string) => {
    setServiceTasks(prevTasks => {
      // Check if the task is already selected
      if (prevTasks.includes(task)) {
        // If it is, remove it from the array
        return prevTasks.filter(t => t !== task);
      } else {
        // If it's not, add it to the array
        return [...prevTasks, task];
      }
    });
  };

  const handleDateSelect = (selectedDate: Date | undefined) => {
    setDate(selectedDate);
    if (isMobile && selectedDate) {
      const calendarPopoverElement = document.querySelector('[data-state="open"][data-radix-popper-content-wrapper]');
      if (calendarPopoverElement) {
        const closeButton = calendarPopoverElement.querySelector('button[aria-label="Close"]');
        if (closeButton instanceof HTMLElement) {
          closeButton.click();
        }
      }
    }
  };

  const uploadImages = async (files: File[]): Promise<string[]> => {
    try {
      setPhotoUploadError(null);

      if (files.length === 0) {
        return [];
      }

      const formData = new FormData();

      // Append each file to FormData with the key 'files[]'
      files.forEach(file => {
        formData.append('files[]', file);
      });

      const { data, error, isSuccess } = await apiService<AssetUploadResponse>('/api/assets/upload', {
        method: 'POST',
        headers: {
          // Don't set Content-Type header for FormData
          'Accept': 'application/json',
          'Content-Type': undefined, // Override default Content-Type for FormData
        },
        body: formData,
      });

      if (!isSuccess) {
        throw new Error(error || 'Upload failed');
      }

      const result = data;

      if (!result || !result.success) {
        throw new Error(result?.message || 'Upload failed');
      }

      // Extract UUIDs from the response
      const uploadedAssetUuids = result.data.map((asset) => asset.uuid);
      setAssetUuids(uploadedAssetUuids);

      return uploadedAssetUuids;
    } catch (error: unknown) {
      console.error('Error uploading images:', error);
      setPhotoUploadError('Failed to upload images. Please try again.');
      return [];
    }
  };

  const handlePhotoChange = async (images: string[], files?: File[]) => {
    setPhotoURLs(images);

    // If files are provided, upload them to the server
    if (files && files.length > 0) {
      await uploadImages(files);
    }
  };

  const handleNext = () => {
    if (step < STEPS.REVIEW) {
      setStep(step + 1);
      window.scrollTo(0, 0);
    }
  };

  const handleBack = () => {
    if (step > STEPS.PROPERTY_TYPE) {
      setStep(step - 1);
      window.scrollTo(0, 0);
    } else {
      // Navigate to previous page if we're at the first step
      // Check for location state first for the referring page
      if (location.state && location.state.from) {
        navigate(location.state.from);
      } else {
        // Fall back to cookie-based previous page tracking
        const previousPage = getPreviousPage();
        navigate(previousPage);
      }
    }
  };

  const handleAddressChange = (value: string) => {
    setAddressDetail({
      address: value
    });
    if (value.trim() !== '') {
      const filtered = sampleAddresses.filter(addr => addr.toLowerCase().includes(value.toLowerCase()));
      setFilteredAddresses(filtered);
      setShowAddressSuggestions(true);
    } else {
      setFilteredAddresses([]);
      setShowAddressSuggestions(false);
    }
  };

  const handleAddressSelect = (selectedAddress: string) => {
    setAddressDetail({
      address: selectedAddress
    });
    setShowAddressSuggestions(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('#address-container')) {
        setShowAddressSuggestions(false);
      }
    };
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleContactInfoChange = (field: string, value: string) => {
    setContactInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const cleanPhone = phone.replace(/\D/g, '');
    if (cleanPhone.length === 10) {
      return true;
    }
    if (cleanPhone.length === 11 && cleanPhone.charAt(0) === '1') {
      return true;
    }
    return cleanPhone.length >= 10 && cleanPhone.length <= 15;
  };

  const formatPhoneNumber = (value: string): string => {
    const cleanPhone = value.replace(/\D/g, '');
    if (cleanPhone.length >= 10) {
      const areaCode = cleanPhone.substring(0, 3);
      const middle = cleanPhone.substring(3, 6);
      const last = cleanPhone.substring(6, 10);
      return `(${areaCode}) ${middle}-${last}`;
    }
    return value;
  };

  const handlePhoneChange = (value: string) => {
    const formattedValue = formatPhoneNumber(value);
    handleContactInfoChange('phone', formattedValue);
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;
    if (!contactInfo.fullName.trim()) {
      newErrors.fullName = "Name is required";
      isValid = false;
    }
    if (!contactInfo.email.trim()) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!validateEmail(contactInfo.email)) {
      newErrors.email = "Please enter a valid email address";
      isValid = false;
    }
    if (!contactInfo.phone.trim()) {
      newErrors.phone = "Phone number is required";
      isValid = false;
    } else if (!validatePhone(contactInfo.phone)) {
      newErrors.phone = "Please enter a valid phone number (at least 10 digits)";
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: {
            ideal: 'environment'
          },
          width: {
            ideal: 1280
          },
          height: {
            ideal: 720
          }
        },
        audio: false
      });
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.onloadedmetadata = () => {
          videoRef.current?.play().catch(e => console.error('Error playing video:', e));
        };
      }
      setStream(mediaStream);
      setShowCamera(true);
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast({
        title: "Camera Error",
        description: "Unable to access camera. Please check your permissions.",
        variant: "destructive"
      });
    }
  };

  const { isAuthenticated } = useAuth();
  const authHeader = useAuthHeader()

  // Store the structured address data from the selected address
  const [structuredAddressData, setStructuredAddressData] = useState<{
    address: string;
    city: string;
    state: string;
    zipCode: string;
  }>({
    address: '',
    city: '',
    state: '',
    zipCode: ''
  });

  const handleSubmit = async () => {
    setSubmitAttempted(true);
    setIsLoading(true)
    if (validateForm()) {
      const formattedDate = date ? date.toISOString().split('T')[0] : null;

      // Use the structured address data if available, otherwise parse from the address string
      let locationData;
      if (structuredAddressData.address === addressDetail.address && structuredAddressData.city && structuredAddressData.state && structuredAddressData.zipCode) {
        locationData = structuredAddressData;
      } else {
        const addressParts = (addressDetail.address || "").split(',');
        let city = '', state = '', zipCode = '';
        if (addressParts.length > 1 && addressParts[1]) {
          city = addressParts[1].trim();
        }
        if (addressParts.length > 2 && addressParts[2]) {
          const stateZip = addressParts[2].trim().split(' ');
          state = stateZip[0] || '';
          zipCode = stateZip[1] || '';
        }
        locationData = {
          address: addressDetail.address || '',
          city: city,
          state: state,
          zipCode: zipCode
        };
      }
      // recurringFrequency is string | null
      const recurringFrequencyValue = selectedFrequency !== 'one-time' ? selectedFrequency : null;
      const jobData = {
        jobType: "send_bids",
        service: {
          category: serviceCategory || '',
          tasks: serviceTasks
        },
        property: {
          type: propertyType || ''
        },
        schedule: {
          date: (formattedDate ?? '') as string, 
          timePreference: (timeSlot || '') as string,
          frequency: (selectedFrequency || '') as string,
          recurringFrequency: (recurringFrequencyValue ?? '') as string
        },
        location: {
          address: locationData.address || '',
          city: locationData.city || '',
          state: locationData.state || '',
          zipCode: locationData.zipCode || ''
        },
        contact: {
          fullName: contactInfo.fullName || '',
          email: contactInfo.email || '',
          phone: contactInfo.phone || ''
        },
        description: description || '',
        assets: assetUuids // Assuming assets is string[] and handled if empty
      };

      try {
        // Send POST request to the API using apiService
        const { data, error, isSuccess } = await apiService<JobBookingResponse>('/api/job-bookings', {
          method: 'POST',
            headers: {
                'Authorization': authHeader,
            },
          body: {jobData : jobData},
        });

        if (!isSuccess) {
          throw new Error(error || 'HTTP error occurred');
        }

        // Handle success
        toast({
          title: "Success",
          description: "Your service request has been submitted successfully",
          variant: "default",
          className: "bg-green-400 text-white",
        });

        // Reset form data
        setPropertyType('');
        setServiceCategory('');
        setServiceTasks([]);
        setDate(undefined);
        setTimeSlot('');
        setSelectedFrequency('one-time');
        setBudget('');
        setPhotoURLs([]);
        setAssetUuids([]);
        setDescription('');
        setAddressDetail({ address: '' });
        setContactInfo({
          fullName: '',
          email: '',
          phone: '',
          address: ''
        });
        setAttachedFiles([]);
        setStructuredAddressData({
          address: '',
          city: '',
          state: '',
          zipCode: ''
        });

        // Navigate to login page with redirect URL to the created job
        setIsLoading(false);
        setStep(STEPS.PROPERTY_TYPE);
        if (!isAuthenticated){
          navigate(`/auth?mode=login`);
        }

      } catch (error: unknown) {
        // Handle error
        console.error("Error submitting job request:", error);
        setIsLoading(false)
        toast({
          title: "Error",
          description: "Your service request has been submitted unsuccessfully.",
          variant: "destructive",
        });
      }
    } else {
      setIsLoading(false)
    }
  };

  const handleFileAttachment = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setAttachedFiles(prev => [...prev, ...newFiles]);
    }
  };

  const removeAttachedFile = (index: number) => {
    setAttachedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const canContinueFromStep7 = () => {
    return description.trim().length > 0;
  };

  const renderStep = () => {
    const pageVariants = {
      initial: { opacity: 0, x: 10 },
      in: { opacity: 1, x: 0 },
      out: { opacity: 0, x: -10 }
    };

    switch (step) {
      case STEPS.PROPERTY_TYPE:
        return (
            <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={{ duration: 0.3 }}
                className="space-y-6"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {property.map(({ type, icon: Icon, label }) => (
                    <Card
                        key={type}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                            propertyType === type ? 'border-primary bg-primary/5 shadow-md' : ''
                        }`}
                        onClick={() => handlePropertyTypeSelect(type)}
                    >
                      <CardContent className="flex flex-col items-center justify-center p-6">
                        <Icon className="h-12 w-12 mb-4 text-primary" />
                        <h3 className="font-medium text-lg">{label}</h3>
                        {propertyType === type && <CheckCircle2 className="h-5 w-5 text-green-500 mt-2" />}
                      </CardContent>
                    </Card>
                ))}
              </div>
            </motion.div>
        );

      case STEPS.SERVICE_CATEGORY:
        return (
          <motion.div 
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="grid grid-cols-2 gap-3">
              {serviceCategories.map(category => (
                <Card 
                  key={category.id} 
                  className={`cursor-pointer transition-all ${serviceCategory === category.id ? 'border-primary bg-primary/5' : ''}`}
                  onClick={() => handleServiceCategorySelect(category.id)}
                >
                  <CardContent className="flex flex-col items-center justify-center p-4">
                    <category.icon className="h-10 w-10 mb-3 text-primary" />
                    <h3 className="font-medium text-center">{category.name}</h3>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        );

      case STEPS.SERVICE_TASKS:
        return serviceCategory ? (
          <motion.div 
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 ">
              {serviceTaskOptions[serviceCategory as keyof typeof serviceTaskOptions].map((task: { name: string; icon: React.ElementType }, index: number) => (
                <Card 
                  key={index} 
                  className={`cursor-pointer transition-all hover:border-primary ${
                    serviceTasks.includes(task.name) ? 'border-primary bg-primary/5' : ''
                  }`}
                  onClick={() => handleTaskSelect(task.name)}
                >
                  <CardContent className="flex flex-col items-center justify-center p-4">
                    <task.icon className="h-8 w-8 mb-2 text-primary" />
                    <h3 className="font-medium text-sm text-center">{task.name}</h3>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="flex justify-between">
              <Button variant="outline" onClick={handleBack} className="flex items-center">
                <ChevronLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <Button
                  onClick={handleNext}
                  className="flex items-center"
                  variant="default"
                  disabled={serviceTasks.length === 0}
              >
                {photoURLs.length > 0 ? 'Continue' : 'Skip This Step'}
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </motion.div>
        ) : null;

      case STEPS.PHOTOS:
        if (isMobile) {
          return (
            <MobilePhotoUploadStep
              photoURLs={photoURLs}
              onPhotoChange={handlePhotoChange}
              onNext={handleNext}
              onBack={handleBack}
            />
          );
        }

        return (
          <motion.div 
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="text-center mb-4">
              <h2 className="text-xl font-semibold">Upload Photos or Video (optional)</h2>
              <p className="text-gray-500 mt-2">
                Photos help service providers understand your needs better
              </p>
            </div>

            <ImageUploader 
              onImagesSelected={handlePhotoChange} 
              currentImages={photoURLs} 
              maxImages={10} 
              multiple={true}
              assetUuids={assetUuids}
              setAssetUuids={setAssetUuids}
            />

            <div className="mt-8">
              {photoUploadError && (
                <p className="text-red-500 text-sm mb-2 text-center">{photoUploadError}</p>
              )}
              <div className="flex justify-between">
                <Button variant="outline" onClick={handleBack} className="flex items-center">
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <Button 
                  onClick={handleNext} 
                  className="flex items-center"
                  variant="default"
                >
                  {photoURLs.length > 0 ? 'Continue' : 'Skip This Step'}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </motion.div>
        );

      case STEPS.DATE_TIME:
        return (
          <motion.div 
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="grid grid-cols-1 gap-6">
              <div>
                <Label className="mb-2 block">Select a date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className={`w-full justify-start text-left font-normal ${!date ? 'text-gray-400' : ''}`}>
                      <Calendar className="mr-2 h-4 w-4" />
                      {date ? format(date, 'PPP') : 'Select a date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent 
                      mode="single" 
                      selected={date} 
                      onSelect={handleDateSelect} 
                      initialFocus 
                      disabled={date => date < new Date()} 
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label className="mb-2 block">Preferred time</Label>
                <RadioGroup value={timeSlot} onValueChange={setTimeSlot} className="grid grid-cols-1 gap-3">
                  {timeOptions.map(option => <div key={option.id}>
                      <label htmlFor={option.id} className={`flex items-center justify-between p-4 rounded-lg cursor-pointer border transition-all ${timeSlot === option.value ? "border-primary bg-primary/5 shadow-md" : "border-gray-200"}`}>
                        <div className="flex items-center">
                          <RadioGroupItem value={option.value} id={option.id} className="mr-3" />
                          <div>
                            <span className="font-medium">{option.label}</span>
                            <p className="text-sm text-gray-500">{option.description}</p>
                          </div>
                        </div>
                        <Clock className={`h-5 w-5 ${option.iconColor}`} />
                      </label>
                    </div>)}
                </RadioGroup>
              </div>
            </div>

            <div className="flex justify-between mt-8">
              <Button variant="outline" onClick={handleBack} className="flex items-center">
                <ChevronLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <Button onClick={handleNext} disabled={!date || !timeSlot} className="flex items-center">
                Continue
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </motion.div>
        );
      case STEPS.FREQUENCY:
        return (
          <motion.div 
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <RadioGroup value={selectedFrequency} onValueChange={setSelectedFrequency} className="space-y-3">
              {frequencyOptions.map(option => <div key={option.id} className="flex items-center">
                  <label htmlFor={option.id} className={`flex items-center justify-between w-full p-4 rounded-lg cursor-pointer border transition-all ${selectedFrequency === option.value ? "border-primary bg-primary/5 shadow-md" : "border-gray-200"}`}>
                    <div className="flex items-center">
                      <RadioGroupItem value={option.value} id={option.id} className="mr-3" />
                      <div>
                        <span className="font-medium">{option.title}</span>
                        <p className="text-sm text-gray-500">{option.description}</p>
                      </div>
                    </div>
                  </label>
                </div>)}
            </RadioGroup>

            <div className="flex justify-between mt-8">
              <Button variant="outline" onClick={handleBack} className="flex items-center">
                <ChevronLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <Button onClick={handleNext} className="flex items-center">
                Continue
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </motion.div>
        );
      case STEPS.BUDGET_LOCATION:
        return (
          <motion.div 
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
            className="space-y-6 h-[calc(60vh)] flex flex-col"
          >
            <div className="flex-grow">
              <div className="h-full flex flex-col space-y-6">
                <div className="flex-grow">
                  <Label htmlFor="description" className="mb-2 block">Describe your project</Label>
                  <Textarea 
                    id="description" 
                    placeholder="Please provide details about what you need help with..." 
                    value={description} 
                    onChange={e => setDescription(e.target.value)} 
                    className="w-full h-full resize-none text-base"
                  />
                </div>

                <div className="pt-4">
                  <Label className="mb-4 block">Attach files (optional)</Label>
                  <div className="flex items-center">
                    <input 
                      type="file" 
                      ref={fileInputRef} 
                      onChange={handleFileChange} 
                      className="hidden" 
                      multiple 
                    />
                    <Button 
                      variant="outline" 
                      onClick={handleFileAttachment} 
                      className="w-full py-6 text-base"
                    >
                      <Upload className="h-5 w-5 mr-2" />
                      Upload
                    </Button>
                  </div>
                  {attachedFiles.length > 0 && (
                    <div className="flex flex-wrap mt-4">
                      {attachedFiles.map((file, index) => (
                        <div key={index} className="mr-2 mb-2 relative">
                          <img src={URL.createObjectURL(file)} alt={`File ${index + 1}`} className="w-20 h-20 rounded object-cover" />
                          <button 
                            onClick={() => removeAttachedFile(index)}
                            className="absolute -top-2 -right-2 bg-red-500 rounded-full p-1 text-white"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="flex justify-between mt-8">
              <Button variant="outline" onClick={handleBack} className="flex items-center">
                <ChevronLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <Button onClick={handleNext} className="flex items-center">
                Continue
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </motion.div>
        );

      case STEPS.REVIEW:
        return (
          <motion.div
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
            className="min-h-[calc(100vh-180px)]"
          >
            <div className="mb-8">
              <Button variant="outline" onClick={handleBack} className="flex items-center">
                <ChevronLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </div>
            <MobileUserInfoForm
              contactInfo={contactInfo}
              errors={errors}
              onContactInfoChange={handleContactInfoChange}
              onPhoneChange={handlePhoneChange}
              onNext={handleSubmit}
              submitAttempted={submitAttempted}
              onAddressSelect={(addressData) => {
                // Update addressDetail with the selected address data
                setAddressDetail({
                  address: addressData.address
                });

                // Store the structured address data to be used in handleSubmit
                setContactInfo(prev => ({
                  ...prev,
                  address: addressData.address
                }));

                // Store the structured address data
                setStructuredAddressData({
                  address: addressData.address,
                  city: addressData.city,
                  state: addressData.state,
                  zipCode: addressData.zipCode
                });
              }}
            />
            <div className="w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 p-4 pt-10 z-10">
              <Button
                  onClick={handleSubmit}
                  className="w-full"
                  variant="default"
                  size="mobile"
              >
                Submit Request  {isLoading && <Loader2 className="h-10 w-10 animate-spin text-white" />}
              </Button>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <SEO
        title="Hire Trusted Local Service Pros – Compare Bids & Get Jobs Done Fast"
        description="Post a job, compare bids, and hire trusted local pros fast. Verified providers. No signup required. Get your home or office project done right with JobON."
        localBusinessSchema={true}
        serviceType="create-job"
        serviceSlug="create-job"
        canonicalUrl={location.pathname}
      />
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Layout hideNav={false} fullWidth>
          {isMobile ? (
            <div className="pb-20">
              {step !== STEPS.REVIEW && (
                <MobileCreateJobHeader
                  title={getStepTitle()}
                  currentStep={getCurrentStepNumber()}
                  totalSteps={getTotalSteps()}
                  onBack={handleBack}
                />
              )}
              <div className={`p-4 ${step === STEPS.REVIEW ? 'pt-14' : ''}`}>
                {renderStep()}
              </div>
              {step !== STEPS.PROPERTY_TYPE &&
                step !== STEPS.SERVICE_CATEGORY &&
                step !== STEPS.SERVICE_TASKS &&
                step !== STEPS.REVIEW && (
                  <div className="fixed bottom-0 left-0 w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 p-4 z-10">
                    <div className="flex justify-between items-center max-w-md mx-auto">
                      <Button variant="outline" onClick={handleBack} className="w-1/3">
                        <ChevronLeft className="h-4 w-4 mr-2" />
                        Back
                      </Button>
                      {step === STEPS.REVIEW ? (
                        <Button
                          onClick={handleSubmit}
                          className="w-2/3"
                        >
                          Submit Request
                        </Button>
                      ) : (
                        <Button
                          onClick={handleNext}
                          disabled={step === STEPS.DATE_TIME && (!date || !timeSlot)}
                          className="w-2/3"
                        >
                          Continue
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Button>
                      )}
                    </div>
                  </div>
                )}
            </div>
          ) : (
            <div className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
              <div className="mb-8">
                <div className="flex items-center mt-4 mb-2">
                  <Progress value={progressPercentage} className="h-2" />
                </div>
                <div className="text-sm text-gray-500 flex justify-between">
                  <span>Step {getCurrentStepNumber()} of {getTotalSteps()}</span>
                  <span>{progressPercentage}% complete</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                {renderStep()}
              </div>
            </div>
          )}
        </Layout>
      </div>
    </>
  );
};

export default CreateJob;