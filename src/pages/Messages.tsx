
import React, { useEffect, useState } from 'react';
import { Layout } from '@/components/Layout';
import { ChatBot } from '@/components/ChatBot';
import { getProviderToMessage, clearProviderToMessage } from '@/utils/messagingUtils';
import { Provider } from '@/components/ProviderCard';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const Messages = () => {
  const [provider, setProvider] = useState<Provider | null>(null);
  const navigate = useNavigate();
  
  useEffect(() => {
    // Get provider from storage when component mounts
    const storedProvider = getProviderToMessage();
    if (storedProvider) {
      setProvider(storedProvider);
      // Clear from storage after retrieving it
      clearProviderToMessage();
    }
    
    return () => {
      // Clean up if component unmounts
      clearProviderToMessage();
    };
  }, []);
  
  const handleBack = () => {
    navigate(-1);
  };
  
  const getProviderInitial = () => {
    return provider?.name ? provider.name.charAt(0) : '';
  };
  
  const stockProfileImages = [
    "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1974&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1974&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=1964&auto=format&fit=crop",
  ];
  
  const getProfileImage = (id: string) => {
    if (!id) return stockProfileImages[0];
    const numericValue = parseInt(id.charAt(id.length - 1), 16) || 0;
    return stockProfileImages[numericValue % stockProfileImages.length];
  };
  
  return (
    <Layout 
      hideMobileNav={true} 
      fullWidth={true} 
      className="flex flex-col bg-gray-50 dark:bg-gray-900"
    >
      <div className="flex-1 flex flex-col h-[calc(100vh-66px)] overflow-hidden pt-4 md:pt-0 fixed-chat-container"> 
        {provider && (
          <div className="bg-white dark:bg-gray-800 p-3 border-b flex items-center shadow-sm">
            <Button 
              variant="ghost" 
              size="icon" 
              className="mr-2" 
              onClick={handleBack}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            
            <Avatar className="h-10 w-10 mr-3">
              <AvatarImage src={getProfileImage(provider.id)} alt={provider.name} />
              <AvatarFallback>{getProviderInitial()}</AvatarFallback>
            </Avatar>
            
            <div>
              <h2 className="font-medium text-base">{provider.name}</h2>
              <p className="text-xs text-gray-500 dark:text-gray-400">{provider.specialty}</p>
            </div>
          </div>
        )}
        
        <ChatBot
          initialMessage={
            provider 
              ? `Hello! I'm interested in your ${provider.specialty} services. Can you tell me more about your availability and rates?` 
              : undefined
          }
          onClose={() => {
            // Placeholder for onClose action. 
            // Could navigate back, clear provider, etc.
            console.log("ChatBot onClose triggered"); 
          }}
        />
      </div>
    </Layout>
  );
};

export default Messages;
