
import React, { useEffect, useState } from 'react';
import { initializeAdminMessagingData } from '@/utils/messagingUtils';
import { AdminMessagingInterface } from '@/components/admin/messaging/AdminMessagingInterface';
import { Loader, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

const AdminMessages = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Initialize mock messaging data when component mounts
  useEffect(() => {
    try {
      initializeAdminMessagingData();
      setIsLoading(false);
    } catch (error) {
      console.error("Error initializing messaging data:", error);
      setHasError(true);
      setIsLoading(false);
    }
  }, []);

  const handleRetry = () => {
    setIsLoading(true);
    setHasError(false);
    
    try {
      initializeAdminMessagingData();
      setIsLoading(false);
    } catch (error) {
      console.error("Error retrying data initialization:", error);
      setHasError(true);
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-gradient-to-b from-indigo-50 to-white dark:from-indigo-950/40 dark:to-gray-900 min-h-screen flex items-center justify-center">
        <div className="text-center animate-pulse">
          <Loader className="h-10 w-10 animate-spin text-indigo-500 mx-auto mb-4" />
          <p className="text-lg font-medium bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">Loading messages...</p>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="bg-gradient-to-b from-indigo-50 to-white dark:from-indigo-950/40 dark:to-gray-900 min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md p-6 bg-white dark:bg-gray-800 shadow-xl rounded-2xl border border-gray-100 dark:border-gray-700">
          <div className="h-16 w-16 bg-red-50 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-500" />
          </div>
          <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-rose-600 to-red-600 bg-clip-text text-transparent">Something went wrong</h2>
          <p className="text-muted-foreground mb-6">
            We couldn't load the messaging interface. Please try again later.
          </p>
          <Button 
            onClick={handleRetry}
            className="bg-gradient-to-r from-indigo-500 to-blue-600 hover:from-indigo-600 hover:to-blue-700 text-white font-medium py-2 px-6 rounded-lg shadow-md hover:shadow-lg transition-all"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 min-h-screen">
      <AdminMessagingInterface />
    </div>
  );
};

export default AdminMessages;
