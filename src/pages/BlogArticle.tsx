import React, { useState, useEffect } from 'react';
import { useLocation, useParams, Navigate } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { BlogSidebar } from '@/components/BlogSidebar';
import { PopularArticles } from '@/components/PopularArticles';
import { formatDate } from '@/lib/utils';
import { Calendar, User, MessageSquare, ArrowLeft, Loader2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import { SEO } from "@/components/SEO.tsx";
import { fetchPostBySlug, fetchRelatedPosts, BlogPost } from '@/services/wordpressApi';

// List of blog post slugs that should use the homepage as canonical URL
const homepageCanonicalSlugs = [
  'choose-right-service-provider',
  'smart-home-technology-review',
  'keep-your-office-bright-simple-ways-to-fight-germs',
  'expert-tips-for-maintaining-a-clean-and-healthy-home',
  'ultimate-guide-spring-cleaning',
  'benefits-of-professional-ac-installation-what-you-need-to-know',
  'home-renovation-prep',
  'spring-cleaning-made-easy-tips-from-the-experts'
];

const BlogArticle: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [currentPost, setCurrentPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const location = useLocation();
  const currentFullUrl = location.pathname;

  useEffect(() => {
    const loadPost = async () => {
      if (!slug) return;

      try {
        setLoading(true);
        setError(null);

        // Fetch the post by slug
        const post = await fetchPostBySlug(slug);

        if (post) {
          setCurrentPost(post);

          // Fetch related posts (posts in the same category)
          // For simplicity, we're just fetching 4 recent posts
          // In a real implementation, you'd use the post's category ID
          const related = await fetchRelatedPosts(post.id, 0, 4);
          setRelatedPosts(related);
        }

        setLoading(false);
      } catch (err) {
        console.error('Error loading blog post:', err);
        setError('Failed to load the article. Please try again later.');
        setLoading(false);
      }
    };

    loadPost();
  }, [slug]);

  const shouldUseHomepageCanonical = slug ? homepageCanonicalSlugs.includes(slug) : false;
  if (shouldUseHomepageCanonical) {
    return <Navigate to="/" replace />;
  }

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12">
          <Link to="/blog" className="text-primary hover:underline inline-flex items-center gap-1 mb-6">
            <ArrowLeft size={16} />
            Back to Blog
          </Link>
          <div className="flex flex-col items-center justify-center py-20">
            <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
            <p className="text-lg text-gray-600">Loading article...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12">
          <Link to="/blog" className="text-primary hover:underline inline-flex items-center gap-1 mb-6">
            <ArrowLeft size={16} />
            Back to Blog
          </Link>
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <p>{error}</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!currentPost) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12">
          <h1 className="text-2xl font-bold mb-4">Article not found</h1>
          <Link to="/blog" className="text-primary hover:underline inline-flex items-center gap-1">
            <ArrowLeft size={16} />
            Back to Blog
          </Link>
        </div>
      </Layout>
    );
  }
  return <Layout>
    <SEO
        title={currentPost.title}
        description={currentPost.excerpt}
        localBusinessSchema={true}
        serviceType="Blog"
        serviceSlug={`blog/${currentPost.slug}`}
        canonicalUrl={shouldUseHomepageCanonical ? "/" : currentFullUrl}
        yoastSEO={currentPost.yoastSEO}
        image={currentPost.image}
    />
      <div className="py-8 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <Link to="/blog" className="text-primary hover:underline inline-flex items-center gap-1 mb-6">
            <ArrowLeft size={16} />
            Back to Blog
          </Link>

          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-8">
              <div className="prose prose-lg dark:prose-invert max-w-none">
                {/* Article Header */}
                <div className="mb-6">

                  <h1 className="text-3xl md:text-4xl font-bold mt-2 mb-4" dangerouslySetInnerHTML={{__html: currentPost.title}}></h1>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400">



                  </div>
                </div>

                {/* Featured Image */}
                <div className="my-6">
                  <img src={currentPost.image} alt={currentPost.title} className="w-full h-auto rounded-lg object-cover" style={{
                  maxHeight: '500px'
                }} />
                </div>

                {/* Article Content */}
                {currentPost.content ? (
                  <div 
                    className="blog-content [&_img]:max-w-full [&_img]:h-auto [&_img]:rounded-lg [&_img]:my-6 
                    [&_iframe]:max-w-full [&_iframe]:rounded-lg [&_iframe]:my-6 
                    [&_h1]:mt-6 [&_h1]:mb-4 [&_h1]:font-bold [&_h1]:leading-tight 
                    [&_h2]:mt-6 [&_h2]:mb-4 [&_h2]:font-bold [&_h2]:leading-tight 
                    [&_h3]:mt-6 [&_h3]:mb-4 [&_h3]:font-bold [&_h3]:leading-tight 
                    [&_h4]:mt-6 [&_h4]:mb-4 [&_h4]:font-bold [&_h4]:leading-tight 
                    [&_h5]:mt-6 [&_h5]:mb-4 [&_h5]:font-bold [&_h5]:leading-tight 
                    [&_h6]:mt-6 [&_h6]:mb-4 [&_h6]:font-bold [&_h6]:leading-tight 
                    [&_p]:mb-4 [&_p]:leading-relaxed 
                    [&_ul]:mb-4 [&_ul]:pl-8 
                    [&_ol]:mb-4 [&_ol]:pl-8 
                    [&_li]:mb-2 
                    [&_blockquote]:border-l-4 [&_blockquote]:border-slate-200 [&_blockquote]:pl-4 [&_blockquote]:italic [&_blockquote]:my-6 
                    [&_a]:text-blue-500 [&_a]:underline [&_a:hover]:no-underline 
                    [&_table]:w-full [&_table]:border-collapse [&_table]:my-6 
                    [&_th]:border [&_th]:border-slate-200 [&_th]:p-2 [&_th]:bg-slate-50 
                    [&_td]:border [&_td]:border-slate-200 [&_td]:p-2 
                    [&_.alignleft]:float-left [&_.alignleft]:mr-4 
                    [&_.alignright]:float-right [&_.alignright]:ml-4 
                    [&_.aligncenter]:block [&_.aligncenter]:mx-auto 
                    after:content-[''] after:table after:clear-both"
                    dangerouslySetInnerHTML={{ __html: currentPost.content }}
                  />
                ) : (
                  <p>Content for this article is not available.</p>
                )}
              </div>

              {/* Author Bio */}
              <div className="mt-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h3 className="text-xl font-bold mb-3">About the Author</h3>
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                    <img src={`https://ui-avatars.com/api/?name=${encodeURIComponent(currentPost.author)}&background=random`} alt={currentPost.author} className="w-full h-full object-cover" />
                  </div>
                  <div>
                    <h4 className="font-semibold">{currentPost.author}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Home service expert with over 10 years of experience in helping homeowners find quality professionals
                      for their home improvement and maintenance needs.
                    </p>
                  </div>
                </div>
              </div>

            </div>

            {/* Sidebar */}
            <div className="lg:col-span-4">
              <BlogSidebar relatedPosts={relatedPosts} />
            </div>
          </div>
        </div>
      </div>

      {/* Add extra padding at the bottom to prevent footer overlap */}
      <div className="pb-20"></div>
    </Layout>;
};
export default BlogArticle;
