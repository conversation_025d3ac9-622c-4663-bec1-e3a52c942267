import React, { useState } from "react";
import { Layout } from "@/components/Layout";
import { SEO } from "@/components/SEO";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Check, X, Briefcase, Calendar, Wrench, Shield, Clock, Zap, TrendingUp, BadgeDollarSign, Users, Award, ChevronDown, PlusCircle, Percent, Bell, BarChart, Repeat, CreditCard, HelpCircle } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import ProviderSignupDialog from "@/components/ProviderSignupDialog";
import PricingTier from "@/components/PricingTier";

const ForProviders = () => {
  const [billingInterval, setBillingInterval] = useState<"monthly" | "yearly">("yearly");

  const getDiscountedPrice = (monthlyPrice: number) => {
    const discountedPrice = (monthlyPrice * 0.8).toFixed(0);
    return `$${discountedPrice}`;
  };

  const starterFeatures = [{
    included: true,
    text: "Access to bidding jobs",
    icon: <Briefcase className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Available",
    text: "Instant Book access",
    icon: <Calendar className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Basic",
    text: "Job Alerts",
    icon: <Bell className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Standard",
    text: "Visibility",
    icon: <Shield className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Basic Dashboard",
    text: "Analytics",
    icon: <BarChart className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: false,
    text: "CRM & Repeat Clients",
    icon: <Repeat className="h-6 w-6 text-gray-300 shrink-0 mt-0.5" />
  }, {
    included: false,
    text: "BNPL (Buy Now, Pay Later)",
    icon: <CreditCard className="h-6 w-6 text-gray-300 shrink-0 mt-0.5" />
  }, {
    included: "Community",
    text: "Support",
    icon: <HelpCircle className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }];

  const proFeatures = [{
    included: true,
    text: "Access to bidding jobs",
    icon: <Briefcase className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Priority Access",
    text: "Instant Book access",
    icon: <Calendar className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Instant Alerts",
    text: "Job Alerts",
    icon: <Bell className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Boosted",
    text: "Visibility",
    icon: <Shield className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Advanced Insights",
    text: "Analytics",
    icon: <BarChart className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: false,
    text: "CRM & Repeat Clients",
    icon: <Repeat className="h-6 w-6 text-gray-300 shrink-0 mt-0.5" />
  }, {
    included: "Add-on",
    text: "BNPL (Buy Now, Pay Later)",
    icon: <CreditCard className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Email Support",
    text: "Support",
    icon: <HelpCircle className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }];

  const eliteFeatures = [{
    included: true,
    text: "Access to bidding jobs",
    icon: <Briefcase className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Top Placement",
    text: "Instant Book access",
    icon: <Calendar className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Real-Time Priority",
    text: "Job Alerts",
    icon: <Bell className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Premium Featured Placement",
    text: "Visibility",
    icon: <Shield className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Full Business Tools",
    text: "Analytics",
    icon: <BarChart className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: true,
    text: "CRM & Repeat Clients",
    icon: <Repeat className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Included",
    text: "BNPL (Buy Now, Pay Later)",
    icon: <CreditCard className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }, {
    included: "Priority Support",
    text: "Support",
    icon: <HelpCircle className="h-6 w-6 text-green-500 shrink-0 mt-0.5" />
  }];

  return <Layout>
    <SEO
        title="Grow Your Service Business – Join as a Provider on JobON"
        description="Join JobON to find jobs, bid on local projects, and get paid faster. Access free tools, smart scheduling, and instant booking. Free to start, scale as you grow."
        localBusinessSchema={true}
        serviceType="For providers"
        serviceSlug="for-providers"
        canonicalUrl="/for-providers"
    />
    <ProviderSignupDialog />
    
    <section className="bg-background pt-16 md:pt-24 lg:pt-32 pb-20">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="flex flex-col lg:flex-row gap-16 items-center space-y-12 lg:space-y-0">
          <div className="w-full lg:w-1/2 space-y-10">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-tight">
              Grow Your Service Business with <span className="text-primary">JobON</span>
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground leading-relaxed">
              Connect with customers, win more jobs, and streamline your workflow with our all-in-one platform for service providers.
            </p>
            <div className="flex flex-col sm:flex-row gap-5">
              <Button size="lg" className="px-8 py-7 text-lg" asChild>
                <Link to="/auth">Join Now</Link>
              </Button>
              <Button variant="outline" size="lg" className="px-8 py-7 text-lg" asChild>
                <Link to="/how-it-works">Learn More</Link>
              </Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-y-6 gap-x-10">
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Find local jobs</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Smart scheduling</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Business tools</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Fast payouts</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Client management</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-6 w-6 text-primary" />
                <span className="text-lg">Growth analytics</span>
              </div>
            </div>
          </div>
          <div className="w-full lg:w-1/2">
            <div className="relative">
              <div className="absolute -top-10 -left-10 w-48 h-48 bg-primary/10 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-16 -right-16 w-64 h-64 bg-blue-400/10 rounded-full blur-3xl"></div>
              <img 
                src="/lovable-uploads/6aa5c008-ec77-4565-9424-5812e712eeb8.png" 
                alt="Service provider using JobON mobile app" 
                className="rounded-2xl shadow-2xl border border-border relative z-10 w-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <section className="py-16 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-y border-border">
      <div className="container px-4 mx-auto max-w-5xl">
        <div className="flex flex-col md:flex-row items-center justify-between gap-8 text-center md:text-left">
          <div className="md:max-w-2xl space-y-4">
            <h2 className="text-3xl font-semibold">Just getting started? Use JobON free and only pay when you win.</h2>
            <p className="text-xl text-muted-foreground">
              Upgrade to Pro or Elite to keep more of your earnings and unlock premium tools.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button size="lg" variant="success" className="py-6 px-8 text-lg whitespace-nowrap" asChild>
              <Link to="/auth">Get Started</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
    
    <section id="pricing-section" className="py-24 md:py-32 bg-muted/30">
      <div className="container px-4 mx-auto max-w-7xl space-y-16">
        <div className="text-center max-w-3xl mx-auto space-y-6">
          <h2 className="text-4xl md:text-5xl font-bold">Simple, transparent pricing</h2>
          <p className="text-xl text-muted-foreground">
            Choose the plan that works for your business needs. Scale as you grow.
          </p>
        </div>
        
        <div className="flex justify-center mb-12">
          <Tabs 
            defaultValue="monthly" 
            className="w-full max-w-sm" 
            value={billingInterval} 
            onValueChange={value => setBillingInterval(value as "monthly" | "yearly")}
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
              <TabsTrigger value="yearly">Yearly (Save 20%)</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          <PricingTier 
            title="Starter" 
            price="Free" 
            description="Perfect for individuals just getting started" 
            commission="20% per job" 
            features={starterFeatures} 
            isYearly={billingInterval === "yearly"} 
            ctaText="Get Started" 
          />
          
          <PricingTier 
            title="Pro" 
            price={billingInterval === "yearly" ? getDiscountedPrice(199) : "$199"} 
            description="For growing businesses needing more opportunities" 
            commission="15% per job" 
            popular={true} 
            highlighted={true} 
            features={proFeatures} 
            isYearly={billingInterval === "yearly"} 
            ctaText="Upgrade to Pro" 
          />
          
          <PricingTier 
            title="Elite" 
            price={billingInterval === "yearly" ? getDiscountedPrice(399) : "$399"} 
            description="For established businesses with high volume needs" 
            commission="12% per job" 
            features={eliteFeatures} 
            ctaText="Upgrade to Elite" 
            isYearly={billingInterval === "yearly"} 
          />
        </div>
        
        <div className="mt-20 text-center">
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Need a custom solution? <Link to="/contact" className="text-primary hover:underline">Contact our sales team</Link> for enterprise pricing and tailored packages.
          </p>
        </div>
      </div>
    </section>
    
    <section className="py-24 md:py-32 bg-background">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="text-center max-w-3xl mx-auto mb-20">
          <h2 className="text-4xl md:text-5xl font-bold">Everything you need to succeed</h2>
          <p className="mt-6 text-xl text-muted-foreground">
            Our comprehensive platform provides all the tools and resources needed to grow your service business.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-10">
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg transition-all hover:shadow-xl">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Briefcase className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-2xl font-semibold mb-4">Find Local Jobs</h3>
            <p className="text-muted-foreground text-lg">
              Browse and bid on local jobs in your service area. Filter by location, price, and service type.
            </p>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="link" className="text-primary font-medium inline-flex items-center mt-6 text-lg p-0">
                  Find Jobs <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="bg-background border-border">
                <DropdownMenuItem asChild>
                  <Link to="/jobs" className="cursor-pointer">Browse All Jobs</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/jobs/plumbing" className="cursor-pointer">Plumbing Jobs</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/jobs/electrical" className="cursor-pointer">Electrical Jobs</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/jobs/landscaping" className="cursor-pointer">Landscaping Jobs</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg transition-all hover:shadow-xl">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Calendar className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-2xl font-semibold mb-4">Join Us</h3>
            <p className="text-muted-foreground text-lg">
              Automate your booking system and sync with your existing calendar to manage appointments efficiently.
            </p>
            <Link to="/auth" className="text-primary font-medium inline-flex items-center mt-6 text-lg">
              Join Us
              <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </Link>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg transition-all hover:shadow-xl">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Wrench className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-2xl font-semibold mb-4">Free Business Tools</h3>
            <p className="text-muted-foreground text-lg">
              Access calculators, templates, and resources to help manage and grow your service business.
            </p>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="link" className="text-primary font-medium inline-flex items-center mt-6 text-lg p-0">
                  All Free Tools <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="bg-background border-border">
                <DropdownMenuItem asChild>
                  <Link to="/free-tools" className="cursor-pointer">All Tools</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/free-tools/pricing-calculator" className="cursor-pointer">Pricing Calculator</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/free-tools/invoice-templates" className="cursor-pointer">Invoice Templates</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/free-tools/marketing-guide" className="cursor-pointer">Marketing Guide</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </section>
    
    <section className="py-24 md:py-32 bg-muted/30">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="text-center max-w-3xl mx-auto mb-20">
          <h2 className="text-4xl md:text-5xl font-bold">Why service providers choose JobON</h2>
          <p className="mt-6 text-xl text-muted-foreground">
            Join thousands of professionals growing their businesses with our platform
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-x-16 gap-y-14">
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <TrendingUp className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Grow Your Business</h3>
              <p className="text-muted-foreground text-lg">Access a steady stream of qualified leads and opportunities in your service area.</p>
            </div>
          </div>
          
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <BadgeDollarSign className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Faster Payments</h3>
              <p className="text-muted-foreground text-lg">Get paid quickly with our secure payment processing system and flexible payout options.</p>
            </div>
          </div>
          
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Zap className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Boost Efficiency</h3>
              <p className="text-muted-foreground text-lg">Streamline operations with our scheduling tools, saving you time and reducing no-shows.</p>
            </div>
          </div>
          
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Shield className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Build Trust</h3>
              <p className="text-muted-foreground text-lg">Earn reviews, showcase your work, and build credibility with potential customers.</p>
            </div>
          </div>
          
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Users className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Customer Management</h3>
              <p className="text-muted-foreground text-lg">Keep track of client history, preferences, and communications in one central location.</p>
            </div>
          </div>
          
          <div className="flex gap-6">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
              <Award className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Stand Out</h3>
              <p className="text-muted-foreground text-lg">Showcase your expertise with verified badges and enhanced profile visibility.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <section className="py-24 md:py-32 bg-background">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="text-center max-w-3xl mx-auto mb-20">
          <h2 className="text-4xl md:text-5xl font-bold">Trusted by service providers</h2>
          <p className="mt-6 text-xl text-muted-foreground">
            Hear from professionals who have grown their business with JobON
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-10">
          <div className="bg-card border border-border rounded-2xl p-8 shadow-lg transition-all hover:shadow-xl">
            <div className="flex items-center mb-6">
              <div className="text-amber-400 flex">
                {[...Array(5)].map((_, i) => <svg key={i} className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>)}
              </div>
            </div>
            <p className="mb-6 text-foreground text-lg">
              "JobON has been a game-changer for my plumbing business. I'm getting quality leads and the scheduling tool has reduced my administrative work by 50%."
            </p>
            <div className="flex items-center">
              <div className="rounded-full bg-primary/10 w-12 h-12 flex items-center justify-center text-primary font-semibold text-lg">
                MS
              </div>
              <div className="ml-4">
                <p className="font-semibold text-lg">Mike Smith</p>
                <p className="text-muted-foreground">Smith Plumbing Services</p>
              </div>
            </div>
          </div>
          
          <div className="bg-card border border-border rounded-2xl p-8 shadow-lg transition-all hover:shadow-xl">
            <div className="flex items-center mb-6">
              <div className="text-amber-400 flex">
                {[...Array(5)].map((_, i) => <svg key={i} className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>)}
              </div>
            </div>
            <p className="mb-6 text-foreground text-lg">
              "The Professional plan gives me a real advantage with priority notifications. I'm able to be one of the first to bid on jobs, which has significantly increased my win rate."
            </p>
            <div className="flex items-center">
              <div className="rounded-full bg-primary/10 w-12 h-12 flex items-center justify-center text-primary font-semibold text-lg">
                JD
              </div>
              <div className="ml-4">
                <p className="font-semibold text-lg">Jennifer Davis</p>
                <p className="text-muted-foreground">Clean & Clear Housekeeping</p>
              </div>
            </div>
          </div>
          
          <div className="bg-card border border-border rounded-2xl p-8 shadow-lg transition-all hover:shadow-xl">
            <div className="flex items-center mb-6">
              <div className="text-amber-400 flex">
                {[...Array(5)].map((_, i) => <svg key={i} className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>)}
              </div>
            </div>
            <p className="mb-6 text-foreground text-lg">
              "Next-day payouts with the Enterprise plan have transformed our cash flow. As a landscaping company with high overhead, this feature alone is worth the subscription."
            </p>
            <div className="flex items-center">
              <div className="rounded-full bg-primary/10 w-12 h-12 flex items-center justify-center text-primary font-semibold text-lg">
                RJ
              </div>
              <div className="ml-4">
                <p className="font-semibold text-lg">Robert Johnson</p>
                <p className="text-muted-foreground">Green Acres Landscaping</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <section className="py-24 md:py-32 bg-muted/30">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="text-center max-w-3xl mx-auto mb-20">
          <h2 className="text-4xl md:text-5xl font-bold">Frequently asked questions</h2>
          <p className="mt-6 text-xl text-muted-foreground">
            Everything you need to know about joining JobON as a service provider
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-10 max-w-5xl mx-auto">
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">How do I get started with JobON?</h3>
            <p className="text-muted-foreground text-lg">
              Simply create a free Starter account, complete your profile, and start browsing available jobs. You can upgrade to Pro or Elite at any time to access additional features.
            </p>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">What are the key differences between plans?</h3>
            <p className="text-muted-foreground text-lg">
              Starter is free with 20% commission, Pro is $199/month with 15% commission and extra features, and Elite is $399/month with 12% commission and full business tools.
            </p>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">How do bids work?</h3>
            <p className="text-muted-foreground text-lg">
              Each plan includes job bidding opportunities. Starter has basic bids, Pro has priority bidding, and Elite offers top placement and unlimited bids.
            </p>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">What is the Scheduling Assistant?</h3>
            <p className="text-muted-foreground text-lg">
              Available on Pro and Elite plans, the Scheduling Assistant helps you manage appointments, send automated reminders, and reduce no-shows.
            </p>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">How do payouts work?</h3>
            <p className="text-muted-foreground text-lg">
              Payout speed varies by plan: Starter accounts receive funds in 14 business days, Pro in 5 days, and Elite the next business day.
            </p>
          </div>
          
          <div className="bg-card border border-border p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-4">Can I change my subscription plan?</h3>
            <p className="text-muted-foreground text-lg">
              Yes, you can upgrade or downgrade your plan at any time. Changes will take effect at the start of your next billing cycle.
            </p>
          </div>
        </div>
        
        <div className="text-center mt-14">
          <p className="text-muted-foreground text-lg">
            Have more questions? Visit our <Link to="/faq" className="text-primary hover:underline">FAQ page</Link> or <Link to="/contact" className="text-primary hover:underline">contact our support team</Link>.
          </p>
        </div>
      </div>
    </section>
    
    <section className="py-24">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="bg-primary/10 border border-primary/20 rounded-3xl p-10 md:p-16 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-primary/5 rounded-full translate-y-1/2 -translate-x-1/2"></div>
          
          <div className="relative z-10 max-w-3xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-bold">Ready to grow your service business?</h2>
            <p className="mt-6 text-xl text-muted-foreground">
              Join thousands of service providers using JobON to find jobs, manage schedules, and grow your business.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-6 justify-center">
              <Button size="lg" className="py-7 px-10 text-lg" asChild>
                <Link to="/auth">Join Now</Link>
              </Button>
              <Button variant="outline" size="lg" className="py-7 px-10 text-lg" asChild>
                <Link to="/contact">Request a Demo</Link>
              </Button>
            </div>
            <p className="mt-8 text-muted-foreground">
              No credit card required to start. Try the Starter plan for free.
            </p>
          </div>
        </div>
      </div>
    </section>
  </Layout>;
};

export default ForProviders;
