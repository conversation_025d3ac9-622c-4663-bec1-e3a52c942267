import { ApiResponse, apiService } from './api';
import axios from 'axios';
import { BASE_API_URL } from '@/lib/constants';

// Base URL for storage assets
const STORAGE_BASE_URL = 'https://dash.jobon.app/storage';

interface UploadResponse {
  urls: string[];
  message?: string;
  id: string;
}

interface ServerUploadResponseItem {
  uuid: string;
  file_name: string;
  url: string;
  mime_type: string;
  file_size: number;
}

interface ServerUploadResponse {
  success: boolean;
  message: string;
  data: ServerUploadResponseItem[];
}

/**
 * Service for handling asset-related operations
 */
export const assetsService = {
  /**
   * Upload multiple files to the server
   * @param files - Array of files to upload
   * @param token - Optional auth token
   * @returns Promise with array of uploaded file URLs
   */
  uploadFiles: async (
    files: File[],
    token?: string
  ): Promise<ApiResponse<UploadResponse>> => {
    try {
      // Create form data with files[] as the field name
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files[]', file);
      });

      // Set headers
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }
      // Don't set Content-Type as it will be set automatically with the boundary

      // Make the request
      const response = await axios.post(
        `${BASE_API_URL}/api/assets/upload`,
        formData,
        {
          headers,
          withCredentials: false
        }
      );

      // Cast the response data to our server response type
      const serverResponse = response.data as ServerUploadResponse;

      // Transform the URLs by adding the storage base URL
      const transformedUrls = serverResponse.data.map(item => 
        `${STORAGE_BASE_URL}${item.url}`
      );

      return {
        data: {
          urls: transformedUrls,
          message: serverResponse.message,
          id: serverResponse.data[0].uuid,
        },
        error: null,
        status: response.status,
        isSuccess: true,
      };
    } catch (error) {
      console.error('Error uploading files:', error);

      if (axios.isAxiosError(error)) {
        return {
          data: null,
          error: error.response?.data?.message || error.message,
          status: error.response?.status || 0,
          isSuccess: false,
        };
      }

      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        status: 0,
        isSuccess: false,
      };
    }
  }
};
