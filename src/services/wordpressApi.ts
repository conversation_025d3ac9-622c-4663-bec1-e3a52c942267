import axios from 'axios';

// Define the base URL for the WordPress API
const API_BASE_URL = 'https://blog.jobon.app/wp-json/wp/v2';

// Utility function to check if a URL is an image URL
const isImageUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false;

  // Check for common image extensions
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
  const lowercaseUrl = url.toLowerCase();

  // Check if URL ends with an image extension or contains image-related paths
  return (
    imageExtensions.some(ext => lowercaseUrl.endsWith(ext)) || 
    lowercaseUrl.includes('/uploads/') ||
    lowercaseUrl.includes('/images/') ||
    lowercaseUrl.includes('/wp-content/uploads/')
  );
};

// Process URLs in the API response data
const processApiUrls = (data: any): any => {
  // Handle string values
  if (typeof data === 'string' && data.includes('blog.jobon.app')) {
    // Don't modify image URLs
    if (isImageUrl(data)) {
      return data;
    }
    // Replace domain in non-image URLs
    return data.replace(/blog\.jobon\.app/g, 'jobon.app');
  }

  // Handle arrays
  if (Array.isArray(data)) {
    return data.map(item => processApiUrls(item));
  }

  // Handle objects
  if (typeof data === 'object' && data !== null) {
    const result: Record<string, any> = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = processApiUrls(data[key]);
      }
    }
    return result;
  }

  // Return unchanged for other data types
  return data;
};

// Interface for WordPress post data
export interface WordPressPost {
  id: number;
  date: string;
  slug: string;
  title: { rendered: string };
  excerpt: { rendered: string };
  content: { rendered: string };
  _embedded?: {
    author?: Array<{ name: string; avatar_urls?: { [key: string]: string } }>;
    'wp:featuredmedia'?: Array<{ source_url: string }>;
    'wp:term'?: Array<Array<{ id: number; name: string; slug: string }>>;
  };
}

// Interface for WordPress category data
export interface WordPressCategory {
  id: number;
  name: string;
  slug: string;
  count: number;
}

// Interface for Yoast SEO data
export interface YoastSEOData {
  title: string;
  description: string;
  canonical: string;
  metaRobotsNoindex: string;
  metaRobotsNofollow: string;
  opengraphTitle: string;
  opengraphDescription: string;
  opengraphImage: string;
  opengraphType: string;
  twitterTitle: string;
  twitterDescription: string;
  twitterImage: string;
  schema: Record<string, any>;
}

// Interface matching our app's BlogPost structure
export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  slug: string;
  date: string;
  author: string;
  commentCount: number;
  image: string;
  content?: string;
  yoastSEO?: YoastSEOData;
}

// Convert WordPress post to our BlogPost format
const convertWordPressPost = (post: WordPressPost): BlogPost => {
  // Process the post data to update URLs
  const processedPost = processApiUrls(post);

  // Extract category from _embedded.wp:term if available
  const categories = processedPost._embedded?.['wp:term']?.[0] || [];
  const category = categories.length > 0 ? categories[0].name : 'Uncategorized';

  // Extract featured image from _embedded.wp:featuredmedia if available
  const featuredMedia = processedPost._embedded?.['wp:featuredmedia']?.[0];
  const imageUrl = featuredMedia?.source_url || 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.0.3';

  // Extract author from _embedded.author if available
  const author = processedPost._embedded?.author?.[0]?.name || 'Unknown Author';

  // Clean up excerpt (remove HTML tags and limit length)
  const cleanExcerpt = processedPost.excerpt.rendered
    .replace(/<\/?[^>]+(>|$)/g, '') // Remove HTML tags
    .trim();

  return {
    id: processedPost.id.toString(),
    title: processedPost.title.rendered,
    excerpt: cleanExcerpt,
    category,
    slug: processedPost.slug,
    date: processedPost.date,
    author,
    commentCount: 0, // WordPress API doesn't provide this directly
    image: imageUrl,
    content: processedPost.content?.rendered
  };
};

// Fetch blog posts with optional pagination and category filtering
export const fetchPosts = async (
  page = 1,
  perPage = 10,
  categoryId?: number
): Promise<{ posts: BlogPost[]; totalPages: number }> => {
  try {
    let url = `${API_BASE_URL}/posts?_embed&page=${page}&per_page=${perPage}`;

    if (categoryId) {
      url += `&categories=${categoryId}`;
    }

    const response = await axios.get(url);
    const posts = response.data.map(convertWordPressPost);

    // Get total pages from headers
    const totalPages = parseInt(response.headers['x-wp-totalpages'] || '1', 10);

    return { posts, totalPages };
  } catch (error) {
    console.error('Error fetching posts:', error);
    return { posts: [], totalPages: 0 };
  }
};

// Fetch Yoast SEO data for a post
export const fetchYoastSEOData = async (postId: number): Promise<YoastSEOData | null> => {
  try {
    // The Yoast REST API endpoint
    const response = await axios.get(`${API_BASE_URL}/posts/${postId}?_fields=yoast_head_json`);

    if (!response.data || !response.data.yoast_head_json) {
      return null;
    }

    // Process the Yoast data to update URLs
    const yoastData = processApiUrls(response.data.yoast_head_json);

    return {
      title: yoastData.title || '',
      description: yoastData.description || '',
      canonical: yoastData.canonical || '',
      metaRobotsNoindex: yoastData.robots?.index || '',
      metaRobotsNofollow: yoastData.robots?.follow || '',
      opengraphTitle: yoastData.og_title || '',
      opengraphDescription: yoastData.og_description || '',
      opengraphImage: yoastData.og_image?.[0]?.url || '',
      opengraphType: yoastData.og_type || '',
      twitterTitle: yoastData.twitter_title || '',
      twitterDescription: yoastData.twitter_description || '',
      twitterImage: yoastData.twitter_image || '',
      schema: yoastData.schema || {}
    };
  } catch (error) {
    console.error('Error fetching Yoast SEO data:', error);
    return null;
  }
};

// Fetch a single post by slug
export const fetchPostBySlug = async (slug: string): Promise<BlogPost | null> => {
  try {
    const response = await axios.get(`${API_BASE_URL}/posts?_embed&slug=${slug}`);

    if (response.data.length === 0) {
      return null;
    }

    const post = convertWordPressPost(response.data[0]);

    // Fetch Yoast SEO data if available
    try {
      const yoastSEO = await fetchYoastSEOData(response.data[0].id);
      if (yoastSEO) {
        post.yoastSEO = yoastSEO;
      }
    } catch (yoastError) {
      console.error('Error fetching Yoast SEO data:', yoastError);
      // Continue without Yoast SEO data
    }

    return post;
  } catch (error) {
    console.error('Error fetching post by slug:', error);
    return null;
  }
};

// Fetch all categories
export const fetchCategories = async (): Promise<WordPressCategory[]> => {
  try {
    const response = await axios.get(`${API_BASE_URL}/categories`);
    // Process the category data to update URLs
    return processApiUrls(response.data);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
};

// Fetch featured posts (most recent posts)
export const fetchFeaturedPosts = async (count = 3): Promise<BlogPost[]> => {
  try {
    const { posts } = await fetchPosts(1, count);
    return posts;
  } catch (error) {
    console.error('Error fetching featured posts:', error);
    return [];
  }
};

// Fetch related posts (posts in the same category, excluding the current post)
export const fetchRelatedPosts = async (
  postId: string,
  categoryId: number,
  count = 4
): Promise<BlogPost[]> => {
  try {
    const url = `${API_BASE_URL}/posts?_embed&categories=${categoryId}&exclude=${postId}&per_page=${count}`;
    const response = await axios.get(url);
    // Process the response data to update URLs and then convert to BlogPost format
    return response.data.map(convertWordPressPost);
  } catch (error) {
    console.error('Error fetching related posts:', error);
    return [];
  }
};

// Fetch popular posts (using various methods with fallbacks)
export const fetchPopularPosts = async (count = 4, excludeId?: string): Promise<BlogPost[]> => {
  try {
    let url = '';
    let posts: BlogPost[] = [];

    // Method 1: Try WordPress Popular Posts plugin API if available
    try {
      // The WordPress Popular Posts plugin typically exposes an endpoint like this
      url = `${API_BASE_URL.replace('/wp/v2', '/wordpress-popular-posts/v1')}/popular-posts?limit=${count}`;
      if (excludeId) {
        url += `&exclude=${excludeId}`;
      }

      const response = await axios.get(url);
      if (response.data && response.data.length > 0) {
        // Process the response data to update URLs and then convert to BlogPost format
        posts = response.data.map(convertWordPressPost);
        return posts;
      }
    } catch (err) {
      console.log(err);
    }

    // Method 2: Try to get posts with most comments
    try {
      url = `${API_BASE_URL}/posts?_embed&orderby=comment_count&order=desc&per_page=${count}`;
      if (excludeId) {
        url += `&exclude=${excludeId}`;
      }

      const response = await axios.get(url);
      if (response.data && response.data.length > 0) {
        // Process the response data to update URLs and then convert to BlogPost format
        posts = response.data.map(convertWordPressPost);
        return posts;
      }
    } catch (err) {
      console.log('Failed to fetch posts by comment count, falling back to recent posts');
    }

    // Method 3: Fallback to most recent posts
    // fetchPosts already processes URLs through convertWordPressPost
    const { posts: recentPosts } = await fetchPosts(1, count);

    // If we have an excludeId, filter it out
    if (excludeId) {
      return recentPosts.filter(post => post.id !== excludeId);
    }

    return recentPosts;
  } catch (error) {
    console.error('Error fetching popular posts:', error);
    return [];
  }
};
