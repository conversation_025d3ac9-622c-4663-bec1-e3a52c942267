import { ApiResponse, apiService } from './api';

// Interface for active users count response
export interface ActiveUsersCount {
  activeProviders: number;
  activeCustomers: number;
}

// User statistics service
export const userStatsService = {
  // Get count of active users (both providers and customers)
  getActiveUsersCount: (token?: string, roleId?: number): Promise<ApiResponse<ActiveUsersCount>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    let endpoint = '/api/user/count-active';
    if (roleId) {
      endpoint += `?role_id=${roleId}`;
    }

    return apiService<ActiveUsersCount>(endpoint, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },
};
