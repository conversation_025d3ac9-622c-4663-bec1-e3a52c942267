import { useState, useEffect, useCallback, useRef } from 'react';
import debounce from 'lodash/debounce';
import mapboxClient from '../lib/mapbox-client';


interface AddressResult {
  address: string; // Full address string
  city: string;
  state: string; // State abbreviation (e.g., "CA")
  zipCode: string;
  // Optionally, add coordinates if needed later
  // longitude?: number;
  // latitude?: number;
}

interface MinimalGeocodeFeature {
  id: string; // Typically features have an ID
  place_name?: string;
  text?: string;
  place_type: string[];
  center?: [number, number]; // [longitude, latitude]
  context?: Array<{
    id: string;
    short_code?: string;
    text: string;
    wikidata?: string; // Optional, often present
  }>;
  // Add other properties like 'properties', 'geometry', 'relevance', 'bbox' if needed by the application.
}

interface MinimalGeocodeRequest {
  query: string;
  countries?: string[];
  limit?: number;
  // Types for Mapbox geocoding, e.g., 'country', 'region', 'postcode', 'district', 'place', 'locality', 'neighborhood', 'address', 'poi'
  types?: ('country' | 'region' | 'postcode' | 'district' | 'place' | 'locality' | 'neighborhood' | 'address' | 'poi')[];
  autocomplete?: boolean;
  // Add other request options like 'bbox', 'proximity', 'language' if needed.
}


interface UseAddressAutocompleteProps {
  debounceMs?: number;
  country?: string; // e.g., 'US'
  limit?: number;
  types?: MinimalGeocodeRequest['types'];
}

export function useAddressAutocomplete({
  debounceMs = 300,
  country = 'US',
  limit = 5,
  types = ['address', 'postcode', 'place', 'locality', 'region'],
}: UseAddressAutocompleteProps = {}) {
  const [query, setQuery] = useState<string>('');
  const [suggestions, setSuggestions] = useState<AddressResult[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedAddress, setSelectedAddress] = useState<AddressResult | null>(null);
  const justSelectedAddressRef = useRef<boolean>(false); // Ref to track if selection just happened


  const parseMapboxFeature = (feature: MinimalGeocodeFeature): AddressResult => {
    let displayAddress = feature.place_name || '';

    if (displayAddress.endsWith(', United States')) {
      displayAddress = displayAddress.substring(0, displayAddress.length - ', United States'.length);
    }

    const address = displayAddress; // Use the modified address
    let city = '';
    let state = ''; // Expecting state abbreviation
    let zipCode = '';
    // const longitude = feature.center?.[0]; 
    // const latitude = feature.center?.[1];

    if (feature.context) {
      for (const item of feature.context) {
        const itemType = item.id.split('.')[0];
        if (itemType === 'place' || itemType === 'locality') {
          city = item.text;
        }
        if (itemType === 'region') {
          state = item.short_code || item.text;
        }
        if (itemType === 'postcode') {
          zipCode = item.text;
        }
      }
    }
    
    if (!city && feature.text && feature.place_type && (feature.place_type.includes('locality') || feature.place_type.includes('place'))) {
        city = feature.text ?? ''; // Use nullish coalescing for a safe assignment
    }

         if (!state || !zipCode) {
        const parts = address.split(',').map((part: string) => part.trim()); // Explicitly type 'part'
        if (parts.length >= 2) {
            const stateZipPart = parts[parts.length - 2]; // e.g., "CA 90210" or "California 90210"
            const stateZipMatch = stateZipPart.match(/^([A-Za-z\s]+?)\s+(\d{5}(-\d{4})?)$/); // Matches "Some State Name 12345" or "SS 12345"
            if (stateZipMatch) {
                if (!state) state = stateZipMatch[1].trim(); // This might be full state name, conversion to abbr might be needed
                if (!zipCode) zipCode = stateZipMatch[2];
            } else {
                 if(!state) state = stateZipPart;
            }
        }
        if (!zipCode && parts.length >=1) {
            const lastPart = parts[parts.length -1];
            const zipMatch = lastPart.match(/^\d{5}(-\d{4})?$/);
            if(zipMatch) zipCode = zipMatch[0];
        }
    }


    return {
      address,
      city,
      state,
      zipCode,
      // longitude,
      // latitude,
    };
  };

  // Debounced function to fetch address suggestions from Mapbox
  const fetchSuggestions = useCallback(
    debounce(async (searchQuery: string) => {
      if (!searchQuery || searchQuery.length < 3) {
        setSuggestions([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Use the locally defined MinimalGeocodeRequest type
        const geocodeRequest: MinimalGeocodeRequest = {
          query: searchQuery,
          countries: country ? [country] : undefined,
          limit,
          types,
          autocomplete: true, // Enable autocomplete for better suggestions
        };
        
        const response = await (mapboxClient.forwardGeocode(geocodeRequest as any).send()); // Use 'as any' for request if types are too strict or mismatch

        if (response && response.body && response.body.features) {
          const parsedSuggestions = (response.body.features as MinimalGeocodeFeature[]).map(parseMapboxFeature);
          setSuggestions(parsedSuggestions);
        } else {
          setSuggestions([]);
        }
      } catch (err: unknown) {
        console.error("Mapbox API Error:", err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching address suggestions.');
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs),
    [debounceMs, country, limit, JSON.stringify(types)] // Add dependencies
  );

  // Update suggestions when query changes
  useEffect(() => {
    if (justSelectedAddressRef.current) {
      justSelectedAddressRef.current = false;
      return;
    }

    fetchSuggestions(query);

    // Cleanup function to cancel any pending debounced calls
    return () => {
      fetchSuggestions.cancel();
    };
  }, [query, fetchSuggestions]); // fetchSuggestions callback itself doesn't change often

  // Handle selecting an address from suggestions
  const handleSelectAddress = (address: AddressResult) => {
    justSelectedAddressRef.current = true; // Signal that the upcoming query update is from a selection
    setSelectedAddress(address);
    setQuery(address.address); // This will trigger the useEffect above
    setSuggestions([]); // Clear suggestions to close the dropdown
  };

  // Reset the selected address and query
  const resetAddress = () => {
    setSelectedAddress(null);
    setQuery('');
    setSuggestions([]);
    justSelectedAddressRef.current = false; // Ensure ref is reset if user clears
  };

  return {
    query,
    setQuery,
    suggestions,
    isLoading,
    error,
    selectedAddress,
    handleSelectAddress,
    resetAddress,
  };
}
