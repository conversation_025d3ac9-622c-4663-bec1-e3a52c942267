
import { useMediaQuery } from "./use-media-query";
import { useIsMobile } from "./use-mobile";

/**
 * A custom hook that provides common UI helper functions
 * Consolidates various responsive checks in one place
 */
export function useUIHelpers() {
  const isMobileUsingBreakpoint = useIsMobile();
  const isTablet = useMediaQuery("(min-width: 640px) and (max-width: 1024px)");
  const isDesktop = useMediaQuery("(min-width: 1024px)");
  const isMobileOrTablet = useMediaQuery("(max-width: 1023px)");
  
  // Return consolidated UI helper functions
  return {
    isMobile: isMobileUsingBreakpoint,
    isTablet,
    isDesktop,
    isMobileOrTablet
  };
}
