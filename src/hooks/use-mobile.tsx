
import * as React from "react";

// This matches the sm breakpoint in Tailwind (640px)
const MO<PERSON>LE_BREAKPOINT = 640;

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean>(
    typeof window !== "undefined" ? window.innerWidth < MOBILE_BREAKPOINT : false
  );

  React.useEffect(() => {
    const checkMobile = () => {
      const newIsMobile = window.innerWidth < MOBILE_BREAKPOINT;
      setIsMobile(newIsMobile);
    };
    
    window.addEventListener("resize", checkMobile);
    checkMobile(); // Check on initial render
    
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return isMobile;
}
