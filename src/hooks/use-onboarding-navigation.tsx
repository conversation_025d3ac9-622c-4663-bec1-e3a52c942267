
import { useNavigate } from 'react-router-dom';
import { useToast } from './use-toast';

export const useOnboardingNavigation = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleTaskNavigation = (taskId: string) => {
    switch (taskId) {
      case 'profile':
        navigate('/provider/profile#business-info');
        break;
      case 'contact':
        navigate('/provider/profile#contact-verification');
        break;
      case 'calendar':
        navigate('/provider/calendar');
        break;
      case 'bid':
        navigate('/provider/leads');
        toast({
          title: "Ready to win jobs?",
          description: "Browse available leads and submit your first bid!",
          variant: "default",
        });
        break;
      default:
        break;
    }
  };

  return { handleTaskNavigation };
};
