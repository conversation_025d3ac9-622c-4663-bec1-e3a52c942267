
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Clock, 
  MapPin, 
  DollarSign,
  ArrowRight,
  Camera
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { categories } from '@/components/CategoryList';
import { useIsMobile } from '@/hooks/use-mobile';
import { JobDataType } from "@/pages/Jobs.tsx";
import { 
  getJobAccentColor, 
  markJobAsViewed, 
  isJobViewed, 
  formatJobTimeline,
  getServicePlaceholder 
} from "@/utils/jobCardUtils";
import { cn } from "@/lib/utils";

// Define the status styles mapping
const statusStyles = {
  open: "bg-green-50 text-green-800 dark:bg-green-900/30 dark:text-green-300 border-green-800",
  assigned: "bg-blue-50 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
  completed: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
};

export interface JobCardProps {
  id: string;
  title: string;
  description: string;
  price: number;
  location: string;
  dueDate: string;
  status: 'open' | 'assigned' | 'completed';
  category: string;
  
  // Urgency tags
  isUrgent?: boolean;
  isTomorrow?: boolean; 
  isSameDay?: boolean;
  
  // Freshness tags
  isNew?: boolean;
  isUpdated?: boolean;
  isExpiring?: boolean;
  
  // Incentive tags
  isHighPaying?: boolean;
  hasTips?: boolean;
  hasBonus?: boolean;
  
  // Project type tags
  isOneTime?: boolean;
  isRecurring?: boolean;
  isRemote?: boolean;
  isOnSite?: boolean;
  
  // Location tags
  isNearby?: boolean;
  detail: JobDataType;
}

export const JobCard: React.FC<JobCardProps> = ({
  id,
  title,
  description,
  location,
  status,
  category,
  isNew = false,
  detail
}) => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [viewed, setViewed] = useState(false);
  
  const categoryData = categories.find(c => c?.id === category) || categories[5];
  const accentColors = getJobAccentColor(id);
  
  // Check if job has been viewed
  useEffect(() => {
    setViewed(isJobViewed(id));
  }, [id]);
  
  const handleCardClick = () => {
    markJobAsViewed(id);
    setViewed(true);
    window.scrollTo({ top: 0, behavior: 'smooth' });
    navigate(`/job/${id}`);
  };

  // Get the primary image or fallback
  const primaryImage = detail?.assets?.[0]?.url || getServicePlaceholder(category);
  const imageCount = detail?.assets?.length || 0;
  
  // Get real data from detail
  const realBudget = detail?.budget || 0;
  const timeline = formatJobTimeline(detail?.schedule, detail?.createdAt);
  const serviceTasks = detail?.service?.tasks?.slice(0, isMobile ? 2 : 3) || [];

  return (
    <div 
      className={cn(
        "bg-white dark:bg-gray-800 flex flex-col justify-between shadow-md rounded-lg overflow-hidden border-l-4 border-gray-300 dark:border-gray-600 cursor-pointer hover:shadow-lg transition-all duration-200",
        accentColors.border,
        viewed ? "opacity-85" : "opacity-100",
        "hover:scale-[1.01] hover:shadow-xl"
      )}
      onClick={handleCardClick}
    >
      <div>
        {/* Category header with image */}
        <div className={cn("px-4 py-3 border-b border-gray-100 dark:border-gray-700 flex items-center justify-between", accentColors.bg)}>
          <div className="flex items-center gap-2">
            {!viewed && (
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse flex-shrink-0" />
            )}
            {categoryData?.icon && (
              <div className={cn("rounded-full p-1", accentColors.icon)}>
                {React.cloneElement(categoryData.icon, { className: "h-4 w-4 text-primary dark:text-primary" })}
              </div>
            )}
            <span className="font-medium text-sm text-gray-900 dark:text-gray-100">{category}</span>
            {isNew && (
              <Badge className="text-xs bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-700 px-2 py-0.5">
                New
              </Badge>
            )}
          </div>

          {/* Image thumbnail */}
          <div className="relative flex-shrink-0">
            <div className={cn("w-14 h-14 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600", !imageLoaded && "animate-pulse")}>
              <img
                src={primaryImage}
                alt={`${title} preview`}
                className="w-full h-full object-cover transition-opacity duration-200"
                onLoad={() => setImageLoaded(true)}
                loading="lazy"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.onerror = null;
                  target.src = 'images/default-image.jpg';
                }}

              />
            </div>
            {imageCount > 1 && (
              <div className="absolute -bottom-1 -right-1 bg-black/70 dark:bg-black/80 text-white text-xs px-1.5 py-0.5 rounded-full flex items-center gap-0.5">
                <Camera className="h-2.5 w-2.5" />
                {imageCount}
              </div>
            )}
          </div>
        </div>

        {/* Main content */}
        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2 mb-2 capitalize">
            {title}
          </h3>

          <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-3">
            {description}
          </p>

          {/* Service tasks */}
          {serviceTasks.length > 0 && (
            <div className="flex flex-wrap gap-1.5 mb-4">
              {serviceTasks.map((task, index) => (
                <span key={index} className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full">
                  {task}
                </span>
              ))}
            </div>
          )}

          {/* Job Details */}
          <div className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
              <span className="truncate">{location}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
              <span className="truncate">{timeline}</span>
            </div>
            {realBudget > 0 && (
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400 flex-shrink-0" />
                <span className="font-medium text-green-700 dark:text-green-400">
                  {realBudget.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* CTA Button */}
      <div className="p-4 pt-0">
        <Button
          size="sm"
          variant="default"
          className="w-full flex items-center justify-center gap-2 pointer-events-none"
        >
          {viewed ? 'View Again' : 'View Details'}
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
