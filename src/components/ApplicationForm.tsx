
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';

const PRIMARY_COLOR = "#9b87f5";

function validateEmail(email: string) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}
function validatePhone(phone: string) {
  // Accepts 1234567890, ************, (*************, ************, ************
  const re = /^(\(\d{3}\)|\d{3})[- .]?\d{3}[- .]?\d{4}$/;
  const digits = phone.replace(/\D/g, '');
  return re.test(phone) && digits.length === 10;
}

export default function ApplicationForm() {
  const [fields, setFields] = useState({
    fullName: '',
    email: '',
    phone: '',
    position: '',
    linkedin: '',
    coverLetter: '',
    file: null as File | null,
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFields(prev => ({ ...prev, [name]: value }));
    // Remove errors as user types
    setErrors(prev => ({ ...prev, [name]: '' }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFields(prev => ({ ...prev, file: e.target.files![0] }));
      setErrors(prev => ({ ...prev, file: '' }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Validate
    const err: { [key: string]: string } = {};
    if (!fields.fullName.trim()) err.fullName = "Full name is required";
    if (!fields.email.trim()) err.email = "Email is required";
    else if (!validateEmail(fields.email)) err.email = "Please enter a valid email address.";

    if (!fields.phone.trim()) err.phone = "Phone number is required";
    else if (!validatePhone(fields.phone)) err.phone = "Please enter a valid phone number.";

    if (fields.file === null) err.file = "Please upload your resume";

    setErrors(err);
    if (Object.keys(err).length === 0) {
      // Submit logic here
      alert("🎉 Your application was submitted successfully!");
    }
  };

  return (
    <form className="flex flex-col gap-6" onSubmit={handleSubmit} noValidate>
      {/* Full Name */}
      <div>
        <label htmlFor="fullName" className="block text-sm font-medium mb-1">
          Full Name
        </label>
        <Input
          id="fullName"
          name="fullName"
          placeholder="Your full name"
          autoComplete="name"
          value={fields.fullName}
          onChange={handleChange}
          className={`border-primary rounded-md focus:border-primary focus-visible:ring-2 focus-visible:ring-primary/30`}
          required
        />
        {errors.fullName && (
          <p className="text-sm text-destructive mt-1">{errors.fullName}</p>
        )}
      </div>

      {/* Email */}
      <div>
        <label htmlFor="email" className="block text-sm font-medium mb-1">
          Email
        </label>
        <Input
          id="email"
          name="email"
          type="email"
          placeholder="<EMAIL>"
          autoComplete="email"
          value={fields.email}
          onChange={handleChange}
          className={`border-primary rounded-md focus:border-primary focus-visible:ring-2 focus-visible:ring-primary/30`}
          required
        />
        {errors.email && (
          <p className="text-sm text-destructive mt-1">{errors.email}</p>
        )}
      </div>

      {/* Phone */}
      <div>
        <label htmlFor="phone" className="block text-sm font-medium mb-1">
          Phone Number
        </label>
        <Input
          id="phone"
          name="phone"
          type="tel"
          placeholder="(*************"
          autoComplete="tel"
          value={fields.phone}
          onChange={handleChange}
          className={`border-primary rounded-md focus:border-primary focus-visible:ring-2 focus-visible:ring-primary/30`}
          required
        />
        {errors.phone && (
          <p className="text-sm text-destructive mt-1">{errors.phone}</p>
        )}
      </div>

      {/* Position */}
      <div>
        <label htmlFor="position" className="block text-sm font-medium mb-1">
          Position
        </label>
        <Input
          id="position"
          name="position"
          placeholder="Position applying for"
          autoComplete="off"
          value={fields.position}
          onChange={handleChange}
          className={`border-primary rounded-md focus:border-primary focus-visible:ring-2 focus-visible:ring-primary/30`}
        />
      </div>

      {/* LinkedIn */}
      <div>
        <label htmlFor="linkedin" className="block text-sm font-medium mb-1">
          LinkedIn Profile (optional)
        </label>
        <Input
          id="linkedin"
          name="linkedin"
          type="url"
          placeholder="https://linkedin.com/in/yourprofile"
          autoComplete="off"
          value={fields.linkedin}
          onChange={handleChange}
          className={`border-primary rounded-md focus:border-primary focus-visible:ring-2 focus-visible:ring-primary/30`}
        />
      </div>

      {/* Cover Letter */}
      <div>
        <label htmlFor="coverLetter" className="block text-sm font-medium mb-1">
          Cover Letter
        </label>
        <Textarea
          id="coverLetter"
          name="coverLetter"
          placeholder="Write a brief cover letter"
          rows={5}
          value={fields.coverLetter}
          onChange={handleChange}
          className={`border-primary rounded-md focus:border-primary focus-visible:ring-2 focus-visible:ring-primary/30`}
        />
      </div>

      {/* Resume */}
      <div>
        <label htmlFor="file" className="block text-sm font-medium mb-1">
          Upload Resume (PDF/DOCX)
        </label>
        <input
          id="file"
          name="file"
          type="file"
          accept=".pdf,.doc,.docx"
          onChange={handleFileChange}
          className="block w-full border border-primary rounded-md bg-background px-3 py-2 text-sm text-gray-700 file:mr-2 file:py-2 file:px-4 file:rounded file:border-0 file:bg-primary file:text-white hover:file:bg-primary/90 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary/30"
          required
        />
        {errors.file && (
          <p className="text-sm text-destructive mt-1">{errors.file}</p>
        )}
      </div>

      <Button type="submit" className="w-full mt-2" size="lg">
        Submit Application
      </Button>
    </form>
  );
}
