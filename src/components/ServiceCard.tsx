
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { ArrowRight } from 'lucide-react';

interface ServiceCardProps {
  id: string;
  title: string;
  icon: React.ReactNode;
  description: string;
  color: string;
  borderColor: string;
  iconBgColor?: string;
  iconColor?: string;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({ 
  id, title, icon, description, color, borderColor, iconBgColor, iconColor
}) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  
  const handleCardClick = () => {
    navigate(`/jobs?category=${encodeURIComponent(title)}`);
  };
  
  return (
    <div 
      className="group cursor-pointer h-full w-full transition-all duration-300"
      onClick={handleCardClick}
    >
      <div className={`
        rounded-xl overflow-hidden border-2 ${borderColor} 
        p-4 flex flex-col items-center text-center h-full 
        bg-white dark:bg-gray-800 shadow-lg hover:shadow-xl 
        hover:translate-y-[-2px] hover:scale-[1.02]
        transition-all duration-300 ease-out
        active:scale-[0.98] active:shadow-md
        ${color}
      `}>
        <div className={`
          w-12 h-12 rounded-xl flex items-center justify-center mb-3 
          ${iconBgColor || 'bg-white dark:bg-gray-700'} shadow-md
          group-hover:shadow-lg transition-shadow duration-300
        `}>
          {React.cloneElement(icon as React.ReactElement, { 
            className: `${isMobile ? 'h-6 w-6' : 'h-7 w-7'} ${iconColor || 'dark:text-white'} transition-transform duration-300 group-hover:scale-110`,
          })}
        </div>
        
        <h3 className="text-base font-bold mb-2 text-gray-900 dark:text-white group-hover:text-primary dark:group-hover:text-primary transition-colors duration-300">
          {title}
        </h3>
        
        {!isMobile && (
          <div className="flex-grow flex flex-col justify-between">
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 leading-relaxed">
              {description}
            </p>
            
            <div className="flex items-center justify-center mt-auto opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
              <span className="text-sm font-semibold flex items-center gap-2 text-gray-700 dark:text-gray-300 bg-white/80 dark:bg-gray-700/80 px-4 py-2 rounded-full border border-gray-200 dark:border-gray-600">
                Find help <ArrowRight size={14} />
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
