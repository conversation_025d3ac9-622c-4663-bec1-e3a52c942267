
import { Crown, Star, Briefcase, MapPin, Clock, MessageSquare, PhoneCall } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { useState } from 'react';
import { RequestBidDialog } from './RequestBidDialog';

interface ProviderCardProps {
  provider: {
    id: string;
    avatarUrl?: string;
    initials?: string;
    businessName: string;
    rating: number;
    reviewCount: number;
    jobsCompleted: number;
    distance: string;
    responseTime: string;
    badges?: string[];
    isFeatured?: boolean;
  };
  onClick: () => void;
}

export const ProviderCardNew = ({
  provider,
  onClick
}: ProviderCardProps) => {
  const [isRequestBidOpen, setIsRequestBidOpen] = useState(false);

  const handleRequestBid = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsRequestBidOpen(true);
  };

  return (
    <div className={`provider-card flex flex-col ${provider.isFeatured ? 'provider-card-featured' : ''}`}>
      {provider.isFeatured && (
        <div className="absolute -top-2 left-0 right-0 flex justify-center z-10">
          <span className="featured-badge">
            <Crown className="h-3 w-3 mr-0.5" />
            Featured Provider
          </span>
        </div>
      )}
      
      <div className="p-4 pb-3">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            {provider.avatarUrl ? (
              <img 
                src={provider.avatarUrl} 
                alt={`${provider.businessName}`} 
                className="provider-avatar"
              />
            ) : (
              <div className="provider-avatar flex items-center justify-center bg-primary text-white text-lg font-semibold">
                {provider.initials || "??"}
              </div>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-bold truncate">{provider.businessName}</h3>
            
            <div className="flex items-center mt-1">
              <div className="flex items-center">
                <Star className="h-4 w-4 text-amber-500 fill-amber-500" />
                <span className="ml-1 font-semibold">{provider.rating}</span>
              </div>
              <span className="mx-1 text-gray-400">•</span>
              <span className="text-sm text-gray-600 dark:text-gray-400">{provider.reviewCount} reviews</span>
            </div>
          
            <div className="flex flex-wrap gap-x-4 gap-y-1.5 mt-3">
              <div className="provider-stat">
                <Briefcase className="h-4 w-4 text-gray-500" />
                <span>{provider.jobsCompleted} jobs</span>
              </div>
              
              <div className="provider-stat">
                <MapPin className="h-4 w-4 text-gray-500" />
                <span>{provider.distance}</span>
              </div>
              
              <div className="provider-stat">
                <Clock className="h-4 w-4 text-gray-500" />
                <span>{provider.responseTime} response</span>
              </div>
            </div>
            
            {provider.badges && provider.badges.length > 0 && (
              <div className="mt-3 flex flex-wrap gap-2">
                {provider.badges.map((badge, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {badge}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="mt-auto border-t border-gray-100 dark:border-gray-700 p-3">
        <div className="flex flex-col md:flex-row gap-2">
          <Button 
            className="provider-action-btn provider-action-btn-primary flex-1" 
            onClick={onClick}
          >
            View Profile
          </Button>
          
          <div className="flex gap-2 w-full md:w-auto">
            <Button 
              variant="outline" 
              className="provider-action-btn provider-action-btn-secondary flex-1 md:flex-auto"
              onClick={handleRequestBid}
            >
              Request Bid
            </Button>
            
            <div className="hidden md:flex gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="provider-action-btn provider-action-btn-secondary"
              >
                <MessageSquare className="h-4 w-4 mr-1" />
                Message
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                className="provider-action-btn provider-action-btn-secondary"
              >
                <PhoneCall className="h-4 w-4 mr-1" />
                Contact
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Request Bid Dialog */}
      <RequestBidDialog
        isOpen={isRequestBidOpen}
        onClose={() => setIsRequestBidOpen(false)}
        providerId={provider.id}
        providerName={provider.businessName}
      />
    </div>
  );
};
