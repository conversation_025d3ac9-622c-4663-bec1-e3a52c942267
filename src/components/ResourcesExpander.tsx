
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from './ui/button';

const ResourcesExpander = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        onClick={toggleExpanded}
        className="flex items-center justify-center py-2 px-3 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white h-auto"
      >
        <span className="text-sm">Support & Resources</span>
        {isExpanded ? (
          <ChevronUp className="ml-2 h-4 w-4" />
        ) : (
          <ChevronDown className="ml-2 h-4 w-4" />
        )}
      </Button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="absolute bottom-full right-0 mb-2 w-[280px] overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg"
          >
            <div className="p-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2 text-sm">Company</h3>
                  <ul className="space-y-1">
                    <li><Link to="/about-us" className="text-sm text-gray-600 dark:text-gray-400 hover:text-primary">About Us</Link></li>
                    <li><Link to="/careers" className="text-sm text-gray-600 dark:text-gray-400 hover:text-primary">Careers</Link></li>
                    <li><Link to="/blog" className="text-sm text-gray-600 dark:text-gray-400 hover:text-primary">Blog</Link></li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2 text-sm">Resources</h3>
                  <ul className="space-y-1">
                    <li><Link to="/how-it-works" className="text-sm text-gray-600 dark:text-gray-400 hover:text-primary">How It Works</Link></li>
                    <li><Link to="/faq" className="text-sm text-gray-600 dark:text-gray-400 hover:text-primary">FAQ</Link></li>
                    <li><Link to="/professionals" className="text-sm text-gray-600 dark:text-gray-400 hover:text-primary">Find Professionals</Link></li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ResourcesExpander;
