
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { saveUserInfo } from '@/utils/messagingUtils';
import { MessageSquare } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface UserRegistrationFormProps {
  onComplete: () => void;
  onCancel?: () => void;
}

export const UserRegistrationForm: React.FC<UserRegistrationFormProps> = ({ 
  onComplete,
  onCancel 
}) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name || !email) {
      toast({
        variant: "destructive",
        title: "Required Fields",
        description: "Please fill in your name and email to continue."
      });
      return;
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast({
        variant: "destructive",
        title: "Invalid Email",
        description: "Please enter a valid email address."
      });
      return;
    }
    
    setIsSubmitting(true);
    
    // Save user information
    saveUserInfo({
      name,
      email,
      phone,
      zipCode
    });
    
    setTimeout(() => {
      setIsSubmitting(false);
      toast({
        title: "Registration Complete",
        description: "You can now message service providers."
      });
      onComplete();
    }, 1000);
  };
  
  return (
    <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <div className="flex items-center gap-2 mb-4 text-primary">
        <MessageSquare className="h-5 w-5" />
        <h2 className="text-lg font-semibold">Complete Your Profile</h2>
      </div>
      
      <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        To message service providers, please provide your contact information. We'll use this to help professionals respond to your inquiries.
      </p>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="name" className="text-sm font-medium">
            Full Name <span className="text-red-500">*</span>
          </Label>
          <Input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter your full name"
            className="mt-1"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="email" className="text-sm font-medium">
            Email <span className="text-red-500">*</span>
          </Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email address"
            className="mt-1"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="phone" className="text-sm font-medium">
            Phone Number
          </Label>
          <Input
            id="phone"
            type="tel"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            placeholder="Enter your phone number"
            className="mt-1"
          />
        </div>
        
        <div>
          <Label htmlFor="zipCode" className="text-sm font-medium">
            ZIP Code
          </Label>
          <Input
            id="zipCode"
            type="text"
            value={zipCode}
            onChange={(e) => setZipCode(e.target.value)}
            placeholder="Enter your ZIP code"
            className="mt-1"
          />
        </div>
        
        <div className="flex gap-3 pt-2">
          <Button 
            type="submit" 
            className="flex-1"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Processing..." : "Continue to Messages"}
          </Button>
          
          {onCancel && (
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              className="flex-1"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};
