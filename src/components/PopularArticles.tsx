import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { BlogPost, fetchPopularPosts } from '@/services/wordpressApi';
import { Card, CardContent } from "@/components/ui/card";
import { Loader2 } from 'lucide-react';

interface PopularArticlesProps {
  excludePostId?: string;
  count?: number;
  className?: string;
  layout?: 'sidebar' | 'bottom';
}

export const PopularArticles: React.FC<PopularArticlesProps> = ({ 
  excludePostId, 
  count = 4, 
  className = '',
  layout = 'sidebar'
}) => {
  const [popularPosts, setPopularPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPopularPosts = async () => {
      try {
        setLoading(true);
        setError(null);
        const posts = await fetchPopularPosts(count, excludePostId);
        setPopularPosts(posts);
      } catch (err) {
        console.error('Error loading popular posts:', err);
        setError('Failed to load popular articles');
      } finally {
        setLoading(false);
      }
    };

    loadPopularPosts();
  }, [count, excludePostId]);

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-5 flex justify-center items-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-5">
          <p className="text-red-500 text-center">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (popularPosts.length === 0) {
    return null;
  }

  if (layout === 'bottom') {
    return (
      <div className={`mt-12 ${className}`}>
        <h3 className="text-2xl font-bold mb-6">Popular Articles</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {popularPosts.map((post) => (
            <Link to={`/blog/${post.slug}`} key={post.id} className="group">
              <div className="rounded-lg overflow-hidden border border-gray-200 dark:border-gray-800 hover:shadow-md transition-shadow">
                <div className="aspect-video overflow-hidden">
                  <img 
                    src={post.image} 
                    alt={post.title}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                </div>
                <div className="p-4">
                  <h4 className="font-semibold text-base line-clamp-2 group-hover:text-primary transition-colors">
                    {post.title}
                  </h4>
                  <div className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                    {post.category}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-0 overflow-hidden">
        <div className="bg-primary/10 py-3 px-4 border-b border-gray-100 dark:border-gray-800">
          <h3 className="text-lg font-bold text-gray-900 dark:text-white">Popular Articles</h3>
        </div>
        <div className="divide-y divide-gray-100 dark:divide-gray-800">
          {popularPosts.map((post) => (
            <Link to={`/blog/${post.slug}`} key={post.id} className="block group hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
              <div className="p-4 flex gap-3 items-center">
                <div className="w-20 h-16 rounded-md overflow-hidden flex-shrink-0 border border-gray-100 dark:border-gray-700">
                  <img 
                    src={post.image} 
                    alt={post.title}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-sm line-clamp-2 group-hover:text-primary transition-colors">
                    {post.title}
                  </h4>
                  <div className="text-xs text-gray-600 dark:text-gray-400 mt-1.5">
                    {post.category}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
        <div className="p-3 bg-gray-50 dark:bg-gray-800/50 text-center">
          <Link to="/blog" className="text-primary hover:underline text-sm font-medium">
            See all articles
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};