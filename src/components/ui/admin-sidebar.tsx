
"use client";

import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Link, useLocation } from "react-router-dom";
import {
  Blocks,
  ChevronsUpDown,
  FileClock,
  GraduationCap,
  Layout,
  LayoutDashboard,
  LogOut,
  MessageSquare,
  MessagesSquare,
  Plus,
  Settings,
  UserCircle,
  UserCog,
  UserSearch,
  Users,
  Briefcase,
  Calendar,
  CreditCard,
  Star,
  Gift,
  Share2,
  Mail,
  Building,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/features/auth/hooks/useAuth";

const sidebarVariants = {
  open: {
    width: "15rem",
  },
  closed: {
    width: "3.05rem",
  },
};

const contentVariants = {
  open: { display: "block", opacity: 1 },
  closed: { display: "block", opacity: 1 },
};

const variants = {
  open: {
    x: 0,
    opacity: 1,
    transition: {
      x: { stiffness: 1000, velocity: -100 },
    },
  },
  closed: {
    x: -20,
    opacity: 0,
    transition: {
      x: { stiffness: 100 },
    },
  },
};

const transitionProps = {
  type: "tween",
  ease: "easeOut",
  duration: 0.2,
  staggerChildren: 0.1,
};

const staggerVariants = {
  open: {
    transition: { staggerChildren: 0.03, delayChildren: 0.02 },
  },
};

export function AdminSidebar() {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const location = useLocation();
  const path = location.pathname;
  const navigate = useNavigate();
  const { toast } = useToast();
  const { logout } = useAuth();

  const handleLogout = () => {
    logout(); // This will handle token removal and navigation

    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account.",
    });
  };

  return (
    <motion.div
      className={cn(
        "sidebar fixed left-0 z-40 h-full shrink-0 border-r"
      )}
      initial={isCollapsed ? "closed" : "open"}
      animate={isCollapsed ? "closed" : "open"}
      variants={sidebarVariants}
      transition={transitionProps}
      onMouseEnter={() => setIsCollapsed(false)}
      onMouseLeave={() => setIsCollapsed(true)}
    >
      <motion.div
        className={`relative z-40 flex text-muted-foreground h-full shrink-0 flex-col bg-white dark:bg-gray-900 transition-all`}
        variants={contentVariants}
      >
        <motion.ul variants={staggerVariants} className="flex h-full flex-col">
          <div className="flex grow flex-col items-center">
            <div className="flex h-[64px] w-full shrink-0 border-b p-2 bg-gradient-to-br from-blue-50 to-white dark:from-gray-800 dark:to-gray-900">
              <div className="mt-1 flex w-full items-center">
                <DropdownMenu>
                  <DropdownMenuTrigger className="w-full" asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="flex w-fit items-center gap-2 px-2" 
                    >
                      <Avatar className='size-9 border-2 border-white shadow-sm'>
                        <AvatarImage src="/placeholder.svg" alt="Admin" />
                        <AvatarFallback>AD</AvatarFallback>
                      </Avatar>
                      <motion.li
                        variants={variants}
                        className="flex w-fit items-center gap-2"
                      >
                        {!isCollapsed && (
                          <div className="flex flex-col items-start">
                            <p className="text-sm font-medium">
                              JobOn Admin
                            </p>
                            <span className="text-xs text-muted-foreground">System Admin</span>
                          </div>
                        )}
                      </motion.li>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start">
                    <DropdownMenuItem
                      asChild
                      className="flex items-center gap-2"
                    >
                      <Link to="/admin/profile">
                        <UserCircle className="h-4 w-4" /> View Profile
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      asChild
                      className="flex items-center gap-2"
                    >
                      <Link to="/admin/settings">
                        <Settings className="h-4 w-4" /> Account Settings
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout} className="flex items-center gap-2">
                      <LogOut className="h-4 w-4" /> Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            <div className="flex h-full w-full flex-col">
              <div className="flex grow flex-col gap-4">
                <ScrollArea className="h-16 grow p-2">
                  <div className={cn("flex w-full flex-col gap-1")}>
                    <Link
                      to="/admin"
                      className={cn(
                        "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                        path === "/admin" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                      )}
                    >
                      <LayoutDashboard className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <p className="ml-3 text-sm font-medium">Dashboard</p>
                        )}
                      </motion.li>
                    </Link>

                    <Link
                        to="/admin/bookings"
                        className={cn(
                            "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                            path === "/admin/bookings" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                        )}
                    >
                      <Calendar className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                            <p className="ml-3 text-sm font-medium">Manage Job Booking</p>
                        )}
                      </motion.li>
                    </Link>

                    <Link
                        to="/admin/business"
                        className={cn(
                            "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                            path === "/admin/business" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                        )}
                    >
                      <Building className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                            <p className="ml-3 text-sm font-medium">Manage Business</p>
                        )}
                      </motion.li>
                    </Link>

                    <Link
                      to="/admin/providers"
                      className={cn(
                        "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                        path === "/admin/providers" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                      )}
                    >
                      <Users className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <p className="ml-3 text-sm font-medium">Manage Providers</p>
                        )}
                      </motion.li>
                    </Link>

                    <Link
                      to="/admin/provider-invitations"
                      className={cn(
                        "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                        path === "/admin/provider-invitations" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                      )}
                    >
                      <Mail className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <p className="ml-3 text-sm font-medium">Provider Invitations</p>
                        )}
                      </motion.li>
                    </Link>

                    <Link
                      to="/admin/customers"
                      className={cn(
                        "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                        path === "/admin/customers" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                      )}
                    >
                      <UserCircle className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <p className="ml-3 text-sm font-medium">Manage Customers</p>
                        )}
                      </motion.li>
                    </Link>

                    <Separator className="my-2" />

                    <Link
                      to="/admin/jobs"
                      className={cn(
                        "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                        path === "/admin/jobs" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                      )}
                    >
                      <Briefcase className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <p className="ml-3 text-sm font-medium">Manage Jobs</p>
                        )}
                      </motion.li>
                    </Link>

                    <Link
                      to="/admin/payments"
                      className={cn(
                        "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                        path === "/admin/payments" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                      )}
                    >
                      <CreditCard className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <p className="ml-3 text-sm font-medium">Manage Payments</p>
                        )}
                      </motion.li>
                    </Link>

                    <Separator className="my-2" />

                    <Link
                      to="/admin/messages"
                      className={cn(
                        "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                        path === "/admin/messages" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                        path === "/admin/messages" && "relative",
                      )}
                    >
                      <MessageSquare className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <p className="ml-3 text-sm font-medium">Messages</p>
                        )}
                      </motion.li>
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <Badge 
                            variant="destructive" 
                            className="ml-2 px-1.5 py-0.5 text-xs absolute right-2"
                          >
                            3
                          </Badge>
                        )}
                      </motion.li>
                      {isCollapsed && (
                        <Badge 
                          variant="destructive" 
                          className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs"
                        >
                          3
                        </Badge>
                      )}
                    </Link>

                    <Link
                      to="/admin/reviews"
                      className={cn(
                        "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                        path === "/admin/reviews" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                      )}
                    >
                      <Star className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <p className="ml-3 text-sm font-medium">Manage Reviews</p>
                        )}
                      </motion.li>
                    </Link>

                    <Link
                      to="/admin/rewards"
                      className={cn(
                        "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                        path === "/admin/rewards" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                      )}
                    >
                      <Gift className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <p className="ml-3 text-sm font-medium">Manage Rewards</p>
                        )}
                      </motion.li>
                    </Link>

                    <Link
                      to="/admin/referrals"
                      className={cn(
                        "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                        path === "/admin/referrals" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                      )}
                    >
                      <Share2 className="h-5 w-5" />
                      <motion.li variants={variants}>
                        {!isCollapsed && (
                          <p className="ml-3 text-sm font-medium">Manage Referrals</p>
                        )}
                      </motion.li>
                    </Link>
                  </div>
                </ScrollArea>
              </div>
              <div className="p-2 border-t bg-gradient-to-br from-white to-blue-50 dark:from-gray-900 dark:to-gray-800">
                <Link
                  to="/admin/settings"
                  className={cn(
                    "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 mb-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                    path === "/admin/settings" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                  )}
                >
                  <Settings className="h-5 w-5 shrink-0" />
                  <motion.li variants={variants}>
                    {!isCollapsed && (
                      <p className="ml-3 text-sm font-medium">Settings</p>
                    )}
                  </motion.li>
                </Link>
                <Button 
                  variant="outline" 
                  className="w-full py-2 text-sm gap-3 font-medium rounded-md shadow-sm hover:shadow flex items-center justify-center" 
                  onClick={handleLogout}
                >
                  <LogOut className="h-4 w-4" />
                  {!isCollapsed && <span>Sign Out</span>}
                </Button>
              </div>
            </div>
          </div>
        </motion.ul>
      </motion.div>
    </motion.div>
  );
}
