
"use client";

import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import {
  Blocks,
  ChevronsUpDown,
  FileClock,
  GraduationCap,
  Layout,
  LayoutDashboard,
  LogOut,
  MessageSquareText,
  MessagesSquare,
  Plus,
  Settings,
  UserCircle,
  UserCog,
  UserSearch,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Link } from "react-router-dom";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useLocation, useNavigate } from 'react-router-dom';
import { useToast } from "@/hooks/use-toast";
import { ProviderVerifiedBadge } from '@/components/provider/ProviderVerifiedBadge';
import { useAuth } from "@/features/auth/hooks/useAuth";

const sidebarVariants = {
  open: {
    width: "15rem",
  },
  closed: {
    width: "4.10rem",
  },
};

const contentVariants = {
  open: { display: "block", opacity: 1 },
  closed: { display: "block", opacity: 1 },
};

const variants = {
  open: {
    x: 0,
    opacity: 1,
    transition: {
      x: { stiffness: 1000, velocity: -100 },
    },
  },
  closed: {
    x: -20,
    opacity: 0,
    transition: {
      x: { stiffness: 100 },
    },
  },
};

const transitionProps = {
  type: "tween",
  ease: "easeOut",
  duration: 0.2,
  staggerChildren: 0.1,
};

const staggerVariants = {
  open: {
    transition: { staggerChildren: 0.03, delayChildren: 0.02 },
  },
};

// Define sidebar navigation items
const providerMenuItems = [
  { 
    name: 'Dashboard', 
    path: '/provider/dashboard', 
    icon: LayoutDashboard
  },
  { 
    name: 'Jobs', 
    path: '/provider/jobs', 
    icon: Layout 
  },
  { 
    name: 'Messages', 
    path: '/provider/messages', 
    icon: MessagesSquare,
    isBeta: true
  },
  { 
    name: 'Calendar', 
    path: '/provider/calendar', 
    icon: FileClock
  },
  { 
    name: 'Earnings', 
    path: '/provider/earnings', 
    icon: Layout
  },
];

const additionalItems = [
  { 
    name: 'Reviews', 
    path: '/provider/reviews', 
    icon: MessageSquareText
  },
  {
    name: 'Performance', 
    path: '/provider/performance', 
    icon: FileClock
  },
  {
    name: 'Badges & Rewards', 
    path: '/provider/badges', 
    icon: GraduationCap
  },
  {
    name: 'Referrals', 
    path: '/provider/referrals', 
    icon: UserSearch
  },
];

const settingsItems = [
  { 
    name: 'Profile', 
    path: '/provider/profile', 
    icon: UserCircle
  },
  { 
    name: 'Settings', 
    path: '/provider/settings', 
    icon: Settings
  },
  {
    name: 'Plans & Pricing', 
    path: '/provider/plans', 
    icon: Blocks
  },
];

export function SessionNavBar() {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const pathname = location.pathname;
  const { logout, user } = useAuth();

  const handleLogout = () => {
    logout(); // This will handle token removal and navigation

    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account.",
    });
  };

  return (
    <motion.div
      className={cn(
        "sidebar fixed left-0 z-40 h-full shrink-0 border-r bg-white dark:bg-gray-900"
      )}
      initial={isCollapsed ? "closed" : "open"}
      animate={isCollapsed ? "closed" : "open"}
      variants={sidebarVariants}
      transition={transitionProps}
      onMouseEnter={() => setIsCollapsed(false)}
      onMouseLeave={() => setIsCollapsed(true)}
    >
      <motion.div
        className={`relative z-40 flex text-muted-foreground h-full shrink-0 flex-col bg-white dark:bg-gray-900 transition-all`}
        variants={contentVariants}
      >
        <motion.ul variants={staggerVariants} className="flex h-full flex-col">
          <div className="flex grow flex-col items-center">
            <div className="flex h-[64px] w-full shrink-0 border-b p-2 bg-gradient-to-br from-blue-50 to-white dark:from-gray-800 dark:to-gray-900">
              <div className="mt-1 flex w-full items-center">
                <DropdownMenu>
                  <DropdownMenuTrigger className="w-full" asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="flex w-fit items-center gap-2 px-2" 
                    >
                      <Avatar className='size-9 border-2 border-white shadow-sm'>
                        <AvatarImage src="/images/avatar-default.svg" alt="Provider" />
                        <AvatarFallback>JP</AvatarFallback>
                      </Avatar>
                      <motion.li
                        variants={variants}
                        className="flex w-fit items-center gap-2"
                      >
                        {!isCollapsed && (
                          <div className="flex flex-col items-start">
                            <div className="flex items-center gap-1">
                              <p className="text-sm font-medium">
                                {user?.name}
                              </p>
                              <ProviderVerifiedBadge plan="pro" size="sm" />
                            </div>
                            <span className="text-xs text-muted-foreground capitalize">{user?.role?.name}</span>
                          </div>
                        )}
                      </motion.li>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start">
                    <DropdownMenuItem
                      asChild
                      className="flex items-center gap-2"
                    >
                      <Link to="/provider/profile">
                        <UserCircle className="h-4 w-4" /> View Profile
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      asChild
                      className="flex items-center gap-2"
                    >
                      <Link to="/provider/settings">
                        <Settings className="h-4 w-4" /> Account Settings
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout} className="flex items-center gap-2">
                      <LogOut className="h-4 w-4" /> Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            <div className="flex h-full w-full flex-col">
              <div className="flex grow flex-col gap-4">
                <ScrollArea className="h-16 grow p-2">
                  <div className={cn("flex w-full flex-col gap-1")}>
                    {/* Primary Menu Items */}
                    {providerMenuItems.map((item) => (
                      <Link
                        key={item.path}
                        to={item.path}
                        className={cn(
                          "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                          pathname === item.path && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                        )}
                      >
                        <item.icon className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && (
                            <div className="ml-2 flex items-center gap-2">
                              <p className="text-sm font-medium">{item.name}</p>
                              {item.isBeta && (
                                <Badge
                                  className={cn(
                                    "flex h-fit w-fit items-center gap-1.5 rounded border-none bg-blue-50 px-1.5 text-blue-600 dark:bg-blue-700 dark:text-blue-300",
                                  )}
                                  variant="outline"
                                >
                                  BETA
                                </Badge>
                              )}
                            </div>
                          )}
                        </motion.li>
                      </Link>
                    ))}

                    <Separator className="my-2" />

                    {/* Additional Items */}
                    {additionalItems.map((item) => (
                      <Link
                        key={item.path}
                        to={item.path}
                        className={cn(
                          "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                          pathname === item.path && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                        )}
                      >
                        <item.icon className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && (
                            <p className="ml-2 text-sm font-medium">{item.name}</p>
                          )}
                        </motion.li>
                      </Link>
                    ))}

                    <Separator className="my-2" />

                    {/* Settings Items */}
                    {settingsItems.map((item) => (
                      <Link
                        key={item.path}
                        to={item.path}
                        className={cn(
                          "flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300",
                          pathname === item.path && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium",
                        )}
                      >
                        <item.icon className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && (
                            <p className="ml-2 text-sm font-medium">{item.name}</p>
                          )}
                        </motion.li>
                      </Link>
                    ))}
                  </div>
                </ScrollArea>
              </div>
              <div className="p-2 border-t bg-gradient-to-br from-white to-blue-50 dark:from-gray-900 dark:to-gray-800">
                <Button 
                  variant="outline" 
                  className="w-full py-2 text-sm gap-3 font-medium rounded-md shadow-sm hover:shadow flex items-center justify-center" 
                  onClick={handleLogout}
                >
                  <LogOut className="h-4 w-4" />
                  {!isCollapsed && <span>Sign Out</span>}
                </Button>
              </div>
            </div>
          </div>
        </motion.ul>
      </motion.div>
    </motion.div>
  );
}
