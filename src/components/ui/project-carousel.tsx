
import { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from './button';
import { cn } from '@/lib/utils';

interface ProjectCarouselProps {
  images: string;
  className?: string;
}

export function ProjectCarousel({ images, className }: ProjectCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  if (!images?.length) return null;

  const handlePrevious = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex(current => (current > 0 ? current - 1 : current));
  };

  const handleNext = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex(current => (current < images.length - 1 ? current + 1 : current));
  };

  return (
    <div 
      className={cn(
        "relative md:w-[280px] h-[180px] rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 w-full mt-10",
        className
      )}
    >
      <img
        src={images}
        alt={`Project image ${currentIndex + 1}`}
        className="w-full h-full object-cover"
        loading="lazy"
      />
      
      {images.length > 1 && (
        <>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "absolute left-2 top-1/2 -translate-y-1/2",
              "bg-black/50 hover:bg-black/70 text-white h-8 w-8",
              "opacity-0 group-hover:opacity-100 transition-opacity",
              currentIndex === 0 && "hidden"
            )}
            onClick={handlePrevious}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "absolute right-2 top-1/2 -translate-y-1/2",
              "bg-black/50 hover:bg-black/70 text-white h-8 w-8",
              "opacity-0 group-hover:opacity-100 transition-opacity",
              currentIndex === images.length - 1 && "hidden"
            )}
            onClick={handleNext}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </>
      )}
    </div>
  );
}
