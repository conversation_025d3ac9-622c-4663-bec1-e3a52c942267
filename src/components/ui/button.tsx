
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { useNavigate } from "react-router-dom"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-95",
  {
    variants: {
      variant: {
        default: "bg-[#2563EB] text-white active:bg-[#2563EB]/80 md:hover:bg-[#2563EB]/90",
        destructive:
          "bg-destructive text-destructive-foreground active:bg-destructive/80 md:hover:bg-destructive/90 dark:bg-destructive dark:text-destructive-foreground dark:active:bg-destructive/70 dark:md:hover:bg-destructive/80",
        outline:
          "border-2 border-blue-500 bg-background active:bg-accent active:text-accent-foreground md:hover:bg-accent md:hover:text-accent-foreground dark:border-blue-600 dark:bg-transparent dark:text-gray-200 dark:active:bg-accent dark:active:text-accent-foreground dark:md:hover:bg-accent dark:md:hover:text-accent-foreground",
        secondary:
          "bg-[#75d1bb] text-white active:bg-[#67bba5] md:hover:bg-[#67bba5] dark:bg-[#75d1bb] dark:text-white dark:active:bg-[#67bba5] dark:md:hover:bg-[#67bba5]",
        ghost: "active:bg-accent active:text-accent-foreground md:hover:bg-accent md:hover:text-accent-foreground dark:active:bg-accent dark:active:text-accent-foreground dark:md:hover:bg-accent dark:md:hover:text-accent-foreground",
        link: "text-primary underline-offset-4 active:underline md:hover:underline dark:text-primary",
        success: "bg-green-600 text-white active:bg-green-700 md:hover:bg-green-700 dark:bg-green-600 dark:text-white dark:active:bg-green-700 dark:md:hover:bg-green-700",
        warning: "bg-warning text-warning-foreground active:bg-warning/80 md:hover:bg-warning/90 dark:bg-warning dark:text-warning-foreground dark:active:bg-warning/70 dark:md:hover:bg-warning/80",
        appStore: "bg-black text-white active:bg-gray-800 md:hover:bg-gray-800 shadow-md transition-all duration-300 dark:bg-black dark:text-white dark:active:bg-gray-900 dark:md:hover:bg-gray-900",
        googlePlay: "bg-primary text-white active:bg-primary-hover md:hover:bg-primary-hover shadow-md transition-all duration-300 dark:bg-primary dark:text-white dark:active:bg-primary-hover dark:md:hover:bg-primary-hover",
        tool: "bg-green-600 text-white active:bg-green-700 md:hover:bg-green-700 dark:bg-green-600 dark:active:bg-green-700 dark:md:hover:bg-green-700 shadow-sm",
        pricing: "bg-green-600 text-white active:bg-green-700 md:hover:bg-green-700 dark:bg-green-600 dark:active:bg-green-700 dark:md:hover:bg-green-700 shadow-md transition-all duration-300",
      },
      size: {
        default: "h-12 px-4 py-2 min-h-[48px]",
        sm: "h-10 rounded-md px-3 min-h-[40px]",
        lg: "h-14 rounded-md px-8 min-h-[56px]",
        xl: "h-14 rounded-md px-8 md:h-16 md:px-10 min-h-[56px] md:min-h-[64px]",
        icon: "h-12 w-12 min-h-[48px] min-w-[48px]",
        appDownload: "h-16 rounded-lg px-4 min-h-[64px]",
        mobile: "h-14 px-4 py-3 text-base w-full min-h-[56px]",
      },
      serviceType: {
        home: "bg-green-500 active:bg-green-600 md:hover:bg-green-600 text-white dark:bg-green-600 dark:active:bg-green-700 dark:md:hover:bg-green-700",
        office: "bg-blue-500 active:bg-blue-600 md:hover:bg-blue-600 text-white dark:bg-blue-600 dark:active:bg-blue-700 dark:md:hover:bg-blue-700",
        commercial: "bg-purple-500 active:bg-purple-600 md:hover:bg-purple-600 text-white dark:bg-purple-600 dark:active:bg-purple-700 dark:md:hover:bg-purple-700",
        none: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      serviceType: "none",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  scrollToTop?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, serviceType, asChild = false, scrollToTop = true, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (props.onClick) {
        props.onClick(e);
      }
    };
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, serviceType, className }))}
        ref={ref}
        {...props}
        onClick={handleClick}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
