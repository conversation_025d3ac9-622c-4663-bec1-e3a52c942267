
import React, { useState } from 'react';
import { <PERSON>, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, Sheet<PERSON><PERSON>le, SheetTrigger } from '@/components/ui/sheet';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { NotificationItem, NotificationItem as NotificationItemType } from './NotificationItem';
import { useToast } from '@/hooks/use-toast';

// Mock notifications data - this would come from an API in a real application
const mockNotifications: NotificationItemType[] = [
  {
    id: '1',
    type: 'lead',
    title: 'New Lead Available',
    message: 'Bathroom Leak Repair – San Diego – $120 budget',
    timestamp: '2 minutes ago',
    read: false,
    actionLink: '/provider/leads',
    actionText: 'Bid Now',
  },
  {
    id: '2',
    type: 'bid',
    title: 'Bid Accepted!',
    message: 'Your bid for Kitchen Sink Replacement was accepted by the customer.',
    timestamp: '1 hour ago',
    read: false,
    actionLink: '/provider/jobs/active',
    actionText: 'View Job',
  },
  {
    id: '3',
    type: 'appointment',
    title: 'Upcoming Appointment',
    message: 'You have an appointment scheduled tomorrow at 10:00 AM with <PERSON>.',
    timestamp: '5 hours ago',
    read: true,
    actionLink: '/provider/calendar',
    actionText: 'View Schedule',
  },
  {
    id: '4',
    type: 'payout',
    title: 'Payout Processed',
    message: 'A payout of $450.00 has been processed to your account.',
    timestamp: '2 days ago',
    read: true,
    actionLink: '/provider/earnings',
    actionText: 'View Earnings',
  },
  {
    id: '5',
    type: 'badge',
    title: 'Badge Earned: Fast Responder',
    message: 'Congratulations! You\'ve earned the Fast Responder badge for responding to leads within 1 hour.',
    timestamp: '3 days ago',
    read: true,
    actionLink: '/provider/performance',
    actionText: 'View Badges',
  },
];

export const NotificationsCenter = () => {
  const [notifications, setNotifications] = useState<NotificationItemType[]>(mockNotifications);
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  const unreadCount = notifications.filter(n => !n.read).length;
  
  const handleMarkAsRead = (id: string) => {
    setNotifications(prevNotifications => 
      prevNotifications.map(notification => 
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const handleMarkAllAsRead = () => {
    setNotifications(prevNotifications => 
      prevNotifications.map(notification => ({ ...notification, read: true }))
    );
    toast({
      title: "Notifications",
      description: "All notifications marked as read",
    });
  };

  const getFilteredNotifications = (read: boolean | null = null) => {
    if (read === null) return notifications;
    return notifications.filter(notification => notification.read === read);
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-destructive text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {unreadCount}
            </span>
          )}
          <span className="sr-only">Open notifications</span>
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-md p-0">
        <SheetHeader className="px-4 py-3 border-b">
          <div className="flex justify-between items-center">
            <SheetTitle>Notifications</SheetTitle>
            {unreadCount > 0 && (
              <Button variant="ghost" size="sm" onClick={handleMarkAllAsRead}>
                Mark all as read
              </Button>
            )}
          </div>
        </SheetHeader>
        
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="w-full grid grid-cols-3 px-4 py-2">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="unread" className="relative">
              Unread
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {unreadCount}
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="read">Read</TabsTrigger>
          </TabsList>
          
          <div className="overflow-y-auto max-h-[500px]">
            <TabsContent value="all" className="m-0">
              {getFilteredNotifications().length > 0 ? (
                getFilteredNotifications().map(notification => (
                  <NotificationItem 
                    key={notification.id} 
                    notification={notification}
                    onMarkAsRead={handleMarkAsRead}
                  />
                ))
              ) : (
                <div className="p-8 text-center text-muted-foreground">
                  <p>No notifications yet</p>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="unread" className="m-0">
              {getFilteredNotifications(false).length > 0 ? (
                getFilteredNotifications(false).map(notification => (
                  <NotificationItem 
                    key={notification.id} 
                    notification={notification}
                    onMarkAsRead={handleMarkAsRead}
                  />
                ))
              ) : (
                <div className="p-8 text-center text-muted-foreground">
                  <p>No unread notifications</p>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="read" className="m-0">
              {getFilteredNotifications(true).length > 0 ? (
                getFilteredNotifications(true).map(notification => (
                  <NotificationItem 
                    key={notification.id} 
                    notification={notification}
                    onMarkAsRead={handleMarkAsRead}
                  />
                ))
              ) : (
                <div className="p-8 text-center text-muted-foreground">
                  <p>No read notifications</p>
                </div>
              )}
            </TabsContent>
          </div>
        </Tabs>
      </SheetContent>
    </Sheet>
  );
};
