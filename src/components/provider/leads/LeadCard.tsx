
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Clock, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';

// Lead type definition
interface Lead {
  id: string;
  title: string;
  description: string;
  location: {
    city: string;
    state: string;
  };
  postedAt: string;
  budget?: number;
  category: string;
  urgent?: boolean;
}

export const LeadCard = ({ lead }: { lead: Lead }) => {
  const isMobile = useIsMobile();
  
  // Generate a unique, subtle background color for each card based on category
  const getCardColor = (category: string) => {
    const colors = {
      "Plumbing": "bg-blue-50 dark:bg-blue-900/10",
      "Electrical": "bg-amber-50 dark:bg-amber-900/10",
      "Home Improvement": "bg-green-50 dark:bg-green-900/10",
      "Cleaning": "bg-purple-50 dark:bg-purple-900/10",
    };
    return colors[category as keyof typeof colors] || "bg-gray-50 dark:bg-gray-800/30";
  };

  return (
    <Card className={`overflow-hidden hover:shadow-md transition-shadow border-l-4 ${lead.urgent ? 'border-l-red-500' : `border-l-primary/50`} ${getCardColor(lead.category)}`}>
      <CardContent className="p-0">
        <div className="p-6">
          <div className="flex justify-between items-start mb-3">
            <h3 className="text-lg font-semibold">{lead.title}</h3>
            {!isMobile && lead.urgent && (
              <Badge variant="destructive" className="ml-2">Urgent</Badge>
            )}
          </div>
          
          <div className="flex flex-wrap items-center text-sm text-gray-500 mb-3 gap-x-4">
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              <span>{lead.location.city}, {lead.location.state}</span>
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              <span>{lead.postedAt}</span>
            </div>
          </div>
          
          <p className="text-gray-600 line-clamp-2 mb-4">{lead.description}</p>
          
          <div className="flex justify-between items-center">
            <div>
              {!isMobile && (
                <>
                  <Badge variant="outline">{lead.category}</Badge>
                  {lead.budget && (
                    <span className="ml-2 text-sm font-medium">${lead.budget} budget</span>
                  )}
                </>
              )}
              {isMobile && lead.urgent && (
                <Badge variant="destructive" className="mr-2">Urgent</Badge>
              )}
            </div>
            <Button asChild size="sm">
              <Link to={`/provider/leads/${lead.id}`}>
                View Details
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
