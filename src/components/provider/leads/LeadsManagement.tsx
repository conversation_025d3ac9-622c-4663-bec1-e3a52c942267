
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { LeadCard } from './LeadCard';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

// Mock lead data type
interface Lead {
  id: string;
  title: string;
  description: string;
  location: {
    city: string;
    state: string;
  };
  postedAt: string;
  budget?: number;
  category: string;
  urgent?: boolean;
}

export const LeadsManagement = () => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to load leads
    const fetchLeads = async () => {
      setLoading(true);
      
      // Mock delay to simulate network request
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock leads data
      const mockLeads = [
        {
          id: 'lead1',
          title: 'Bathroom Sink Repair',
          description: 'Need to fix a leaking bathroom sink faucet and replace the trap. The sink is also draining slowly.',
          location: {
            city: 'San Francisco',
            state: 'CA'
          },
          postedAt: '2 hours ago',
          budget: 200,
          category: 'Plumbing',
          urgent: true
        },
        {
          id: 'lead2',
          title: 'Kitchen Backsplash Installation',
          description: 'Looking for someone to install a tile backsplash in my kitchen. Materials will be provided.',
          location: {
            city: 'Oakland',
            state: 'CA'
          },
          postedAt: '5 hours ago',
          budget: 450,
          category: 'Home Improvement'
        },
        {
          id: 'lead3',
          title: 'Ceiling Fan Installation',
          description: 'Need to replace an existing ceiling fan with a new one in the master bedroom.',
          location: {
            city: 'Berkeley',
            state: 'CA'
          },
          postedAt: '1 day ago',
          budget: 150,
          category: 'Electrical',
          urgent: false
        },
        {
          id: 'lead4',
          title: 'Weekly House Cleaning',
          description: 'Looking for recurring weekly house cleaning service for a 3-bedroom home.',
          location: {
            city: 'Alameda',
            state: 'CA'
          },
          postedAt: '2 days ago',
          budget: 120,
          category: 'Cleaning'
        }
      ];
      
      setLeads(mockLeads);
      setLoading(false);
    };
    
    fetchLeads();
  }, []);

  // Loading state with skeletons
  if (loading) {
    return (
      <div className="space-y-4">
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">All Leads</TabsTrigger>
            <TabsTrigger value="matched">Best Matched</TabsTrigger>
            <TabsTrigger value="urgent">Urgent</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="mt-4">
            <div className="grid gap-4 md:grid-cols-2">
              {[...Array(4)].map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="p-6">
                      <Skeleton className="h-6 w-3/4 mb-2" />
                      <Skeleton className="h-4 w-1/2 mb-4" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-3/4 mb-4" />
                      <div className="flex justify-between items-center">
                        <Skeleton className="h-4 w-1/3" />
                        <Skeleton className="h-8 w-28" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  // Empty state
  if (leads.length === 0) {
    return (
      <div className="space-y-4">
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">All Leads</TabsTrigger>
            <TabsTrigger value="matched">Best Matched</TabsTrigger>
            <TabsTrigger value="urgent">Urgent</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <h3 className="text-lg font-medium mb-2">No new leads available</h3>
                  <p className="text-muted-foreground mb-4">
                    Check back soon! New leads are added frequently.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">All Leads ({leads.length})</TabsTrigger>
          <TabsTrigger value="matched">Best Matched ({leads.filter(lead => lead.category === 'Plumbing').length})</TabsTrigger>
          <TabsTrigger value="urgent">Urgent ({leads.filter(lead => lead.urgent).length})</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="mt-4">
          <div className="grid gap-4 md:grid-cols-2">
            {leads.map((lead) => (
              <LeadCard key={lead.id} lead={lead} />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="matched" className="mt-4">
          <div className="grid gap-4 md:grid-cols-2">
            {leads.filter(lead => lead.category === 'Plumbing').map((lead) => (
              <LeadCard key={lead.id} lead={lead} />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="urgent" className="mt-4">
          <div className="grid gap-4 md:grid-cols-2">
            {leads.filter(lead => lead.urgent).map((lead) => (
              <LeadCard key={lead.id} lead={lead} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
