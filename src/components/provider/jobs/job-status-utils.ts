
import { Job } from '@/types/jobs';

export type JobStatus = 'Open' | 'Confirmed' | 'Awaiting Materials' | 'In Progress' | 'Completed' | 'Cancelled';

export type JobStatusUpdate = {
  jobId: string;
  newStatus: JobStatus;
  notes?: string;
};

export function getStatusColor(status: string): {bg: string, text: string, border: string} {
  switch(status) {
    case 'Confirmed':
      return {
        bg: 'bg-green-50',
        text: 'text-green-600',
        border: 'border-green-200'
      };
    case 'Awaiting Materials':
      return {
        bg: 'bg-amber-50',
        text: 'text-amber-600',
        border: 'border-amber-200'
      };
    case 'In Progress':
      return {
        bg: 'bg-blue-50',
        text: 'text-blue-600',
        border: 'border-blue-200'
      };
    case 'Completed':
      return {
        bg: 'bg-purple-50',
        text: 'text-purple-600',
        border: 'border-purple-200'
      };
    case 'Cancelled':
      return {
        bg: 'bg-red-50',
        text: 'text-red-600',
        border: 'border-red-200'
      };
    default:
      return {
        bg: 'bg-gray-50',
        text: 'text-gray-600',
        border: 'border-gray-200'
      };
  }
}

export function getStatusGradient(status: string): string {
  switch(status) {
    case 'Confirmed':
      return 'bg-gradient-to-br from-white via-white to-green-50';
    case 'Awaiting Materials':
      return 'bg-gradient-to-br from-white via-white to-amber-50';
    case 'In Progress':
      return 'bg-gradient-to-br from-white via-white to-blue-50';
    case 'Completed':
      return 'bg-gradient-to-br from-white via-white to-purple-50';
    case 'Cancelled':
      return 'bg-gradient-to-br from-white via-white to-red-50';
    default:
      return 'bg-gradient-to-br from-white via-white to-gray-50';
  }
}
