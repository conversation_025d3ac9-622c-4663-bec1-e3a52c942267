import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar } from '@/components/ui/calendar';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { format, isSameDay } from 'date-fns';
import { Calendar as CalendarIcon, Clock, Users, MapPin, Plus, ChevronLeft, ChevronRight, CheckCircle2 } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { getStatusColor } from '@/components/provider/jobs/job-status-utils';
import { AvailabilityDialog } from './AvailabilityDialog';

interface Job {
  id: string;
  title: string;
  customerName: string;
  address: string;
  dateTime: Date;
  status: 'scheduled' | 'inProgress' | 'completed';
}

const statusColors = {
  scheduled: {
    bg: 'bg-blue-50',
    text: 'text-blue-600',
    border: 'border-blue-200',
    icon: <Clock className="h-4 w-4 text-blue-500" />
  },
  inProgress: {
    bg: 'bg-amber-50',
    text: 'text-amber-600',
    border: 'border-amber-200',
    icon: <CheckCircle2 className="h-4 w-4 text-amber-500" />
  },
  completed: {
    bg: 'bg-green-50',
    text: 'text-green-500',
    border: 'border-green-200',
    icon: <CheckCircle2 className="h-4 w-4 text-green-500" />
  }
};

export const CalendarPage = () => {
  const [view, setView] = useState<'month' | 'week'>('month');
  const [date, setDate] = useState<Date>(new Date());
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [jobPreviewOpen, setJobPreviewOpen] = useState(false);
  const [availabilityDialogOpen, setAvailabilityDialogOpen] = useState(false);
  const isMobile = useIsMobile();

  // Sample jobs data
  const jobs: Job[] = [{
    id: '1',
    title: 'Kitchen Sink Repair',
    customerName: 'Sarah Johnson',
    address: '123 Main St, Seattle, WA',
    dateTime: new Date(2025, 3, 22, 10, 0),
    status: 'scheduled'
  }, {
    id: '2',
    title: 'Bathroom Pipe Installation',
    customerName: 'Michael Smith',
    address: '456 Park Ave, Bellevue, WA',
    dateTime: new Date(2025, 3, 24, 14, 30),
    status: 'inProgress'
  }, {
    id: '3',
    title: 'Dishwasher Installation',
    customerName: 'Emma Davis',
    address: '789 Pine St, Redmond, WA',
    dateTime: new Date(2025, 3, 25, 9, 0),
    status: 'completed'
  }];

  // Date has job
  const hasJobOnDate = (date: Date) => {
    return jobs.some(job => isSameDay(job.dateTime, date));
  };

  // Get jobs for selected date
  const getJobsForDate = (selectedDate: Date) => {
    return jobs.filter(job => isSameDay(job.dateTime, selectedDate));
  };

  // Open job preview
  const openJobPreview = (job: Job) => {
    setSelectedJob(job);
    setJobPreviewOpen(true);
  };

  // Helper to navigate between dates
  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(date);
    if (direction === 'prev') {
      newDate.setDate(newDate.getDate() - 1);
    } else {
      newDate.setDate(newDate.getDate() + 1);
    }
    setDate(newDate);
  };
  return <div className="space-y-4">
      {/* Mobile header */}
      {isMobile}

      {/* Desktop header and controls */}
      {!isMobile && <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold">Calendar</h1>
            <p className="text-muted-foreground">Manage your schedule and jobs</p>
          </div>
          <div className="flex items-center gap-2 self-stretch sm:self-auto">
            <Tabs value={view} onValueChange={v => setView(v as 'month' | 'week')}>
              <TabsList>
                <TabsTrigger value="month">Month</TabsTrigger>
                <TabsTrigger value="week">Week</TabsTrigger>
              </TabsList>
            </Tabs>
            <Button onClick={() => setAvailabilityDialogOpen(true)}>Set Availability</Button>
          </div>
        </div>}

      {/* Mobile Date Selection and Controls */}
      {isMobile && <div className="flex items-center justify-between mb-4">
          <Button variant="ghost" size="icon" className="text-blue-600" onClick={() => navigateDate('prev')}>
            <ChevronLeft className="h-5 w-5" />
          </Button>
          
          <div className="text-center">
            <h2 className="text-lg font-semibold">{format(date, 'MMMM d, yyyy')}</h2>
            <p className="text-xs text-muted-foreground">
              {getJobsForDate(date).length} jobs scheduled
            </p>
          </div>
          
          <Button variant="ghost" size="icon" className="text-blue-600" onClick={() => navigateDate('next')}>
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>}

      {/* Mobile view selector */}
      {isMobile && <div className="flex justify-between items-center mb-4">
          <Select value={view} onValueChange={v => setView(v as 'month' | 'week')}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="View" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="week">Week</SelectItem>
            </SelectContent>
          </Select>
          
          <Button 
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
            onClick={() => setAvailabilityDialogOpen(true)}
          >
            Set Availability
          </Button>
        </div>}

      {/* Calendar */}
      <Card className={cn("border-blue-100 shadow-md overflow-hidden", isMobile ? "rounded-xl" : "")}>
        <CardHeader className={cn("pb-3 border-b", isMobile ? "bg-gradient-to-r from-blue-50 to-indigo-50" : "")}>
          <CardTitle>
            {!isMobile && format(date, 'MMMM yyyy')}
            {isMobile && 'Calendar'}
          </CardTitle>
          <CardDescription>
            {jobs.length} jobs scheduled this month
          </CardDescription>
        </CardHeader>
        <CardContent className={cn(isMobile ? "p-2 pt-3" : "p-4", "bg-white")}>
          <Calendar mode="single" selected={date} onSelect={newDate => newDate && setDate(newDate)} className={cn("rounded-md border shadow-sm", isMobile ? "mx-auto" : "")} modifiers={{
          hasJob: date => hasJobOnDate(date)
        }} modifiersClassNames={{
          hasJob: "bg-blue-100 font-bold text-blue-800 relative"
        }} footer={<div className="mt-4 flex items-center justify-between text-sm">
                <div className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-blue-400 mr-1"></div>
                  <span className="text-xs">Has Job</span>
                </div>
                <div className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-blue-600 mr-1"></div>
                  <span className="text-xs">Selected</span>
                </div>
              </div>} />
        </CardContent>
      </Card>

      {/* Jobs for selected date */}
      <Card className={cn("border-blue-100 shadow-md", isMobile ? "rounded-xl mb-20" : "")}>
        <CardHeader className={cn("pb-3 border-b", isMobile ? "bg-gradient-to-r from-blue-50 to-indigo-50" : "")}>
          <CardTitle className="flex items-center">
            <CalendarIcon className="h-5 w-5 mr-2 text-blue-600" />
            Jobs on {format(date, 'MMMM d, yyyy')}
          </CardTitle>
          <CardDescription>
            {getJobsForDate(date).length} jobs scheduled for this day
          </CardDescription>
        </CardHeader>
        <CardContent className={cn("p-0", isMobile ? "max-h-[350px] overflow-auto" : "")}>
          {getJobsForDate(date).length > 0 ? <div className={isMobile ? "divide-y" : "space-y-3 p-4"}>
              {getJobsForDate(date).map(job => {
            const statusStyle = statusColors[job.status];
            return <div key={job.id} className={cn("cursor-pointer transition-colors", isMobile ? `p-4 ${statusStyle.bg} border-l-4 ${statusStyle.border} animate-fade-in` : "p-4 border rounded-lg hover:bg-secondary/10")} onClick={() => openJobPreview(job)}>
                    <div className="flex justify-between items-start mb-2">
                      <h3 className={cn("font-medium", statusStyle.text)}>{job.title}</h3>
                      <Badge className={cn("capitalize", job.status === 'scheduled' ? "bg-blue-100 text-blue-700 hover:bg-blue-200" : job.status === 'inProgress' ? "bg-amber-100 text-amber-700 hover:bg-amber-200" : "bg-green-100 text-green-700 hover:bg-green-200")}>
                        {job.status === 'inProgress' ? 'In Progress' : job.status}
                      </Badge>
                    </div>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-blue-500" />
                        <span className="font-medium">{format(job.dateTime, 'h:mm a')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-indigo-500" />
                        <span>{job.customerName}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-purple-500" />
                        <span className="truncate">{job.address}</span>
                      </div>
                    </div>
                  </div>;
          })}
            </div> : <div className="py-12 text-center">
              <div className="flex flex-col items-center justify-center p-6">
                <div className="h-16 w-16 rounded-full bg-blue-50 flex items-center justify-center mb-4">
                  <CalendarIcon className="h-8 w-8 text-blue-400" />
                </div>
                <h3 className="text-lg font-medium mb-1">No jobs scheduled</h3>
                <p className="text-muted-foreground max-w-xs">
                  Start winning jobs to fill up your schedule for this day!
                </p>
              </div>
            </div>}
        </CardContent>
      </Card>

      {/* Mobile floating action button */}
      {isMobile}

      {/* Job preview dialog */}
      <Dialog open={jobPreviewOpen} onOpenChange={setJobPreviewOpen}>
        {selectedJob && <DialogContent className={cn("sm:max-w-md", isMobile ? "p-0 rounded-t-xl h-[80vh] mt-auto" : "")}>
            <DialogHeader className={cn("p-5", isMobile ? "bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-xl border-b" : "")}>
              <DialogTitle className="mb-1 flex items-center gap-2">
                {statusColors[selectedJob.status].icon}
                {selectedJob.title}
              </DialogTitle>
              <DialogDescription>
                <Badge className={cn("capitalize mt-1", selectedJob.status === 'scheduled' ? "bg-blue-100 text-blue-700 hover:bg-blue-200" : selectedJob.status === 'inProgress' ? "bg-amber-100 text-amber-700 hover:bg-amber-200" : "bg-green-100 text-green-700 hover:bg-green-200")}>
                  {selectedJob.status === 'inProgress' ? 'In Progress' : selectedJob.status}
                </Badge>
              </DialogDescription>
            </DialogHeader>
            <div className={cn("space-y-4", isMobile ? "p-5" : "pt-4")}>
              <div className="grid grid-cols-1 gap-4">
                <div className={cn("rounded-lg p-3", statusColors[selectedJob.status].bg, statusColors[selectedJob.status].border)}>
                  <h3 className="text-sm font-medium mb-2 text-gray-700">Appointment Details</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-blue-500" />
                      <p className="text-sm">{format(selectedJob.dateTime, 'PPP p')}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-indigo-500" />
                      <p className="text-sm">{selectedJob.customerName}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-purple-500" />
                      <p className="text-sm truncate">{selectedJob.address}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-2 pt-2">
                <Button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700">
                  Get Directions
                </Button>
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline">Message Customer</Button>
                  <Button variant="outline">View Details</Button>
                </div>
                {isMobile && selectedJob.status === 'scheduled' && <Button className="w-full mt-2 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700">
                    Mark as In Progress
                  </Button>}
                {isMobile && selectedJob.status === 'inProgress' && <Button className="w-full mt-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700">
                    Mark as Completed
                  </Button>}
              </div>
            </div>
          </DialogContent>}
      </Dialog>

      {/* Availability Dialog */}
      <AvailabilityDialog 
        isOpen={availabilityDialogOpen}
        onClose={() => setAvailabilityDialogOpen(false)}
      />
    </div>;
};
