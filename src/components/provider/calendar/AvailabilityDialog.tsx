
import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Clock } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const DAYS = [
  { id: 'monday', label: 'Monday' },
  { id: 'tuesday', label: 'Tuesday' },
  { id: 'wednesday', label: 'Wednesday' },
  { id: 'thursday', label: 'Thursday' },
  { id: 'friday', label: 'Friday' },
  { id: 'saturday', label: 'Saturday' },
  { id: 'sunday', label: 'Sunday' },
];

const TIME_OPTIONS = Array.from({ length: 24 * 4 }, (_, i) => {
  const hour = Math.floor(i / 4);
  const minute = (i % 4) * 15;
  const period = hour >= 12 ? 'PM' : 'AM';
  const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
  return {
    value: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
    label: `${hour12}:${minute.toString().padStart(2, '0')} ${period}`
  };
});

interface DayAvailability {
  enabled: boolean;
  startTime: string;
  endTime: string;
}

type WeekAvailability = {
  [key: string]: DayAvailability;
};

interface AvailabilityDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AvailabilityDialog: React.FC<AvailabilityDialogProps> = ({
  isOpen,
  onClose
}) => {
  const { toast } = useToast();

  // Default availability for each day
  const defaultAvailability: WeekAvailability = {
    monday: { enabled: true, startTime: '09:00', endTime: '17:00' },
    tuesday: { enabled: true, startTime: '09:00', endTime: '17:00' },
    wednesday: { enabled: true, startTime: '09:00', endTime: '17:00' },
    thursday: { enabled: true, startTime: '09:00', endTime: '17:00' },
    friday: { enabled: true, startTime: '09:00', endTime: '17:00' },
    saturday: { enabled: false, startTime: '10:00', endTime: '15:00' },
    sunday: { enabled: false, startTime: '10:00', endTime: '15:00' },
  };

  // Initialize availability from localStorage or use defaults
  const [availability, setAvailability] = useState<WeekAvailability>(() => {
    const saved = localStorage.getItem('providerAvailability');
    return saved ? JSON.parse(saved) : defaultAvailability;
  });

  // Save to localStorage whenever availability changes
  useEffect(() => {
    localStorage.setItem('providerAvailability', JSON.stringify(availability));
  }, [availability]);

  const handleToggleDay = (dayId: string) => {
    setAvailability(prev => ({
      ...prev,
      [dayId]: {
        ...prev[dayId],
        enabled: !prev[dayId].enabled
      }
    }));
  };

  const handleTimeChange = (dayId: string, type: 'startTime' | 'endTime', value: string) => {
    setAvailability(prev => ({
      ...prev,
      [dayId]: {
        ...prev[dayId],
        [type]: value
      }
    }));
  };

  const handleSave = () => {
    // Save to localStorage (already happening in useEffect)
    
    // Show success toast
    toast({
      title: "Availability saved",
      description: "Your availability settings have been updated.",
    });
    
    // Close dialog
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Set Your Availability</DialogTitle>
          <DialogDescription>
            Configure your working hours for each day of the week.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4 max-h-[60vh] overflow-y-auto">
          {DAYS.map((day) => (
            <div key={day.id} className="mb-6 last:mb-0">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <Switch 
                    checked={availability[day.id].enabled} 
                    onCheckedChange={() => handleToggleDay(day.id)} 
                    id={`${day.id}-toggle`}
                  />
                  <Label 
                    htmlFor={`${day.id}-toggle`}
                    className={`ml-2 font-medium ${!availability[day.id].enabled ? 'text-muted-foreground' : ''}`}
                  >
                    {day.label}
                  </Label>
                </div>
              </div>
              
              <div className={`grid grid-cols-2 gap-3 pl-10 ${!availability[day.id].enabled ? 'opacity-50' : ''}`}>
                <div>
                  <Label className="text-xs mb-1 block text-muted-foreground">Start Time</Label>
                  <Select
                    value={availability[day.id].startTime}
                    onValueChange={(value) => handleTimeChange(day.id, 'startTime', value)}
                    disabled={!availability[day.id].enabled}
                  >
                    <SelectTrigger className="w-full">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                        <SelectValue placeholder="Start time" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      {TIME_OPTIONS.map((time) => (
                        <SelectItem key={time.value} value={time.value}>
                          {time.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label className="text-xs mb-1 block text-muted-foreground">End Time</Label>
                  <Select
                    value={availability[day.id].endTime}
                    onValueChange={(value) => handleTimeChange(day.id, 'endTime', value)}
                    disabled={!availability[day.id].enabled}
                  >
                    <SelectTrigger className="w-full">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                        <SelectValue placeholder="End time" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      {TIME_OPTIONS.map((time) => (
                        <SelectItem key={time.value} value={time.value}>
                          {time.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave}>Save Availability</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
