import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Share2, Copy, Users, Mail, Check } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';

interface ReferralProgramCardProps {
  referralLink: string;
  referralStats: {
    invited: number;
    activated: number;
    rewards: number;
  };
}

const mockReferrals = [
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "Completed First Job",
    joinDate: "2025-03-15",
    reward: "$50"
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "Signed Up",
    joinDate: "2025-04-20",
    reward: "Pending"
  }
];

export const ReferralProgramCard = ({ 
  referralLink, 
  referralStats 
}: ReferralProgramCardProps) => {
  const { toast } = useToast();
  const [showCopied, setShowCopied] = useState(false);
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(referralLink);
    setShowCopied(true);
    toast({
      title: "Success!",
      description: "Referral link copied to clipboard",
    });
    
    setTimeout(() => setShowCopied(false), 2000);
  };
  
  const shareViaEmail = () => {
    const subject = encodeURIComponent("Join me on JobOn - Service Provider Platform");
    const body = encodeURIComponent(
      `Hey there,\n\nI've been using JobOn to find new customers and grow my business. Join using my referral link and we both earn rewards:\n\n${referralLink}\n\nBest,\n`
    );
    window.open(`mailto:?subject=${subject}&body=${body}`);
    
    toast({
      title: "Email client opened",
      description: "Share your referral link with colleagues",
    });
  };
  
  return (
    <Card className="overflow-hidden hover:shadow-md transition-all">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b pb-6">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">Provider Referral Program</CardTitle>
            <CardDescription className="mt-1">
              Invite other service professionals and earn $50 when they complete their first job
            </CardDescription>
          </div>
          <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
            <Share2 className="h-6 w-6 text-blue-600" />
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-6">
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">
            Your personal referral link
          </label>
          <div className="flex">
            <Input 
              value={referralLink} 
              readOnly 
              className="rounded-r-none focus-visible:ring-0"
            />
            <Button 
              onClick={copyToClipboard} 
              variant="secondary"
              className="rounded-l-none"
            >
              {showCopied ? "Copied!" : "Copy"}
              <Copy className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <div className="p-4 bg-slate-50 rounded-lg text-center">
            <div className="text-2xl font-bold mb-1 text-blue-600">
              {referralStats.invited}
            </div>
            <div className="text-sm text-muted-foreground">
              Pros Invited
            </div>
          </div>
          <div className="p-4 bg-slate-50 rounded-lg text-center">
            <div className="text-2xl font-bold mb-1 text-green-600">
              {referralStats.activated}
            </div>
            <div className="text-sm text-muted-foreground">
              Joined JobOn
            </div>
          </div>
          <div className="p-4 bg-slate-50 rounded-lg text-center">
            <div className="text-2xl font-bold mb-1 text-amber-600">
              ${referralStats.rewards}
            </div>
            <div className="text-sm text-muted-foreground">
              Rewards Earned
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4">Your Referrals</h3>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Join Date</TableHead>
                  <TableHead>Reward</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockReferrals.map((referral) => (
                  <TableRow key={referral.email}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{referral.name}</div>
                        <div className="text-sm text-muted-foreground">{referral.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {referral.status === "Completed First Job" && (
                          <Check className="mr-2 h-4 w-4 text-green-500" />
                        )}
                        <span className={referral.status === "Completed First Job" ? "text-green-600" : ""}>
                          {referral.status}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{new Date(referral.joinDate).toLocaleDateString()}</TableCell>
                    <TableCell>{referral.reward}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="flex flex-col sm:flex-row gap-3 p-6 pt-0">
        <Button className="w-full sm:w-auto" onClick={copyToClipboard}>
          <Copy className="mr-2 h-4 w-4" />
          Copy Referral Link
        </Button>
        <Button variant="outline" className="w-full sm:w-auto" onClick={shareViaEmail}>
          <Mail className="mr-2 h-4 w-4" />
          Share via Email
        </Button>
      </CardFooter>
    </Card>
  );
};
