
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PerformanceMetricCard } from '@/components/provider/performance/PerformanceMetricCard';
import { UpgradeBanner } from '@/components/provider/subscription/UpgradeBanner';
import { OnboardingChecklist } from '@/components/provider/onboarding/OnboardingChecklist';
import { Briefcase, Calendar, DollarSign, Star, Users, Bell, BadgeCheck } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { MobileMetricCard } from '@/components/provider/dashboard/MobileMetricCard';

export const DashboardHome = () => {
  const { toast } = useToast();
  const [currentPlan, setCurrentPlan] = useState('free');
  const [showUpgradeBanner, setShowUpgradeBanner] = useState(true);
  const [isNewUser, setIsNewUser] = useState(true);
  const isMobile = useIsMobile();

  useEffect(() => {
    // Clear onboarding checklist hidden state on component mount (simulating new session)
    // In a real app, this would happen on login
    if (!localStorage.getItem('hasVisitedBefore')) {
      localStorage.removeItem('onboardingChecklistHidden');
      localStorage.setItem('hasVisitedBefore', 'true');
    }

    setTimeout(() => {
      const mockData = {
        plan: 'free',
        newLeads: 3,
        activeJobs: 5,
        jobsThisMonth: 8,
        earnings: 2450,
        averageRating: 4.8,
        reviews: 23,
        isNewUser: true
      };
      
      setIsNewUser(mockData.isNewUser);
      
      if (mockData.newLeads > 0) {
        toast({
          title: `${mockData.newLeads} new leads available!`,
          description: "Review and bid on them to grow your business.",
          variant: "default"
        });
      }
    }, 1500);
  }, [toast]);

  return (
    <div className="space-y-6">
      {isNewUser && (
        <OnboardingChecklist />
      )}
      
      {currentPlan === 'free' && showUpgradeBanner && (
        <UpgradeBanner 
          message="Win 2x more jobs with priority access to leads. Upgrade to Pro today!"
          ctaText="Upgrade to Pro"
        />
      )}
      
      {isMobile ? (
        // Mobile-specific dashboard with colorful metric cards
        <div className="grid grid-cols-2 gap-4">
          <MobileMetricCard 
            title="Active Jobs" 
            value="5" 
            icon={<Briefcase className="h-5 w-5" />} 
            color="blue"
          />
          <MobileMetricCard 
            title="Upcoming" 
            value="3" 
            icon={<Calendar className="h-5 w-5" />} 
            color="purple"
          />
          <MobileMetricCard 
            title="This Month" 
            value="$2,450" 
            icon={<DollarSign className="h-5 w-5" />} 
            color="green"
          />
          <MobileMetricCard 
            title="Rating" 
            value="4.8" 
            icon={<Star className="h-5 w-5" />} 
            color="amber"
          />
          <MobileMetricCard 
            title="Response" 
            value="92%" 
            icon={<Bell className="h-5 w-5" />} 
            color="blue"
          />
          <MobileMetricCard 
            title="Status" 
            value="Starter" 
            icon={<BadgeCheck className="h-5 w-5" />} 
            color="slate"
          />
        </div>
      ) : (
        // Desktop metric cards
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
          <PerformanceMetricCard
            title="Active Jobs"
            value="5"
            icon={<Briefcase className="h-5 w-5" />}
            color="blue"
            trend={{ value: 25, label: "vs last month" }}
          />
          
          <PerformanceMetricCard
            title="Upcoming Appointments"
            value="3"
            icon={<Calendar className="h-5 w-5" />}
            color="purple"
            description="Next: Tomorrow, 2:00 PM"
          />
          
          <PerformanceMetricCard
            title="This Month's Earnings"
            value="$2,450"
            icon={<DollarSign className="h-5 w-5" />}
            color="green"
            trend={{ value: 12, label: "vs last month" }}
            progress={{ value: 75, target: 100, label: "Monthly Goal: $3,200" }}
          />
          
          <PerformanceMetricCard
            title="Customer Rating"
            value="4.8"
            icon={<Star className="h-5 w-5" />}
            color="amber"
            description="From 23 reviews"
          />
          
          <PerformanceMetricCard
            title="Response Rate"
            value="92%"
            icon={<Bell className="h-5 w-5" />}
            color="blue"
            progress={{ value: 92, label: "Goal: 95%" }}
          />
          
          <PerformanceMetricCard
            title="Pro Status"
            value={currentPlan === 'free' ? 'Starter' : currentPlan === 'pro' ? 'Pro' : 'Elite'}
            icon={<BadgeCheck className="h-5 w-5" />}
            color={currentPlan === 'free' ? 'slate' : currentPlan === 'pro' ? 'blue' : 'amber'}
            description={currentPlan === 'free' ? 'Unlock more features with Pro' : 'Verified status active'}
          />
        </div>
      )}
      
      {currentPlan === 'free' && !isMobile && (
        <div className="my-6">
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 dark:from-blue-950/20 dark:to-indigo-950/20 dark:border-blue-900/40">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Grow Your Business with Pro</CardTitle>
              <CardDescription>
                Pro members see 35% more leads and have access to advanced business tools
              </CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-2">
              <div className="bg-white/80 dark:bg-gray-800/50 rounded-lg p-4 flex items-center space-x-3 shadow-sm">
                <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center flex-shrink-0">
                  <Bell className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h4 className="font-medium">Priority Alerts</h4>
                  <p className="text-sm text-muted-foreground">Get leads before others</p>
                </div>
              </div>
              <div className="bg-white/80 dark:bg-gray-800/50 rounded-lg p-4 flex items-center space-x-3 shadow-sm">
                <div className="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center flex-shrink-0">
                  <DollarSign className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h4 className="font-medium">Lower Commission</h4>
                  <p className="text-sm text-muted-foreground">15% vs 20% standard</p>
                </div>
              </div>
              <div className="bg-white/80 dark:bg-gray-800/50 rounded-lg p-4 flex items-center space-x-3 shadow-sm">
                <div className="h-10 w-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center flex-shrink-0">
                  <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h4 className="font-medium">Premium Support</h4>
                  <p className="text-sm text-muted-foreground">Business coaching included</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="space-y-5">
        <h2 className="text-xl font-semibold">Recent Activity</h2>
        <div className="border rounded-lg divide-y">
          <div className="p-4 flex items-start">
            <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
              <Briefcase className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="font-medium">New lead available: Kitchen Plumbing Repair</p>
              <p className="text-sm text-muted-foreground">10 minutes ago · San Diego, CA</p>
            </div>
          </div>
          <div className="p-4 flex items-start">
            <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3">
              <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="font-medium">Payment received: $350.00</p>
              <p className="text-sm text-muted-foreground">Yesterday · Bathroom Sink Installation</p>
            </div>
          </div>
          <div className="p-4 flex items-start">
            <div className="h-8 w-8 rounded-full bg-amber-100 dark:bg-amber-900 flex items-center justify-center mr-3">
              <Star className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            </div>
            <div>
              <p className="font-medium">New 5-star review from Michael T.</p>
              <p className="text-sm text-muted-foreground">2 days ago · Water Heater Replacement</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
