
import React from 'react';
import { BadgeCheck } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface VerifiedBadgeProps {
  plan: 'pro' | 'elite' | string;
  size?: 'sm' | 'md' | 'lg';
}

export const ProviderVerifiedBadge: React.FC<VerifiedBadgeProps> = ({ 
  plan,
  size = 'md'
}) => {
  if (!plan || plan === 'free') return null;
  
  const isElite = plan === 'elite';
  
  const sizeStyles = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <BadgeCheck className={cn(
            sizeStyles[size],
            isElite ? 'text-amber-500' : 'text-blue-500'
          )} />
        </TooltipTrigger>
        <TooltipContent>
          <p>{isElite ? 'Elite' : 'Pro'} Verified Provider</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
