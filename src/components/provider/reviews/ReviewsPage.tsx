import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format } from 'date-fns';
import { MessageSquare, Star, StarOff, Calendar, ChevronDown, Filter, PlusCircle, Zap } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { Badge } from '@/components/ui/badge';

interface Review {
  id: string;
  customerName: string;
  rating: number;
  text: string;
  date: Date;
  jobTitle: string;
  replied: boolean;
  replyText?: string;
}

export const ReviewsPage = () => {
  const { toast } = useToast();
  const [sortBy, setSortBy] = useState("newest");
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [replyDialogOpen, setReplyDialogOpen] = useState(false);
  const [replyText, setReplyText] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [expandedCardId, setExpandedCardId] = useState<string | null>(null);
  const isMobile = useIsMobile();

  const reviews: Review[] = [
    {
      id: '1',
      customerName: 'Sarah Johnson',
      rating: 5,
      text: 'John did an excellent job fixing our kitchen sink. He was professional, arrived on time, and completed the work quickly. Highly recommend!',
      date: new Date(2025, 3, 15),
      jobTitle: 'Kitchen Sink Repair',
      replied: false
    },
    {
      id: '2',
      customerName: 'Michael Smith',
      rating: 4,
      text: 'Good work on our bathroom pipe installation. The job was done well, though it took a bit longer than initially quoted. Would use their services again.',
      date: new Date(2025, 3, 10),
      jobTitle: 'Bathroom Pipe Installation',
      replied: true,
      replyText: 'Thank you for your feedback, Michael! We appreciate your understanding regarding the timeline and are glad you were satisfied with the results.'
    },
    {
      id: '3',
      customerName: 'Emma Davis',
      rating: 5,
      text: 'Fantastic service! John installed our new dishwasher and was incredibly knowledgeable and efficient. Everything works perfectly.',
      date: new Date(2025, 3, 5),
      jobTitle: 'Dishwasher Installation',
      replied: false
    },
    {
      id: '4',
      customerName: 'Robert Wilson',
      rating: 3,
      text: 'The work was satisfactory but communication could have been better. I had to follow up multiple times about the schedule.',
      date: new Date(2025, 2, 25),
      jobTitle: 'Faucet Replacement',
      replied: false
    }
  ];

  const handleReplySubmit = () => {
    toast({
      title: "Reply submitted",
      description: "Your response has been posted to the customer review.",
    });
    setReplyDialogOpen(false);
    setReplyText("");
  };

  const getSortedReviews = () => {
    let filteredReviews = [...reviews];
    
    // Filter by tab selection
    if (activeTab === "unreplied") {
      filteredReviews = filteredReviews.filter(review => !review.replied);
    } else if (activeTab === "replied") {
      filteredReviews = filteredReviews.filter(review => review.replied);
    }
    
    // Sort by selected criteria
    if (sortBy === "newest") {
      return filteredReviews.sort((a, b) => b.date.getTime() - a.date.getTime());
    } else if (sortBy === "highest") {
      return filteredReviews.sort((a, b) => b.rating - a.rating);
    } else {
      return filteredReviews.sort((a, b) => a.rating - b.rating);
    }
  };

  const toggleExpandCard = (id: string) => {
    setExpandedCardId(expandedCardId === id ? null : id);
  };

  const getReviewRating = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
            }`}
          />
        ))}
      </div>
    );
  };

  const calculateAverageRating = () => {
    if (reviews.length === 0) return "0.0";
    const sum = reviews.reduce((total, review) => total + review.rating, 0);
    return (sum / reviews.length).toFixed(1);
  };

  const getTotalRatings = (stars: number) => {
    return reviews.filter(review => review.rating === stars).length;
  };
  
  const getPercentageForRating = (stars: number) => {
    if (reviews.length === 0) return 0;
    return Math.round((getTotalRatings(stars) / reviews.length) * 100);
  };

  const renderMobileReviewCard = (review: Review) => {
    const isExpanded = expandedCardId === review.id;
    
    return (
      <Card 
        key={review.id} 
        className={`mb-4 overflow-hidden border shadow-sm transition-all ${
          isExpanded ? 'shadow-md' : ''
        }`}
      >
        <div className="p-3 border-b">
          <div className="flex items-center justify-between">
            <div className="font-medium">{review.customerName}</div>
            <div className="flex items-center">
              {getReviewRating(review.rating)}
            </div>
          </div>
          <div className="text-xs mt-1 text-gray-500 flex items-center">
            <Calendar className="h-3 w-3 mr-1" />
            {format(review.date, 'MMM d, yyyy')}
          </div>
        </div>
        
        <CardContent className="p-3">
          <div className="mb-2">
            <span className="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full">
              {review.jobTitle}
            </span>
          </div>
          
          <p className={`text-sm mt-2 text-gray-700 ${!isExpanded ? "line-clamp-2" : ""}`}>
            {review.text}
          </p>
          
          {review.replied && review.replyText && isExpanded && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <p className="text-xs font-medium text-gray-500 mb-1">Your Response:</p>
              <p className="text-sm text-gray-600">{review.replyText}</p>
            </div>
          )}
          
          <div className="flex justify-between items-center mt-3">
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-xs p-0 h-auto text-gray-500"
              onClick={() => toggleExpandCard(review.id)}
            >
              {isExpanded ? "Show less" : "Read more"}
              <ChevronDown className={`ml-1 h-3 w-3 transition-transform ${isExpanded ? "rotate-180" : ""}`} />
            </Button>
            
            {!review.replied ? (
              <Button 
                variant="outline" 
                size="sm"
                className="text-primary border-primary/30 hover:bg-primary/10"
                onClick={() => {
                  setSelectedReview(review);
                  setReplyDialogOpen(true);
                }}
              >
                <MessageSquare className="h-3 w-3 mr-1" />
                Reply
              </Button>
            ) : (
              <Button 
                variant="ghost" 
                size="sm"
                className="text-gray-500"
                onClick={() => {
                  setSelectedReview(review);
                  setReplyText(review.replyText || "");
                  setReplyDialogOpen(true);
                }}
              >
                Edit Reply
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Mobile Header */}
      {isMobile && (
        <div className="bg-[#2563EB] -mx-4 -mt-4 px-4 py-5 text-white mb-6">
          <h1 className="text-xl font-bold">Customer Reviews</h1>
          <p className="text-white/80 text-sm mt-1">
            Manage feedback and build your reputation
          </p>
          
          <div className="mt-4 flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg p-2">
            <div className="flex flex-col items-center flex-1 px-3 py-2 rounded-md bg-white/10">
              <span className="text-2xl font-bold">{calculateAverageRating()}</span>
              <span className="text-xs text-white/80">Average</span>
            </div>
            <div className="flex flex-col items-center flex-1 px-3 py-2 rounded-md bg-white/10">
              <span className="text-2xl font-bold">{reviews.length}</span>
              <span className="text-xs text-white/80">Total</span>
            </div>
            <div className="flex flex-col items-center flex-1 px-3 py-2 rounded-md bg-white/10">
              <span className="text-2xl font-bold">{reviews.filter(r => !r.replied).length}</span>
              <span className="text-xs text-white/80">Pending</span>
            </div>
          </div>
        </div>
      )}
      
      {/* Desktop Header */}
      {!isMobile && (
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Reviews</h1>
            <p className="text-muted-foreground">
              Manage and respond to your customer reviews
            </p>
          </div>
          <Select
            value={sortBy}
            onValueChange={setSortBy}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="highest">Highest Rated</SelectItem>
              <SelectItem value="lowest">Lowest Rated</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Mobile Filter/Tabs */}
      {isMobile && (
        <div className="mb-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-3 w-full bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
              <TabsTrigger value="all" className="rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700">
                All
              </TabsTrigger>
              <TabsTrigger value="unreplied" className="rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700">
                Pending
              </TabsTrigger>
              <TabsTrigger value="replied" className="rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700">
                Replied
              </TabsTrigger>
            </TabsList>
          </Tabs>
          
          <div className="flex justify-between items-center mt-4">
            <div className="flex items-center">
              <Filter className="h-4 w-4 text-gray-500 mr-2" />
              <Select
                value={sortBy}
                onValueChange={setSortBy}
              >
                <SelectTrigger className="border-0 bg-gray-100 dark:bg-gray-800 h-8 px-3 text-sm">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="highest">Highest Rated</SelectItem>
                  <SelectItem value="lowest">Lowest Rated</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="text-sm text-gray-500">
              {getSortedReviews().length} reviews
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          {getSortedReviews().length > 0 ? (
            <div className="space-y-4">
              {isMobile ? (
                <div className="animate-fade-in">
                  {getSortedReviews().map(review => renderMobileReviewCard(review))}
                </div>
              ) : (
                // Desktop review cards
                getSortedReviews().map((review) => (
                  <Card key={review.id}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-base">{review.customerName}</CardTitle>
                          <div className="flex items-center gap-1 mt-1">
                            {getReviewRating(review.rating)}
                            <span className="text-sm text-muted-foreground ml-2">
                              {review.rating} out of 5
                            </span>
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {format(review.date, 'MMM d, yyyy')}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="mb-2">
                        <span className="text-xs text-muted-foreground px-2 py-1 bg-secondary/30 rounded-full">
                          {review.jobTitle}
                        </span>
                      </div>
                      <p className="text-sm">{review.text}</p>
                      
                      {review.replied && review.replyText && (
                        <div className="mt-4 pl-4 border-l-2 border-muted">
                          <p className="text-xs font-medium mb-1">Your Response:</p>
                          <p className="text-sm text-muted-foreground">{review.replyText}</p>
                        </div>
                      )}
                    </CardContent>
                    <CardFooter className="pt-0">
                      {!review.replied ? (
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="ml-auto"
                          onClick={() => {
                            setSelectedReview(review);
                            setReplyDialogOpen(true);
                          }}
                        >
                          <MessageSquare className="h-4 w-4 mr-1" />
                          Reply
                        </Button>
                      ) : (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="ml-auto"
                          onClick={() => {
                            setSelectedReview(review);
                            setReplyText(review.replyText || "");
                            setReplyDialogOpen(true);
                          }}
                        >
                          Edit Reply
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))
              )}
            </div>
          ) : (
            <Card className="py-12">
              <CardContent className="flex flex-col items-center justify-center text-center">
                <StarOff className="h-12 w-12 text-muted-foreground opacity-30 mb-3" />
                <h3 className="text-lg font-medium mb-1">No reviews yet</h3>
                <p className="text-muted-foreground max-w-sm">
                  No reviews yet. Complete more jobs to start building your 5-star reputation!
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Mobile Rating Summary Card */}
        {isMobile && (
          <Card className="mt-6 border shadow-sm">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Star className="h-5 w-5 text-yellow-500 mr-2 fill-yellow-500" />
                Rating Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col items-center bg-gray-50 dark:bg-gray-800/20 rounded-xl p-4">
                <div className="text-5xl font-bold text-primary">
                  {calculateAverageRating()}
                </div>
                <div className="flex items-center mt-1 mb-1">
                  {getReviewRating(Math.round(parseFloat(calculateAverageRating())))}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  From {reviews.length} reviews
                </div>
              </div>
              
              <div className="space-y-2 mt-3">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center gap-2">
                    <div className="flex items-center w-10">
                      <span className="text-sm font-medium">{rating}</span>
                      <Star className="h-3 w-3 text-yellow-400 fill-yellow-400 ml-1" />
                    </div>
                    <div className="relative flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                      <div 
                        className="absolute top-0 left-0 h-full bg-primary" 
                        style={{ width: `${getPercentageForRating(rating)}%` }}
                      ></div>
                    </div>
                    <div className="w-8 text-right text-xs font-medium">
                      {getTotalRatings(rating)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Desktop Rating Summary and Tips Cards */}
        {!isMobile && (
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Rating Summary</CardTitle>
                <CardDescription>
                  Overview of your customer ratings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col items-center">
                  <div className="text-5xl font-bold text-primary">
                    {calculateAverageRating()}
                  </div>
                  <div className="flex items-center mt-1 mb-1">
                    {getReviewRating(Math.round(parseFloat(calculateAverageRating())))}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    From {reviews.length} reviews
                  </div>
                </div>
                
                <div className="space-y-2 mt-6">
                  {[5, 4, 3, 2, 1].map((rating) => (
                    <div key={rating} className="flex items-center gap-2">
                      <div className="flex items-center w-12">
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400 mr-1" />
                        <span className="text-sm">{rating}</span>
                      </div>
                      <div className="relative flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className="absolute top-0 left-0 h-full bg-yellow-400" 
                          style={{ width: `${getPercentageForRating(rating)}%` }}
                        ></div>
                      </div>
                      <div className="w-10 text-right text-sm text-muted-foreground">
                        {getTotalRatings(rating)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
            
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Tips For Better Reviews</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <h3 className="font-medium">Respond promptly</h3>
                  <p className="text-sm text-muted-foreground">
                    Customers appreciate quick responses, aim to reply within 24-48 hours.
                  </p>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">Be professional</h3>
                  <p className="text-sm text-muted-foreground">
                    Even with negative feedback, remain respectful and solution-oriented.
                  </p>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">Ask for reviews</h3>
                  <p className="text-sm text-muted-foreground">
                    Politely request reviews after successfully completing jobs.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
      
      {/* Mobile Tips Section */}
      {isMobile && (
        <Card className="mt-6 border shadow-sm">
          <div className="bg-primary p-4 text-white">
            <div className="flex items-center">
              <Zap className="h-5 w-5 mr-2" />
              <h3 className="font-medium">Tips For Better Reviews</h3>
            </div>
          </div>
          <CardContent className="p-4">
            <div className="space-y-4">
              <div className="flex gap-3 p-3 bg-gray-50 dark:bg-gray-800/20 rounded-lg">
                <PlusCircle className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <h3 className="font-medium text-sm">Respond promptly</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    Customers appreciate quick responses, aim to reply within 24-48 hours.
                  </p>
                </div>
              </div>
              
              <div className="flex gap-3 p-3 bg-gray-50 dark:bg-gray-800/20 rounded-lg">
                <PlusCircle className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <h3 className="font-medium text-sm">Be professional</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    Even with negative feedback, remain respectful and solution-oriented.
                  </p>
                </div>
              </div>
              
              <div className="flex gap-3 p-3 bg-gray-50 dark:bg-gray-800/20 rounded-lg">
                <PlusCircle className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <h3 className="font-medium text-sm">Ask for reviews</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    Politely request reviews after successfully completing jobs.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      <Dialog open={replyDialogOpen} onOpenChange={setReplyDialogOpen}>
        <DialogContent className={isMobile ? "max-w-[95%] rounded-lg" : ""}>
          <DialogHeader>
            <DialogTitle>Reply to {selectedReview?.customerName}</DialogTitle>
            <DialogDescription>
              Your response will be visible to the customer and potential clients
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="bg-muted/50 p-3 rounded-md">
              <p className="text-sm font-medium mb-1">Original Review:</p>
              <p className="text-sm">{selectedReview?.text}</p>
            </div>
            <Textarea
              placeholder="Type your response here..."
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              rows={5}
              className="focus-visible:ring-primary"
            />
          </div>
          <DialogFooter className={isMobile ? "flex-col gap-2" : ""}>
            <Button 
              variant="outline" 
              onClick={() => setReplyDialogOpen(false)}
              className={isMobile ? "w-full" : ""}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleReplySubmit} 
              disabled={!replyText.trim()}
              className={isMobile ? "w-full" : ""}
            >
              {selectedReview?.replied ? 'Update Reply' : 'Post Reply'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
