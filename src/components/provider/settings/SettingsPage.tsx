
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Bell, 
  CreditCard, 
  Mail, 
  Phone, 
  Shield, 
  UserCog, 
  AlertTriangle,
  Check
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { AvailabilitySettingsSection } from './AvailabilitySettingsSection';

export const SettingsPage = () => {
  const { toast } = useToast();
  const [email, setEmail] = useState('<EMAIL>');
  const [phone, setPhone] = useState('(*************');
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(true);
  const [leadNotifications, setLeadNotifications] = useState(true);
  const [messageNotifications, setMessageNotifications] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  
  const handleSaveProfile = () => {
    setIsSaving(true);
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      toast({
        title: "Settings updated",
        description: "Your account settings have been saved successfully.",
      });
    }, 1000);
  };

  const handlePasswordReset = () => {
    toast({
      title: "Password reset email sent",
      description: "Check your email for instructions to reset your password.",
    });
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="account" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="availability">Availability</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="account" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                Manage your account details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <div className="flex items-center gap-2">
                  <Mail className="text-muted-foreground h-4 w-4" />
                  <Input
                    id="email"
                    placeholder="Email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="flex-1"
                  />
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <Check className="h-3 w-3 mr-1" /> Verified
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <div className="flex items-center gap-2">
                  <Phone className="text-muted-foreground h-4 w-4" />
                  <Input
                    id="phone"
                    placeholder="Phone number"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="pt-2">
                <Button 
                  variant="outline" 
                  onClick={handlePasswordReset}
                >
                  Reset Password
                </Button>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveProfile} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="availability" className="space-y-4">
          <AvailabilitySettingsSection />
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Choose how you would like to be notified
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Bell className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="email-notifications" className="flex-1">
                      Email Notifications
                    </Label>
                  </div>
                  <Switch
                    id="email-notifications"
                    checked={emailNotifications}
                    onCheckedChange={setEmailNotifications}
                  />
                </div>
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Bell className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="sms-notifications" className="flex-1">
                      SMS Notifications
                    </Label>
                  </div>
                  <Switch
                    id="sms-notifications"
                    checked={smsNotifications}
                    onCheckedChange={setSmsNotifications}
                  />
                </div>
                <Separator />
                
                <div className="space-y-3">
                  <h3 className="text-sm font-medium">Notification Types</h3>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="lead-notifications" className="text-sm text-muted-foreground pl-6">
                      New Leads
                    </Label>
                    <Switch
                      id="lead-notifications"
                      checked={leadNotifications}
                      onCheckedChange={setLeadNotifications}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="message-notifications" className="text-sm text-muted-foreground pl-6">
                      Customer Messages
                    </Label>
                    <Switch
                      id="message-notifications"
                      checked={messageNotifications}
                      onCheckedChange={setMessageNotifications}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveProfile} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Subscription Plan</CardTitle>
              <CardDescription>
                Manage your subscription and billing details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-primary/5 p-4 rounded-lg flex justify-between items-center">
                <div>
                  <h3 className="font-medium">Current Plan: <span className="text-primary">Free</span></h3>
                  <p className="text-sm text-muted-foreground mt-1">Commission Rate: 20% per job</p>
                </div>
                <Badge variant="outline" className="bg-primary/10 text-primary">Free Plan</Badge>
              </div>

              <Alert className="bg-amber-50 text-amber-800 border-amber-200">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Lower your commission by upgrading to Pro (15%) or Elite (12%) plans.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label className="text-base">Payment Method</Label>
                <div className="border rounded-md p-3 flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">No payment method on file</span>
                  </div>
                  <Button variant="outline" size="sm">Add Payment Method</Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-base">Billing History</Label>
                <div className="border rounded-md p-4 text-center text-muted-foreground">
                  <p>No billing history available</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel Subscription</Button>
              <Button>Upgrade Plan</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Manage your account security and privacy
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="two-factor" className="flex-1">
                      Two-Factor Authentication
                    </Label>
                  </div>
                  <Button variant="outline" size="sm">Enable</Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Add an extra layer of security to your account with two-factor authentication.
                </p>
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <UserCog className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="privacy" className="flex-1">
                      Privacy Settings
                    </Label>
                  </div>
                  <Button variant="outline" size="sm">Manage</Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Control who can see your profile and contact information.
                </p>
              </div>

              <Separator />

              <div className="pt-2">
                <Button variant="destructive">Delete My Account</Button>
                <p className="text-xs text-muted-foreground mt-2">
                  This action is permanent and cannot be undone.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <div className="flex justify-end">
        <Button variant="default" className="w-full md:w-auto">
          Sign Out
        </Button>
      </div>
    </div>
  );
};
