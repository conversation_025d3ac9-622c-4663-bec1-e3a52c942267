
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, CalendarClock } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { AvailabilityDialog } from '../calendar/AvailabilityDialog';

interface DayAvailability {
  enabled: boolean;
  startTime: string;
  endTime: string;
}

type WeekAvailability = {
  [key: string]: DayAvailability;
};

export const AvailabilitySettingsSection = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();
  const [availability, setAvailability] = useState<WeekAvailability | null>(null);
  
  // Load availability from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('providerAvailability');
    if (saved) {
      setAvailability(JSON.parse(saved));
    }
  }, [isDialogOpen]); // Refresh when dialog closes

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const period = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${hour12}:${minutes} ${period}`;
  };

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Availability Settings</CardTitle>
              <CardDescription className="mt-1">
                Configure your working hours for each day of the week
              </CardDescription>
            </div>
            <Button onClick={() => setIsDialogOpen(true)}>Edit Availability</Button>
          </div>
        </CardHeader>
        <CardContent>
          {availability ? (
            <div className="space-y-4">
              {Object.entries(availability).map(([dayId, dayData]) => (
                <div key={dayId} className="flex items-center justify-between border-b pb-3 last:border-0 last:pb-0">
                  <div className="flex items-center">
                    <div className="w-32 capitalize font-medium">
                      {dayId}
                    </div>
                    {dayData.enabled ? (
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-blue-500" />
                        <span>
                          {formatTime(dayData.startTime)} - {formatTime(dayData.endTime)}
                        </span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground italic">Not available</span>
                    )}
                  </div>
                  <Badge 
                    variant="outline" 
                    className={dayData.enabled 
                      ? "bg-green-50 text-green-600 border-green-200"
                      : "bg-gray-50 text-gray-500 border-gray-200"
                    }
                  >
                    {dayData.enabled ? "Available" : "Unavailable"}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <CalendarClock className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
              <p>No availability settings found</p>
              <p className="text-sm text-muted-foreground mt-1">
                Set your working hours to let customers know when you're available
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <AvailabilityDialog 
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
      />
    </>
  );
};
