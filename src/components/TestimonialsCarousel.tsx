
import React from 'react';
import { 
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from '@/components/ui/carousel';
import { Card, CardContent } from '@/components/ui/card';
import { StarRating } from './StarRating';
import {MapPin, Quote} from 'lucide-react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { useIsMobile } from '@/hooks/use-mobile';

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Homeowner",
    text: "Found a reliable handyman to fix my leaking roof within hours. The service was exceptional, and I've been using JobON for all my home maintenance needs since!",
    rating: 5,
    location: "San Francisco, CA",
    image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 75,
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Small Business Owner",
    text: "As a restaurant owner, I needed help moving equipment to our new location. JobON connected me with professional movers who handled everything with care. Incredible service!",
    rating: 5,
    location: "New York, NY",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 120,
  },
  {
    id: 3,
    name: "Emily Rodriguez",
    role: "Working Parent",
    text: "Finding reliable cleaning help used to be a struggle. Now with JobON, I can schedule recurring cleaning services with just a few clicks. It's been a lifesaver for maintaining our home while working full-time!",
    rating: 4,
    location: "Chicago, IL",
    image: "https://images.unsplash.com/photo-1554151228-14d9def656e4?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 100,
  },
  {
    id: 4,
    name: "David Thompson",
    role: "Property Manager",
    text: "Managing multiple properties means constant maintenance needs. JobON has been essential for quickly finding qualified professionals for everything from plumbing emergencies to routine landscaping.",
    rating: 5,
    location: "Austin, TX",
    image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 50,
  },
  {
    id: 5,
    name: "Lisa Patel",
    role: "Event Planner",
    text: "JobON helped me find last-minute staff for an important corporate event. The professionals were reliable and skilled. It's become an essential resource for my event planning business!",
    rating: 4,
    location: "Miami, FL",
    image: "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 150,
  },
  {
    id: 6,
    name: "Robert Wilson",
    role: "Office Manager",
    text: "Our office needed an urgent IT solution when our system went down. Within an hour through JobON, we had a qualified technician on-site who resolved our issue. The speed and quality of service was impressive.",
    rating: 5,
    location: "Seattle, WA",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 80,
  },
  {
    id: 7,
    name: "Amanda Foster",
    role: "Interior Designer",
    text: "I found excellent craftsmen for my client's custom cabinetry project through JobON. The quality of work exceeded expectations and was delivered on schedule. My clients were thrilled with the results!",
    rating: 5,
    location: "Portland, OR",
    image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 200,
  },
  {
    id: 8,
    name: "James Wright",
    role: "Real Estate Agent",
    text: "I use JobON to connect my clients with reliable contractors before listing their homes. The quick turnaround time for minor repairs has helped me sell properties faster and for better prices.",
    rating: 5,
    location: "Denver, CO",
    image: "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 65,
  },
  {
    id: 9,
    name: "Sophia Martinez",
    role: "Hotel Manager",
    text: "Our hotel relies on quick maintenance solutions to keep guests happy. JobON has been invaluable for finding reliable professionals at short notice, especially for plumbing and HVAC emergencies.",
    rating: 4,
    location: "Las Vegas, NV",
    image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 95,
  },
  {
    id: 10,
    name: "Thomas Lee",
    role: "Senior Citizen",
    text: "As a retiree, finding trustworthy help for home repairs was always worrisome. JobON has connected me with respectful professionals who don't overcharge and explain everything clearly.",
    rating: 5,
    location: "Phoenix, AZ",
    image: "https://images.unsplash.com/photo-1504257432389-52343af06ae3?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 10,
  },
  {
    id: 11,
    name: "Rachel Green",
    role: "Coffee Shop Owner",
    text: "When our espresso machine broke down during our busiest season, we found a specialist through JobON who fixed it the same day. Saved us thousands in potential lost revenue!",
    rating: 5,
    location: "Boston, MA",
    image: "https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 5,
  },
  {
    id: 12,
    name: "Kevin Barnes",
    role: "Apartment Resident",
    text: "Living in a small apartment, I needed space-saving solutions. Found a custom furniture maker on JobON who designed and built exactly what I needed. Reasonable prices and excellent craftsmanship!",
    rating: 4,
    location: "Nashville, TN",
    image: "https://images.unsplash.com/photo-1530268729831-4b0b9e170218?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    saved : 350,
  }
];

export const TestimonialsCarousel: React.FC = () => {
  const isMobile = useIsMobile();
  
  return (
    <div className="w-full max-w-5xl mx-auto px-0 md:px-10 relative">
      <Carousel
        opts={{
          align: "start",
          loop: true,
          containScroll: "trimSnaps",
        }}
      >
        <div className="flex items-center">
          <div className="flex-none mr-1 sm:mr-2 z-20">
            <CarouselPrevious className="h-8 w-8" scrollToTop={false} />
          </div>
          
          <div className="flex-grow overflow-hidden">
            <CarouselContent>
              {testimonials.map((testimonial) => (
                <CarouselItem key={testimonial.id} className="basis-[80%] md:basis-1/2 lg:basis-1/3">
                  <Card className="h-full">
                    {
                      !isMobile &&
                      <CardContent className="p-3 sm:p-6 flex flex-col justify-between h-full">
                        <div>
                          <div className="mb-3 opacity-70">
                            <Quote className="h-6 sm:h-8 w-6 sm:w-8 text-primary" />
                          </div>
                          <p className="text-foreground/80 mb-4 italic text-sm sm:text-base line-clamp-6">"{testimonial.text}"</p>
                        </div>
                        <div>
                          <div className="mb-2">
                            <StarRating rating={testimonial.rating} />
                          </div>
                          <div className="mt-4 flex items-center">
                            <Avatar className="h-8 w-8 sm:h-12 sm:w-12 mr-2 sm:mr-3 border-2 border-primary/20">
                              <AvatarImage src={testimonial.image} alt={testimonial.name} />
                              <AvatarFallback>{testimonial.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-semibold text-sm sm:text-base">{testimonial.name}</p>
                              <p className="text-xs sm:text-sm text-foreground/70">{testimonial.role} • {testimonial.location}</p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    }
                    {
                      isMobile &&
                        <div className="p-2 flex flex-col gap-4">
                          <div className="mt-4 flex items-center">
                            <Avatar className="h-12 w-12 mr-2 sm:mr-3 border-2 border-primary/20">
                              <AvatarImage src={testimonial.image} alt={testimonial.name} />
                              <AvatarFallback>{testimonial.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-semibold text-sm sm:text-base">{testimonial.name}</p>
                              <p className="text-xs sm:text-sm text-foreground/70">{testimonial.role} • {testimonial.location}</p>
                            </div>
                          </div>
                          <p className="text-foreground/80 text-sm sm:text-base line-clamp-6">"{testimonial.text}"</p>
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-sm text-primary">Saved ${testimonial.saved}</span>
                            <div className="flex items-center gap-2">
                              <MapPin className=" h-4 w-4 text-gray-400" />
                              <p className="text-xs text-gray-500">
                                {testimonial.location}
                              </p>
                            </div>
                          </div>
                        </div>
                    }
                  </Card>
                </CarouselItem>
              ))}
            </CarouselContent>
          </div>
          
          <div className="flex-none ml-1 sm:ml-2">
            <CarouselNext className="h-8 w-8" scrollToTop={false} />
          </div>
        </div>
      </Carousel>
    </div>
  );
};
