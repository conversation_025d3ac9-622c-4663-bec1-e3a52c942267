
import React from 'react';
import { Link } from 'react-router-dom';
import { SheetContent } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '../ThemeToggle';
import { MobileMenuAccordion } from './MobileMenuAccordion';
import { BadgeCheck, ChevronRight, Settings } from 'lucide-react';

interface MobileAccountSheetProps {
  onClose: () => void;
}

export const MobileAccountSheet: React.FC<MobileAccountSheetProps> = ({ onClose }) => {
  return (
    <SheetContent side="right" className="w-[85vw] sm:max-w-md p-0">
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between px-4 py-3 border-b">
          <div className="font-medium text-lg">Menu</div>
          <ThemeToggle />
        </div>
        
        <div className="flex-1 overflow-auto py-2 mobile-menu-wrapper">
          <div className="px-2 space-y-1">
            <Link 
              to="/auth" 
              className="flex items-center gap-3 px-4 py-3 text-foreground hover:bg-accent rounded-md mb-2"
              onClick={onClose}
            >
              <div className="section-icon">
                <BadgeCheck className="h-5 w-5 text-primary" />
              </div>
              <span className="font-medium">Sign In / Register</span>
              <ChevronRight className="h-4 w-4 ml-auto" />
            </Link>
            
            <MobileMenuAccordion onItemClick={onClose} />
          </div>
        </div>
        
        <div className="py-3 px-4 border-t mt-auto">
          <Button 
            asChild 
            className="w-full"
            onClick={onClose}
          >
            <Link to="/auth" className="flex items-center justify-center gap-2">
              <Settings className="h-4 w-4" />
              <span>Account Settings</span>
            </Link>
          </Button>
        </div>
      </div>
    </SheetContent>
  );
};
