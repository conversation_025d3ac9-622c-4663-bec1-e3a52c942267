
import React, { useRef, useEffect, useState } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { User, MapPin, Phone, Mail, Loader2 } from 'lucide-react';
import { useAddressAutocomplete } from '@/hooks/use-address-autocomplete';
import {apiService} from "@/services/api.ts";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface UserInfo {
  fullName: string;
  address: string;
  phone: string;
  email: string;
}

interface FormErrors {
  fullName?: string;
  address?: string;
  phone?: string;
  email?: string;
}

interface AddressData {
  address: string;
  city: string;
  state: string;
  zipCode: string;
}

interface MobileUserInfoFormProps {
  contactInfo: UserInfo;
  errors: FormErrors;
  onContactInfoChange: (field: keyof UserInfo, value: string) => void;
  onPhoneChange: (value: string) => void;
  onNext: () => void;
  submitAttempted?: boolean;
  onAddressSelect?: (addressData: AddressData) => void;
}

interface dataUserAddressesType {
  id: number;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  created_at: string;
  updated_at: string;
}

interface ResponseType {
  success: boolean;
  data: dataUserAddressesType[];
}

export const MobileUserInfoForm: React.FC<MobileUserInfoFormProps> = ({
  contactInfo,
  errors,
  onContactInfoChange,
  onPhoneChange,
  submitAttempted = false,
  onAddressSelect,
}) => {
  const {
    query,
    setQuery,
    suggestions,
    isLoading,
    error,
    handleSelectAddress,
  } = useAddressAutocomplete();

  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeIndex, setActiveIndex] = useState(-1);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [dataUserAddresses, setDataUserAddresses] = useState<dataUserAddressesType[]>([]);

  // Update the query when contactInfo.address changes (for initial value)
  useEffect(() => {
    if (contactInfo.address && !query) {
      setQuery(contactInfo.address);
    }
  }, [contactInfo.address, query, setQuery]);

  // Handle outside clicks to close suggestions
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(e.target as Node) && 
          inputRef.current && !inputRef.current.contains(e.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    // Arrow down
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setActiveIndex(prev => 
        prev < suggestions.length - 1 ? prev + 1 : prev
      );
    }
    // Arrow up
    else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setActiveIndex(prev => (prev > 0 ? prev - 1 : 0));
    }
    // Enter
    else if (e.key === 'Enter' && activeIndex >= 0) {
      e.preventDefault();
      const selected = suggestions[activeIndex];
      if (selected) {
        handleAddressSelection(selected);
      }
    }
    // Escape
    else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  // Handle address selection
  const handleAddressSelection = (address: AddressData) => {
    handleSelectAddress(address);
    onContactInfoChange('address', address.address);
    setShowSuggestions(false);
    setActiveIndex(-1);

    // Pass the selected address data to the parent component
    if (onAddressSelect) {
      onAddressSelect(address);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    onContactInfoChange('address', value);
    setShowSuggestions(true);
    setActiveIndex(-1);
  };
  const { token } = useAuth();
  const fetchUserAddresses = async () => {
    if (!token) return;
    
    try {
      const endpoint = `/api/user-addresses`;
      const response = await apiService(endpoint, {
        method: 'GET',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        requiresAuth: true,
        includeCredentials: true
      });
      
      if (response.isSuccess && response.data) {
        const data = response.data as ResponseType;
        if (data.success && data.data.length > 0){
          setDataUserAddresses(data.data);
        }
      }
    } catch (error) {
      console.error("Error fetching user addresses:", error);
    }
  };

  useEffect(()=>{
    fetchUserAddresses();
  },[])

  return (
    <div className="space-y-6 pb-10">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="fullName" className="text-sm font-medium text-gray-700">
            Full Name
          </Label>
          <div className="relative">
            <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            <Input
              id="fullName"
              type="text"
              placeholder="Full Name"
              value={contactInfo.fullName}
              onChange={(e) => onContactInfoChange('fullName', e.target.value)}
              className={`pl-10 ${errors.fullName && submitAttempted ? 'border-red-500 focus:border-red-500 focus-visible:ring-red-300' : ''}`}
              aria-invalid={!!errors.fullName}
            />
          </div>
          {errors.fullName && submitAttempted && (
            <p className="text-sm text-red-500">{errors.fullName}</p>
          )}
        </div>
        {dataUserAddresses?.length ? (
            <div className={'flex flex-col gap-2'}>
              <Label htmlFor="fullName" className="text-sm font-medium text-gray-700">
                Select your location:
              </Label>
              <div className="mb-2 [&>button]:pl-2">
                <Select
                    onValueChange={(value) => {
                      const selectedAddress = dataUserAddresses.find(addr => addr.address === value);
                      if (selectedAddress) {
                        setQuery(selectedAddress.address);
                        onContactInfoChange('address', selectedAddress.address);
                        if (onAddressSelect) {
                          onAddressSelect({
                            address: selectedAddress.address,
                            city: selectedAddress.city,
                            state: selectedAddress.state,
                            zipCode: selectedAddress.zip_code
                          });
                        }
                      }
                    }}
                >
                  <SelectTrigger
                    className={`pl-10 ${errors.address && submitAttempted ? 'border-red-500 focus:border-red-500 focus-visible:ring-red-300' : ''}`}
                    aria-invalid={!!errors.address}>
                    <SelectValue placeholder="Select an address" />
                  </SelectTrigger>
                  <SelectContent>
                    {dataUserAddresses.map((address) => (
                        <SelectItem key={address.id} value={address.address}>
                          {address.address}
                        </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
        ):<></>}
        <div className="space-y-2 relative">
          <Label htmlFor="address" className="text-sm font-medium text-gray-700">
            Address
          </Label>
          <div className="relative">
            <MapPin className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            <Input
              id="address"
              ref={inputRef}
              type="text"
              placeholder="Address"
              value={query}
              onChange={handleInputChange}
              onFocus={() => setShowSuggestions(true)}
              onKeyDown={handleKeyDown}
              className={`pl-10 pr-8 ${errors.address && submitAttempted ? 'border-red-500 focus:border-red-500 focus-visible:ring-red-300' : ''}`}
              aria-invalid={!!errors.address}
              aria-autocomplete="list"
              aria-controls="address-suggestions"
              aria-expanded={showSuggestions}
            />
            {isLoading && (
              <div className="absolute right-3 top-2.5">
                <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
              </div>
            )}
          </div>

          {showSuggestions && suggestions.length > 0 && (
            <div 
              ref={suggestionsRef}
              id="address-suggestions"
              className="absolute z-10 w-ful mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
              role="listbox"
            >
              {suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className={`relative cursor-pointer select-none py-2 px-4 ${
                    index === activeIndex ? 'bg-blue-100 text-blue-900' : 'text-gray-900'
                  }`}
                  onClick={() => handleAddressSelection(suggestion)}
                  onMouseEnter={() => setActiveIndex(index)}
                  role="option"
                  aria-selected={index === activeIndex}
                >
                  <div className="font-medium">{suggestion.address}</div>
                </div>
              ))}
            </div>
          )}

          {error && (
            <p className="text-sm text-red-500">Error fetching addresses: {error}</p>
          )}

          {errors.address && submitAttempted && (
            <p className="text-sm text-red-500">{errors.address}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
            Phone Number
          </Label>
          <div className="relative">
            <Phone className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            <Input
              id="phone"
              type="tel"
              placeholder="Phone Number"
              value={contactInfo.phone}
              onChange={(e) => onPhoneChange(e.target.value)}
              className={`pl-10 ${errors.phone && submitAttempted ? 'border-red-500 focus:border-red-500 focus-visible:ring-red-300' : ''}`}
              aria-invalid={!!errors.phone}
            />
          </div>
          {errors.phone && submitAttempted && (
            <p className="text-sm text-red-500">{errors.phone}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium text-gray-700">
            Email Address
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            <Input
              id="email"
              type="email"
              placeholder="Email Address"
              value={contactInfo.email}
              onChange={(e) => onContactInfoChange('email', e.target.value)}
              className={`pl-10 ${errors.email && submitAttempted ? 'border-red-500 focus:border-red-500 focus-visible:ring-red-300' : ''}`}
              aria-invalid={!!errors.email}
            />
          </div>
          {errors.email && submitAttempted && (
            <p className="text-sm text-red-500">{errors.email}</p>
          )}
        </div>
      </div>
      {/* No fixed bottom button here for mobile. It's in parent for review step. */}
    </div>
  );
};
