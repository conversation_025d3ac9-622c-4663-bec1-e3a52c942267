
import React from 'react';
import { ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useNavigate, useLocation } from 'react-router-dom';
import { navigateToPreviousPage } from '@/utils/navigationUtils';

interface MobileCreateJobHeaderProps {
  title: string;
  currentStep: number;
  totalSteps: number;
  onBack: () => void;
}

export const MobileCreateJobHeader: React.FC<MobileCreateJobHeaderProps> = ({
  title,
  currentStep,
  totalSteps,
  onBack
}) => {
  const progressPercentage = Math.round((currentStep / totalSteps) * 100);
  const navigate = useNavigate();
  const location = useLocation();

  const handleBackClick = () => {
    if (currentStep === 1) {
      window.history.back();
    } else {
      onBack();
    }
  };


  return (
    <div className="sticky top-0 z-10 bg-white dark:bg-gray-900">
      <div className="flex items-center p-4">
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={handleBackClick}
          className="mr-2"
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-lg font-medium flex-1">{title}</h1>
      </div>
      <Progress 
        value={progressPercentage} 
        className="h-1" 
      />
      <div className="flex justify-between px-4 py-1 text-xs text-gray-500">
        <span>Step {currentStep} of {totalSteps}</span>
        <span>{progressPercentage}%</span>
      </div>
    </div>
  );
};
