
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  <PERSON>ch, Zap, Sparkles, Scissors, HelpCircle, Banknote, 
  BookOpen, Search, Calendar, User, ArrowRight,
  Users, LifeBuoy, Phone
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface MobileHamburgerMenuProps {
  onItemClick: () => void;
  isAuthenticated: boolean;
  onAuthClick: () => void;
  onLogout: () => void;
}

export const MobileHamburgerMenu: React.FC<MobileHamburgerMenuProps> = ({
  onItemClick,
  isAuthenticated,
  onAuthClick,
  onLogout
}) => {
  const coreServices = [
    { id: 'plumbing', name: 'Plumbing', icon: Wrench, color: 'text-blue-600', bg: 'bg-blue-100' },
    { id: 'electrical', name: 'Electrical', icon: Zap, color: 'text-amber-600', bg: 'bg-amber-100' },
    { id: 'cleaning', name: 'Cleaning', icon: Sparkles, color: 'text-cyan-600', bg: 'bg-cyan-100' },
    { id: 'landscaping', name: 'Landscaping', icon: Scissors, color: 'text-green-600', bg: 'bg-green-100' },
  ];

  const resources = [
    { id: 'how-it-works', name: 'How It Works', icon: HelpCircle, path: '/how-it-works' },
    { id: 'financing', name: 'Financing Options', icon: Banknote, path: '/financing' },
    { id: 'blog', name: 'Blog & Tips', icon: BookOpen, path: '/blog' },
    { id: 'support', name: 'Customer Support', icon: Phone, path: '/support' },
  ];

  const providerResources = [
    { id: 'find-jobs', name: 'Find Jobs', icon: Search, path: '/jobs' },
    { id: 'free-tools', name: 'Business Tools', icon: Wrench, path: '/free-tools' },
    { id: 'join-us', name: 'Become a Provider', icon: Calendar, path: '/for-providers' },
  ];

  return (
    <div className="flex flex-col h-full max-h-[85vh] overflow-y-auto">
      {/* Quick Actions */}
      <div className="px-4 py-3 border-b bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900">
        <Button 
          className="w-full bg-primary hover:bg-primary/90 min-h-[48px]"
          onClick={onItemClick}
          asChild
        >
          <Link to="/create-job" className="flex items-center justify-center gap-2">
            <span className="font-semibold">Post a Job</span>
            <ArrowRight className="h-4 w-4" />
          </Link>
        </Button>
      </div>

      <div className="flex-1 px-2 py-3 space-y-2">
        <Accordion type="single" defaultValue="services" className="space-y-2">
          {/* Services Section */}
          <AccordionItem value="services" className="border rounded-lg overflow-hidden">
            <AccordionTrigger className="px-4 py-3 hover:bg-accent/50 hover:no-underline">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-blue-100">
                  <Wrench className="h-5 w-5 text-blue-600" />
                </div>
                <span className="font-semibold">Services</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-2 py-3">
              <div className="grid grid-cols-2 gap-2">
                {coreServices.map((service) => {
                  const Icon = service.icon;
                  return (
                    <Link
                      key={service.id}
                      to={`/services/${service.id}`}
                      onClick={onItemClick}
                      className="flex flex-col items-center p-3 rounded-lg border hover:bg-accent/50 transition-colors min-h-[80px] touch-target"
                    >
                      <div className={`p-2 rounded-full ${service.bg} mb-1`}>
                        <Icon className={`h-5 w-5 ${service.color}`} />
                      </div>
                      <span className="text-xs font-medium text-center">{service.name}</span>
                    </Link>
                  );
                })}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Resources Section */}
          <AccordionItem value="resources" className="border rounded-lg overflow-hidden">
            <AccordionTrigger className="px-4 py-3 hover:bg-accent/50 hover:no-underline">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-green-100">
                  <LifeBuoy className="h-5 w-5 text-green-600" />
                </div>
                <span className="font-semibold">Resources</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-2 py-3">
              <div className="space-y-1">
                {resources.map((resource) => {
                  const Icon = resource.icon;
                  return (
                    <Link
                      key={resource.id}
                      to={resource.path}
                      onClick={onItemClick}
                      className="flex items-center gap-3 px-3 py-3 rounded-md hover:bg-accent/50 transition-colors min-h-[48px] touch-target"
                    >
                      <Icon className="h-5 w-5 text-primary" />
                      <span className="font-medium">{resource.name}</span>
                      <ArrowRight className="h-4 w-4 ml-auto text-gray-400" />
                    </Link>
                  );
                })}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* For Providers Section */}
          <AccordionItem value="providers" className="border rounded-lg overflow-hidden">
            <AccordionTrigger className="px-4 py-3 hover:bg-accent/50 hover:no-underline">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-purple-100">
                  <Users className="h-5 w-5 text-purple-600" />
                </div>
                <span className="font-semibold">For Providers</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-2 py-3">
              <div className="space-y-1">
                {providerResources.map((resource) => {
                  const Icon = resource.icon;
                  return (
                    <Link
                      key={resource.id}
                      to={resource.path}
                      onClick={onItemClick}
                      className="flex items-center gap-3 px-3 py-3 rounded-md hover:bg-accent/50 transition-colors min-h-[48px] touch-target"
                    >
                      <Icon className="h-5 w-5 text-primary" />
                      <span className="font-medium">{resource.name}</span>
                      <ArrowRight className="h-4 w-4 ml-auto text-gray-400" />
                    </Link>
                  );
                })}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      {/* Account Section */}
      <div className="px-4 py-3 border-t bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
        {isAuthenticated ? (
          <div className="space-y-2">
            <Button 
              variant="outline" 
              className="w-full min-h-[48px]"
              onClick={onAuthClick}
            >
              <User className="mr-2 h-4 w-4" />
              Dashboard
            </Button>
            <Button 
              variant="ghost" 
              className="w-full min-h-[48px] text-gray-600"
              onClick={onLogout}
            >
              Sign Out
            </Button>
          </div>
        ) : (
          <Button 
            variant="outline"
            className="w-full min-h-[48px]"
            onClick={onAuthClick}
          >
            <User className="mr-2 h-4 w-4" />
            Sign In / Register
          </Button>
        )}
      </div>
    </div>
  );
};
