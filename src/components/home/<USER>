import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { BlogCard } from '@/components/BlogCard';
import { useIsMobile } from '@/hooks/use-mobile';
import { fetchFeaturedPosts, BlogPost } from '@/services/wordpressApi';

export const LatestBlogPosts: React.FC = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isMobile = useIsMobile();

  useEffect(() => {
    const loadPosts = async () => {
      try {
        setLoading(true);
        // Fetch the latest 8 blog posts
        const latestPosts = await fetchFeaturedPosts(8);
        setPosts(latestPosts);
        setLoading(false);
      } catch (err) {
        setError('Failed to load blog posts. Please try again later.');
        setLoading(false);
      }
    };

    loadPosts();
  }, []);

  return (
    <section className="py-12 md:py-20 px-4 md:px-6 lg:px-12 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-center mb-6 md:mb-12">
          <div>
            <div className="flex items-center space-x-2">
              <h2 className="text-2xl md:text-4xl font-bold text-gray-900 dark:text-white">
                Latest From Our Blog
              </h2>
              <div className="h-1 w-8 md:w-12 bg-primary rounded-full"></div>
            </div>
          </div>
          {/* Desktop "View All" button */}
          <Link to="/blog" className="hidden md:flex items-center text-primary hover:text-primary/80 transition-colors">
            <span>View all</span>
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <p>{error}</p>
          </div>
        )}

        {/* Loading State */}
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading blog posts...</span>
          </div>
        ) : posts.length === 0 ? (
          // Empty State
          <div className="text-center py-12">
            <p className="text-lg text-gray-600 dark:text-gray-300">No blog posts found.</p>
          </div>
        ) : (
          // Blog Posts Grid
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
            {posts.map(post => (
              <div key={post.id} className="h-full">
                <BlogCard post={post} compact={true} />
              </div>
            ))}
          </div>
        )}
        
        {/* Mobile "View All" button */}
        {isMobile && !loading && posts.length > 0 && (
          <div className="text-center mt-6">
            <Link to="/blog" className="inline-flex items-center text-primary hover:text-primary/80 transition-colors text-sm font-medium">
              View all articles
              <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default LatestBlogPosts;