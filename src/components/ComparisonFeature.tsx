
import React from 'react';
import { CheckCircle2, XCircle } from 'lucide-react';

interface ComparisonFeatureProps {
  feature: string;
  jobon: boolean;
  angi: boolean;
  thumbtack: boolean;
  taskrabbit: boolean;
  index: number;
}

const ComparisonFeature: React.FC<ComparisonFeatureProps> = ({
  feature,
  jobon,
  angi,
  thumbtack,
  taskrabbit,
  index
}) => {
  return (
    <tr className={index % 2 === 0 ? 'bg-gray-50 dark:bg-gray-800/50' : 'bg-white dark:bg-gray-900'}>
      <td className="p-4 text-gray-700 dark:text-gray-300 border-b dark:border-gray-700">
        {feature}
      </td>
      <td className="p-4 text-center border-b dark:border-gray-700 bg-blue-50 dark:bg-blue-900/20">
        {jobon ? 
          <CheckCircle2 className="w-6 h-6 mx-auto text-primary" /> : 
          <XCircle className="w-6 h-6 mx-auto text-gray-400 dark:text-gray-600" />
        }
      </td>
      <td className="p-4 text-center border-b dark:border-gray-700">
        {angi ? 
          <CheckCircle2 className="w-6 h-6 mx-auto text-green-500" /> : 
          <XCircle className="w-6 h-6 mx-auto text-gray-400 dark:text-gray-600" />
        }
      </td>
      <td className="p-4 text-center border-b dark:border-gray-700">
        {thumbtack ? 
          <CheckCircle2 className="w-6 h-6 mx-auto text-green-500" /> : 
          <XCircle className="w-6 h-6 mx-auto text-gray-400 dark:text-gray-600" />
        }
      </td>
      <td className="p-4 text-center border-b dark:border-gray-700">
        {taskrabbit ? 
          <CheckCircle2 className="w-6 h-6 mx-auto text-green-500" /> : 
          <XCircle className="w-6 h-6 mx-auto text-gray-400 dark:text-gray-600" />
        }
      </td>
    </tr>
  );
};

export default ComparisonFeature;
