import React, { useState, useEffect } from 'react';
import { Search, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { useGeolocation } from '@/hooks/use-geolocation';
interface SearchBarProps {
  variant?: 'standard' | 'zipcode';
  placeholder?: string;
  locationPlaceholder?: string;
  showButton?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  defaultZip?: string;
  onZipSubmit?: (zipCode: string) => void;
}
export const SearchBar: React.FC<SearchBarProps> = ({
  variant = 'standard',
  placeholder = 'What service do you need?',
  locationPlaceholder = 'Enter location',
  showButton = true,
  size = 'md',
  className = '',
  defaultZip = '',
  onZipSubmit
}) => {
  const [searchText, setSearchText] = useState('');
  const [zipCode, setZipCode] = useState(defaultZip);
  const [searchFocused, setSearchFocused] = useState(false);
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const {
    zipCode: detectedZipCode,
    loading
  } = useGeolocation();

  // Set detected zipcode when available
  useEffect(() => {
    if (detectedZipCode && !zipCode) {
      console.log("Using detected zipcode:", detectedZipCode);
      setZipCode(detectedZipCode);
    }
  }, [detectedZipCode, zipCode]);
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (variant === 'zipcode') {
      if (onZipSubmit) {
        onZipSubmit(zipCode);
      } else {
        navigate(`/professionals?zip=${zipCode}`);
      }
    } else if (searchText) {
      navigate(`/professionals?search=${encodeURIComponent(searchText)}${zipCode ? `&zip=${zipCode}` : ''}`);
    }
  };
  const sizeClasses = {
    sm: 'h-9 text-sm',
    md: 'h-11 text-base',
    lg: 'h-12 text-lg'
  };

  // Enhanced mobile styling with more compact design
  const mobileClasses = isMobile ? 'shadow-md rounded-lg overflow-hidden mb-1' : '';
  const buttonClasses = isMobile ? 'bg-[#4169E1] hover:bg-[#3154c5] text-white font-medium rounded-none w-full' : 'bg-primary hover:bg-primary-dark text-white font-medium rounded-none';
  return <form onSubmit={handleSearchSubmit} className={`relative ${className}`}>
      
    </form>;
};