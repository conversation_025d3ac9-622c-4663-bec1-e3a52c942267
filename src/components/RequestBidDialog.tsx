
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, CheckCircle } from 'lucide-react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

interface Job {
  id: string;
  title: string;
  description: string;
}

interface RequestBidDialogProps {
  isOpen: boolean;
  onClose: () => void;
  providerId: string;
  providerName: string;
}

export const RequestBidDialog = ({ isOpen, onClose, providerId, providerName }: RequestBidDialogProps) => {
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null);
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  
  // Mock jobs - this would come from your API in a real implementation
  const [userJobs, setUserJobs] = useState<Job[]>([
    {
      id: 'job-1',
      title: 'Bathroom Remodel',
      description: 'Need help with a full bathroom renovation'
    },
    {
      id: 'job-2',
      title: 'Electrical Rewiring',
      description: 'Need to update old wiring in the kitchen'
    }
  ]);

  const handleRequestBid = async () => {
    if (!selectedJobId) return;
    
    setStatus('loading');
    
    try {
      // This would be an actual API call in a real implementation
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Simulate success
      setStatus('success');
    } catch (error) {
      setStatus('error');
    }
  };

  const handleClose = () => {
    // Reset the dialog state
    setSelectedJobId(null);
    setStatus('idle');
    onClose();
  };

  const hasJobs = userJobs.length > 0;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">
            Request Bid from {providerName}
          </DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          {status === 'success' ? (
            <div className="text-center py-6">
              <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
              <h3 className="text-lg font-medium">Bid Request Sent!</h3>
              <p className="text-sm text-gray-500 mt-2">
                {providerName} will review your job and respond soon.
              </p>
              <Button className="mt-4 w-full" onClick={handleClose}>
                Done
              </Button>
            </div>
          ) : !hasJobs ? (
            <div className="text-center py-6">
              <p className="text-gray-600 mb-4">You don't have any active jobs to request bids for.</p>
              <Button onClick={() => window.location.href = '/create-job'}>
                Post a New Job
              </Button>
            </div>
          ) : (
            <>
              <p className="text-sm text-gray-500 mb-4">
                Select one of your jobs to request a bid:
              </p>
              
              <RadioGroup value={selectedJobId || ""} onValueChange={setSelectedJobId} className="gap-3">
                {userJobs.map((job) => (
                  <div key={job.id} className="flex items-start space-x-2 border rounded-lg p-3 cursor-pointer hover:bg-gray-50">
                    <RadioGroupItem value={job.id} id={job.id} className="mt-1" />
                    <Label htmlFor={job.id} className="flex-1 cursor-pointer">
                      <div className="font-medium">{job.title}</div>
                      <div className="text-sm text-gray-500 line-clamp-2">{job.description}</div>
                    </Label>
                  </div>
                ))}
              </RadioGroup>
              
              <DialogFooter className="mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  className="w-full sm:w-auto"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleRequestBid}
                  disabled={!selectedJobId || status === 'loading'}
                  className="w-full sm:w-auto"
                >
                  {status === 'loading' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : 'Request Bid'}
                </Button>
              </DialogFooter>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
