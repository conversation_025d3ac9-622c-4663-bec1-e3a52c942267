import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { CheckCircle, X, ArrowRight } from 'lucide-react';
const ProviderSignupDialog = () => {
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();
  useEffect(() => {
    // Show dialog after 5 seconds on every page visit
    const timer = setTimeout(() => {
      setOpen(true);
    }, 5000);
    return () => clearTimeout(timer);
  }, []);
  const handleClose = () => setOpen(false);
  const handleSignUp = () => {
    navigate('/provider-signup');
    handleClose();
  };
  return <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[750px] lg:max-w-[900px] p-0 overflow-hidden rounded-xl border-none shadow-2xl">
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground z-50">
          
          <span className="sr-only">Close</span>
        </DialogClose>
        
        <div className="grid grid-cols-1 md:grid-cols-2">
          <div className="p-6 md:p-8">
            <DialogHeader>
              <DialogTitle className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                Grow Your Business with JobON
              </DialogTitle>
            </DialogHeader>
            
            <div className="mt-4">
              <p className="text-muted-foreground text-base">
                Join thousands of professionals who have expanded their client base and increased their income.
              </p>
              
              <div className="space-y-4 mt-6">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
                  <span className="text-base">Connect with new customers in your area</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
                  <span className="text-base">Receive job alerts that match your skills</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
                  <span className="text-base">Build your online reputation with verified reviews</span>
                </div>
              </div>
              
              <Card className="bg-primary/5 mt-6 p-3 border-none rounded-lg">
                <p className="text-center font-medium">
                  <span className="font-bold">Starter Plan</span> - Free to Get Started, No Credit Card Required
                </p>
              </Card>
              
              <div className="mt-6 flex flex-col space-y-3">
                <Button size="lg" className="w-full py-5 text-base font-medium rounded-lg shadow-lg shadow-primary/25 hover:shadow-primary/40 transition-all duration-300 relative group" onClick={handleSignUp}>
                  <div className="flex items-center justify-center gap-2">
                    Sign Up As a Provider
                    <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </div>
                </Button>
                
                <Button variant="outline" className="w-full border-2 py-4 text-base hover:bg-muted/30" onClick={handleClose}>
                  No, thanks
                </Button>
              </div>
              
              <div className="mt-4 text-center text-xs text-muted-foreground">
                By signing up, you agree to our{' '}
                <Link to="/terms" className="underline underline-offset-2 hover:text-primary transition-colors">Terms</Link> and{' '}
                <Link to="/privacy" className="underline underline-offset-2 hover:text-primary transition-colors">Privacy Policy</Link>
              </div>
            </div>
          </div>
          
          <div className="relative hidden md:block">
            <img src="/lovable-uploads/05d16b12-7db3-4f35-a7e3-ab600fe02ab7.png" alt="Service professional using JobON" className="h-full w-full object-cover object-contain" />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
            <div className="absolute bottom-3 left-3 right-3 p-3 rounded-lg bg-white/90 backdrop-blur-sm">
              <p className="text-sm font-semibold">Join over 10,000+ service professionals</p>
              <div className="flex items-center mt-1">
                <div className="flex -space-x-2">
                  {[...Array(4)].map((_, i) => <div key={i} className="h-6 w-6 rounded-full bg-primary text-white border-2 border-white flex items-center justify-center text-xs font-medium">
                      P
                    </div>)}
                </div>
                <span className="text-xs ml-2 text-muted-foreground">Trusted by professionals nationwide</span>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>;
};
export default ProviderSignupDialog;