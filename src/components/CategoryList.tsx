
import React, { useState } from 'react';
import { ServiceCard } from './ServiceCard';
import { Wrench, Zap, PaintBucket, Bug, Trees, Hammer, Settings, Fan, Sun, Construction, ChevronRight, ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';

export const categories = [
  {
    id: 'plumbing',
    title: 'Plumbing',
    description: 'Fixing leaks, clogs, installations, and plumbing repairs for homes, offices, and commercial properties.',
    icon: <Wrench className="h-6 w-6" strokeWidth={1.5} />,
    color: 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400',
    borderColor: 'border-blue-200 dark:border-blue-700',
    iconBgColor: 'bg-blue-100 dark:bg-blue-800',
    iconColor: 'text-blue-600 dark:text-blue-400',
  },
  {
    id: 'electrical',
    title: 'Electrical',
    description: 'Wiring, installations, repairs, and electrical maintenance for all property types.',
    icon: <Zap className="h-6 w-6" strokeWidth={1.5} />,
    color: 'bg-amber-50 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400',
    borderColor: 'border-amber-200 dark:border-amber-700',
    iconBgColor: 'bg-amber-100 dark:bg-amber-800',
    iconColor: 'text-amber-600 dark:text-amber-400',
  },
  {
    id: 'cleaning',
    title: 'Cleaning',
    description: 'Professional cleaning services for residential, commercial, and office spaces.',
    icon: <PaintBucket className="h-6 w-6" strokeWidth={1.5} />,
    color: 'bg-cyan-50 dark:bg-cyan-900/30 text-cyan-600 dark:text-cyan-400',
    borderColor: 'border-cyan-200 dark:border-cyan-700',
    iconBgColor: 'bg-cyan-100 dark:bg-cyan-800',
    iconColor: 'text-cyan-600 dark:text-cyan-400',
  },
  {
    id: 'landscaping',
    title: 'Landscaping',
    description: 'Lawn care, gardening, tree services, and outdoor maintenance for properties of all sizes.',
    icon: <Trees className="h-6 w-6" strokeWidth={1.5} />,
    color: 'bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400',
    borderColor: 'border-green-200 dark:border-green-700',
    iconBgColor: 'bg-green-100 dark:bg-green-800',
    iconColor: 'text-green-600 dark:text-green-400',
  },
];

export const CategoryList: React.FC = () => {
  const isMobile = useIsMobile();
  const [currentPage, setCurrentPage] = useState(0);
  
  // For desktop: display all categories
  // For mobile: display 4 categories per page
  const itemsPerPage = isMobile ? 4 : categories.length;
  const pageCount = Math.ceil(categories.length / itemsPerPage);
  
  const handlePrevPage = () => {
    setCurrentPage((prev) => (prev > 0 ? prev - 1 : prev));
  };
  
  const handleNextPage = () => {
    setCurrentPage((prev) => (prev < pageCount - 1 ? prev + 1 : prev));
  };
  
  const visibleCategories = isMobile
    ? categories.slice(currentPage * itemsPerPage, (currentPage + 1) * itemsPerPage)
    : categories;

  return (
    <div className="w-full px-1">
      {isMobile && (
        <div className="mb-6 flex items-center justify-between">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Service Category
          </h3>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 shadow-sm bg-white dark:bg-gray-800"
              onClick={handlePrevPage}
              disabled={currentPage === 0}
            >
              <ChevronLeft className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="sr-only">Previous page</span>
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 shadow-sm bg-white dark:bg-gray-800"
              onClick={handleNextPage}
              disabled={currentPage === pageCount - 1}
            >
              <ChevronRight className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="sr-only">Next page</span>
            </Button>
          </div>
        </div>
      )}
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 relative w-full max-w-full overflow-hidden animate-fade-in">
        {visibleCategories.map((category) => (
          <ServiceCard
            key={category.id}
            id={category.id}
            title={category.title}
            description={category.description}
            icon={category.icon}
            color={category.color}
            borderColor={category.borderColor}
            iconBgColor={category.iconBgColor}
            iconColor={category.iconColor}
          />
        ))}
      </div>
      
      {isMobile && (
        <div className="mt-6 flex justify-center">
          {Array.from({ length: pageCount }).map((_, index) => (
            <Button
              key={index}
              variant="ghost"
              size="icon"
              className={`h-2 w-2 rounded-full mx-1 ${
                currentPage === index 
                  ? 'bg-primary dark:bg-primary shadow-sm' 
                  : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
              }`}
              onClick={() => setCurrentPage(index)}
            >
              <span className="sr-only">Page {index + 1}</span>
            </Button>
          ))}
        </div>
      )}
    </div>
  );
};
