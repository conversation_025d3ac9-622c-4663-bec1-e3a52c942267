
import React, { useState } from 'react';
import { BottomNavBar } from './mobile/BottomNavBar';
import { Sheet, SheetTrigger, SheetContent } from '@/components/ui/sheet';
import { Drawer, DrawerTrigger, DrawerContent } from '@/components/ui/drawer';
import { MobileServicesDrawer } from './mobile/MobileServicesDrawer';
import { useIsMobile } from '@/hooks/use-mobile';

export const MobileNavigation = () => {
  const isMobile = useIsMobile();
  const [isServicesOpen, setIsServicesOpen] = useState(false);

  // We provide access to the drawer state to be used in the bottom nav
  return (
    <>
      <BottomNavBar onExploreClick={() => setIsServicesOpen(true)} />
      
      {isMobile ? (
        <Drawer open={isServicesOpen} onOpenChange={setIsServicesOpen}>
          <DrawerContent>
            <MobileServicesDrawer onClose={() => setIsServicesOpen(false)} />
          </DrawerContent>
        </Drawer>
      ) : (
        <Sheet open={isServicesOpen} onOpenChange={setIsServicesOpen}>
          <SheetContent>
            <MobileServicesDrawer onClose={() => setIsServicesOpen(false)} />
          </SheetContent>
        </Sheet>
      )}
    </>
  );
};
