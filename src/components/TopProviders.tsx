
import React, { useState } from 'react';
import { ProviderCard, Provider } from './ProviderCard';
import { Separator } from './ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { 
  Pagination,
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious,
  PaginationEllipsis
} from './ui/pagination';
import { ChevronLeft, ChevronRight, Crown } from 'lucide-react';

interface TopProvidersProps {
  serviceId: string;
  providers: Provider[];
}

export const TopProviders: React.FC<TopProvidersProps> = ({ serviceId, providers }) => {
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const providersPerPage = 3;
  
  // Add isFeatured property to some providers (in a real app, this would come from the database)
  const enhancedProviders = providers.map((provider, index) => ({
    ...provider,
    isFeatured: index % 3 === 0 // For demonstration, make every 3rd provider featured
  }));
  
  // Filter providers for different categories
  const allProviders = enhancedProviders;
  const verifiedProviders = enhancedProviders.filter(p => p.badges.includes('verified'));
  const fastResponseProviders = enhancedProviders.filter(p => p.badges.includes('fastResponse'));
  const topRatedProviders = enhancedProviders.filter(p => p.badges.includes('topRated'));
  const mostHiredProviders = enhancedProviders.filter(p => p.badges.includes('mostHired'));
  
  // Sort providers to show featured ones first within each category
  const sortProvidersByFeatured = (providerList: (Provider & { isFeatured?: boolean })[]) => {
    return [...providerList].sort((a, b) => (b.isFeatured ? 1 : 0) - (a.isFeatured ? 1 : 0));
  };

  // Calculate pagination for active tab
  const getPagedProviders = (providerList: (Provider & { isFeatured?: boolean })[]) => {
    const sortedProviders = sortProvidersByFeatured(providerList);
    const indexOfLastProvider = currentPage * providersPerPage;
    const indexOfFirstProvider = indexOfLastProvider - providersPerPage;
    return sortedProviders.slice(indexOfFirstProvider, indexOfLastProvider);
  };

  // Get total pages for current tab's providers
  const [activeTab, setActiveTab] = useState<string>("all");
  
  const getTotalPages = (providerList: Provider[]) => {
    return Math.ceil(providerList.length / providersPerPage);
  };

  const getCurrentProviders = () => {
    switch (activeTab) {
      case 'topRated':
        return getPagedProviders(topRatedProviders);
      case 'fastResponse':
        return getPagedProviders(fastResponseProviders);
      case 'mostHired':
        return getPagedProviders(mostHiredProviders);
      case 'all':
      default:
        return getPagedProviders(allProviders);
    }
  };

  const getCurrentTotalPages = () => {
    switch (activeTab) {
      case 'topRated':
        return getTotalPages(topRatedProviders);
      case 'fastResponse':
        return getTotalPages(fastResponseProviders);
      case 'mostHired':
        return getTotalPages(mostHiredProviders);
      case 'all':
      default:
        return getTotalPages(allProviders);
    }
  };
  
  const getCurrentTotalItems = () => {
    switch (activeTab) {
      case 'topRated':
        return topRatedProviders.length;
      case 'fastResponse':
        return fastResponseProviders.length;
      case 'mostHired':
        return mostHiredProviders.length;
      case 'all':
      default:
        return allProviders.length;
    }
  };

  // Handle tab change - reset to page 1
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setCurrentPage(1);
  };

  return (
    <div className="space-y-8">
      <Tabs defaultValue="all" onValueChange={handleTabChange}>
        <TabsList className="bg-gray-50 w-full rounded-md">
          <TabsTrigger value="all">All Professionals</TabsTrigger>
          <TabsTrigger value="topRated">Top Rated</TabsTrigger>
          <TabsTrigger value="fastResponse">Fast Response</TabsTrigger>
          <TabsTrigger value="mostHired">Most Hired</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="mt-6">
          <div className="grid grid-cols-1 gap-0">
            {getCurrentProviders().map((provider, index) => (
              <div key={provider.id} className={provider.isFeatured ? "relative" : ""}>
                {provider.isFeatured && (
                  <div className="absolute -top-3 left-0 right-0 flex justify-center z-10">
                    <span className="bg-amber-400 text-amber-900 px-4 py-1 rounded-full text-sm font-semibold flex items-center shadow-md">
                      <Crown className="h-4 w-4 mr-1.5" />
                      Featured Provider
                    </span>
                  </div>
                )}
                <ProviderCard 
                  key={provider.id} 
                  provider={provider} 
                  jobsCompleted={80 + (index * 10)}
                  distance={`${(1 + (index * 0.4) % 5).toFixed(1)} miles away`}
                  listView={true}
                  className={provider.isFeatured ? "mt-4 border-2 border-amber-300 shadow-lg shadow-amber-100" : ""}
                />
              </div>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="topRated" className="mt-6">
          <div className="grid grid-cols-1 gap-0">
            {getCurrentProviders().map((provider, index) => (
              <div key={provider.id} className={provider.isFeatured ? "relative" : ""}>
                {provider.isFeatured && (
                  <div className="absolute -top-3 left-0 right-0 flex justify-center z-10">
                    <span className="bg-amber-400 text-amber-900 px-4 py-1 rounded-full text-sm font-semibold flex items-center shadow-md">
                      <Crown className="h-4 w-4 mr-1.5" />
                      Featured Provider
                    </span>
                  </div>
                )}
                <ProviderCard 
                  key={provider.id} 
                  provider={provider} 
                  badgeType="topRated" 
                  jobsCompleted={90 + (index * 8)}
                  distance={`${(1.2 + (index * 0.6) % 5).toFixed(1)} miles away`}
                  listView={true}
                  className={provider.isFeatured ? "mt-4 border-2 border-amber-300 shadow-lg shadow-amber-100" : ""}
                />
              </div>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="fastResponse" className="mt-6">
          <div className="grid grid-cols-1 gap-0">
            {getCurrentProviders().map((provider, index) => (
              <div key={provider.id} className={provider.isFeatured ? "relative" : ""}>
                {provider.isFeatured && (
                  <div className="absolute -top-3 left-0 right-0 flex justify-center z-10">
                    <span className="bg-amber-400 text-amber-900 px-4 py-1 rounded-full text-sm font-semibold flex items-center shadow-md">
                      <Crown className="h-4 w-4 mr-1.5" />
                      Featured Provider
                    </span>
                  </div>
                )}
                <ProviderCard 
                  key={provider.id} 
                  provider={provider} 
                  badgeType="fastResponse" 
                  jobsCompleted={70 + (index * 12)}
                  distance={`${(1.5 + (index * 0.5) % 5).toFixed(1)} miles away`}
                  listView={true}
                  className={provider.isFeatured ? "mt-4 border-2 border-amber-300 shadow-lg shadow-amber-100" : ""}
                />
              </div>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="mostHired" className="mt-6">
          <div className="grid grid-cols-1 gap-0">
            {getCurrentProviders().map((provider, index) => (
              <div key={provider.id} className={provider.isFeatured ? "relative" : ""}>
                {provider.isFeatured && (
                  <div className="absolute -top-3 left-0 right-0 flex justify-center z-10">
                    <span className="bg-amber-400 text-amber-900 px-4 py-1 rounded-full text-sm font-semibold flex items-center shadow-md">
                      <Crown className="h-4 w-4 mr-1.5" />
                      Featured Provider
                    </span>
                  </div>
                )}
                <ProviderCard 
                  key={provider.id} 
                  provider={provider} 
                  badgeType="mostHired" 
                  jobsCompleted={110 + (index * 15)}
                  distance={`${(1.8 + (index * 0.3) % 5).toFixed(1)} miles away`}
                  listView={true}
                  className={provider.isFeatured ? "mt-4 border-2 border-amber-300 shadow-lg shadow-amber-100" : ""}
                />
              </div>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Modern, sleek pagination */}
      {getCurrentTotalPages() > 1 && (
        <div className="mt-8">
          <Pagination
            totalItems={getCurrentTotalItems()}
            itemsPerPage={providersPerPage}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          >
            <PaginationContent className="flex items-center gap-2">
              {/* The pagination content is handled by the Pagination component */}
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
};
