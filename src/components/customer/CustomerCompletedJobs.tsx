
import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>itle 
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileText, Star, ArrowRight } from "lucide-react";
import { ReviewFormDialog } from './job-management/ReviewFormDialog';
import { JobSummaryDialog } from './job-management/JobSummaryDialog';

export function CustomerCompletedJobs() {
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [isSummaryDialogOpen, setIsSummaryDialogOpen] = useState(false);

  // Mock data for completed jobs
  const completedJobs = [
    {
      id: "job1",
      title: "Garage Door Repair",
      provider: "JJ's Garage Services",
      completionDate: "Mar 25, 2025",
      amount: "$350.00",
      reviewed: true,
      description: "Fixed garage door spring and lubricated all moving parts. Adjusted door balance and checked safety sensors.",
      location: "123 Main St"
    },
    {
      id: "job2",
      title: "Lawn Mowing and Trimming",
      provider: "Green Thumb Landscaping",
      completionDate: "Apr 2, 2025",
      amount: "$85.00",
      reviewed: false,
      description: "Full lawn service including mowing, edging, and cleanup of all clippings from walkways.",
      location: "123 Main St"
    },
    {
      id: "job3",
      title: "Water Heater Installation",
      provider: "Mike's Plumbing",
      completionDate: "Apr 8, 2025",
      amount: "$650.00",
      reviewed: true,
      description: "Removed old water heater and installed new 50-gallon tank. Connected to existing plumbing and tested all connections.",
      location: "123 Main St"
    }
  ];

  const handleReviewClick = (job: any) => {
    setSelectedJob(job);
    setIsReviewDialogOpen(true);
  };

  const handleViewSummary = (job: any) => {
    setSelectedJob(job);
    setIsSummaryDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Completed Jobs</h1>
          <p className="text-muted-foreground">View your finished service requests</p>
        </div>
      </div>

      {completedJobs.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="py-8 text-center">
            <div className="flex justify-center mb-4">
              <FileText className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium">No completed jobs</h3>
            <p className="text-sm text-muted-foreground mb-4">
              You don't have any completed jobs yet. Jobs will appear here once providers mark them as complete.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {completedJobs.map(job => (
            <Card key={job.id} className="flex flex-col">
              <CardHeader className="pb-2">
                <CardTitle className="text-xl">{job.title}</CardTitle>
              </CardHeader>
              <CardContent className="flex-grow">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Provider:</span>
                    <span className="text-sm font-medium">{job.provider}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Completed:</span>
                    <span className="text-sm font-medium">{job.completionDate}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Amount:</span>
                    <span className="text-sm font-medium">{job.amount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Review:</span>
                    <span className="text-sm font-medium flex items-center">
                      {job.reviewed ? (
                        <>
                          <Star className="h-4 w-4 text-amber-500 mr-1" fill="currentColor" /> 
                          Submitted
                        </>
                      ) : (
                        <span className="text-red-500">Pending</span>
                      )}
                    </span>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  variant={job.reviewed ? "outline" : "default"} 
                  className="w-full justify-between"
                  onClick={() => job.reviewed ? handleViewSummary(job) : handleReviewClick(job)}
                >
                  {job.reviewed ? "View Job Summary" : "Leave a Review"}
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Dialogs */}
      {selectedJob && (
        <>
          <ReviewFormDialog 
            isOpen={isReviewDialogOpen}
            onClose={() => setIsReviewDialogOpen(false)}
            job={selectedJob}
          />
          
          <JobSummaryDialog
            isOpen={isSummaryDialogOpen}
            onClose={() => setIsSummaryDialogOpen(false)}
            job={selectedJob}
          />
        </>
      )}
    </div>
  );
}
