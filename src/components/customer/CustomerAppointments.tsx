
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, MapPin, User, MessageSquare, Phone } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export function CustomerAppointments() {
  const { toast } = useToast();

  const handleMessage = (providerName: string) => {
    toast({
      title: "Opening message",
      description: `Starting conversation with ${providerName}`,
    });
  };

  const handleCall = (providerName: string) => {
    toast({
      title: "Calling provider",
      description: `Initiating call to ${providerName}`,
    });
  };

  return (
    <div className="space-y-6">
      <Card className="border-0 shadow-lg bg-white">
        <CardHeader className="pb-4 bg-blue-600 text-white rounded-t-lg">
          <CardTitle className="flex items-center gap-2 text-white">
            <Calendar className="h-5 w-5" />
            Upcoming Appointments
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y divide-gray-100">
            {/* Bathroom Renovation */}
            <div className="p-6 hover:bg-blue-50 transition-colors border-l-4 border-blue-500">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-gray-900 text-lg">Bathroom Renovation</h3>
                    <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                      Confirmed
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Calendar className="h-4 w-4 text-blue-500" />
                      <span className="font-medium">April 25, 2025</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <Clock className="h-4 w-4 text-green-500" />
                      <span>11:00 AM</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <User className="h-4 w-4 text-purple-500" />
                      <span>John's Renovation Services</span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
                    onClick={() => handleMessage("John's Renovation Services")}
                  >
                    <MessageSquare className="h-4 w-4" />
                    Message
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center gap-2 hover:bg-green-50 hover:border-green-300"
                    onClick={() => handleCall("John's Renovation Services")}
                  >
                    <Phone className="h-4 w-4" />
                    Call
                  </Button>
                </div>
              </div>
            </div>

            {/* Kitchen Plumbing */}
            <div className="p-6 hover:bg-yellow-50 transition-colors border-l-4 border-yellow-500">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-gray-900 text-lg">Kitchen Plumbing</h3>
                    <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200">
                      Pending
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Calendar className="h-4 w-4 text-blue-500" />
                      <span className="font-medium">April 29, 2025</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <Clock className="h-4 w-4 text-green-500" />
                      <span>2:00 PM</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <User className="h-4 w-4 text-purple-500" />
                      <span>Pro Plumbing Solutions</span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
                    onClick={() => handleMessage("Pro Plumbing Solutions")}
                  >
                    <MessageSquare className="h-4 w-4" />
                    Message
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center gap-2 hover:bg-green-50 hover:border-green-300"
                    onClick={() => handleCall("Pro Plumbing Solutions")}
                  >
                    <Phone className="h-4 w-4" />
                    Call
                  </Button>
                </div>
              </div>
            </div>

            {/* Electrical Inspection */}
            <div className="p-6 hover:bg-gray-50 transition-colors border-l-4 border-gray-500">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-gray-900 text-lg">Electrical Inspection</h3>
                    <Badge className="bg-gray-100 text-gray-700 border-gray-200">
                      Scheduled
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Calendar className="h-4 w-4 text-blue-500" />
                      <span className="font-medium">May 4, 2025</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <Clock className="h-4 w-4 text-green-500" />
                      <span>9:00 AM</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <User className="h-4 w-4 text-purple-500" />
                      <span>Elite Electrical Services</span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
                    onClick={() => handleMessage("Elite Electrical Services")}
                  >
                    <MessageSquare className="h-4 w-4" />
                    Message
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center gap-2 hover:bg-green-50 hover:border-green-300"
                    onClick={() => handleCall("Elite Electrical Services")}
                  >
                    <Phone className="h-4 w-4" />
                    Call
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
