
import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, PlusCircle } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileCustomerReviews } from "./MobileCustomerReviews";

interface Review {
  id: string;
  provider: {
    name: string;
    avatar?: string;
    initials: string;
  };
  jobTitle: string;
  date: string;
  rating: number;
  comment: string;
}

interface PendingReview {
  id: string;
  provider: {
    name: string;
    avatar?: string;
    initials: string;
  };
  jobTitle: string;
  completionDate: string;
}

export function CustomerReviews() {
  const isMobile = useIsMobile();
  
  // If on a mobile device, show the mobile-optimized version
  if (isMobile) {
    return <MobileCustomerReviews />;
  }
  
  // Mock reviews
  const reviews: Review[] = [
    {
      id: "rev1",
      provider: {
        name: "Green Thumb Landscaping",
        avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=1964&auto=format&fit=crop",
        initials: "GT"
      },
      jobTitle: "Lawn Maintenance",
      date: "Apr 10, 2025",
      rating: 5,
      comment: "Excellent service! They were on time, professional, and left my yard looking amazing. I'll definitely hire them again for future landscaping needs."
    },
    {
      id: "rev2",
      provider: {
        name: "Mike's Plumbing",
        initials: "MP"
      },
      jobTitle: "Bathroom Sink Repair",
      date: "Mar 28, 2025",
      rating: 4,
      comment: "Good service overall. Fixed the issue quickly, though arrived a bit later than scheduled. Fair pricing and professional demeanor."
    },
    {
      id: "rev3",
      provider: {
        name: "Elite Electricians",
        initials: "EE"
      },
      jobTitle: "Lighting Installation",
      date: "Mar 15, 2025",
      rating: 3,
      comment: "Average service. The work was completed correctly, but they left a mess that I had to clean up afterward. Communication could be improved."
    }
  ];
  
  // Mock pending reviews
  const pendingReviews: PendingReview[] = [
    {
      id: "pending1",
      provider: {
        name: "Deluxe Renovations",
        initials: "DR"
      },
      jobTitle: "Kitchen Cabinet Installation",
      completionDate: "Apr 18, 2025"
    }
  ];
  
  // Generate stars based on rating
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <Star 
        key={index} 
        className={`h-4 w-4 ${index < rating ? 'text-amber-500 fill-amber-500' : 'text-gray-300'}`} 
      />
    ));
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl md:text-3xl font-bold">Reviews</h1>
      <p className="text-muted-foreground">Manage your service provider reviews</p>
      
      {pendingReviews.length > 0 && (
        <Card className="border-amber-200 bg-amber-50">
          <CardHeader>
            <CardTitle className="text-amber-800">Pending Reviews</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pendingReviews.map(review => (
                <div key={review.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      {review.provider.avatar && (
                        <AvatarImage src={review.provider.avatar} alt={review.provider.name} />
                      )}
                      <AvatarFallback>{review.provider.initials}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h4 className="font-medium">{review.provider.name}</h4>
                      <p className="text-sm text-muted-foreground">{review.jobTitle}</p>
                      <p className="text-xs text-muted-foreground">Completed on {review.completionDate}</p>
                    </div>
                  </div>
                  <Button>
                    <PlusCircle className="h-4 w-4 mr-2" /> Leave Review
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {reviews.map(review => (
          <Card key={review.id}>
            <CardHeader className="pb-2">
              <div className="flex items-center gap-3">
                <Avatar>
                  {review.provider.avatar && (
                    <AvatarImage src={review.provider.avatar} alt={review.provider.name} />
                  )}
                  <AvatarFallback>{review.provider.initials}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-base">{review.provider.name}</CardTitle>
                  <p className="text-sm text-muted-foreground">{review.jobTitle}</p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2 mb-2">
                <div className="flex">{renderStars(review.rating)}</div>
                <span className="text-sm text-muted-foreground">• {review.date}</span>
              </div>
              <p className="text-sm">{review.comment}</p>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {reviews.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="py-8 text-center">
            <div className="flex justify-center mb-4">
              <Star className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium">No reviews yet</h3>
            <p className="text-sm text-muted-foreground mb-4">
              You haven't left any reviews for providers yet. Reviews help the community find great service providers.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
