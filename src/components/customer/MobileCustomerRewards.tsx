
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Award, Gift, Star, Trophy, Shield } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

export function MobileCustomerRewards() {
  const { toast } = useToast();
  // Mock data - In real app, this would come from user context/API
  const hasCompletedFirstJob = true;
  const [totalJobsCompleted, setTotalJobsCompleted] = React.useState<number>(2);
  const walletBalance = 100; // This would be $100 from first job reward
  const [showRewardDialog, setShowRewardDialog] = React.useState(false);
  const [rewardDialogContent, setRewardDialogContent] = React.useState({
    title: '',
    description: '',
    amount: 0
  });

  // Badge definitions - same as desktop
  const badges = [
    {
      id: 'first-hire',
      name: 'First Hire',
      icon: Award,
      description: 'Earned after completing your first job on JobOn',
      requirement: 1,
      reward: 100
    },
    {
      id: 'loyal-customer',
      name: 'Loyal Customer',
      icon: Trophy,
      description: 'Earned after completing three jobs on JobOn',
      requirement: 3,
      reward: 150
    },
    {
      id: 'power-user',
      name: 'Power User',
      icon: Star,
      description: 'Earned after completing ten jobs on JobOn',
      requirement: 10,
      reward: 200
    }
  ];

  // Helper function to check if a badge is unlocked
  const isBadgeUnlocked = (requiredJobs: number) => {
    return totalJobsCompleted >= requiredJobs;
  };

  // Same useEffect for reward notifications
  React.useEffect(() => {
    // Use explicit number comparisons
    if (totalJobsCompleted === 1) {
      setRewardDialogContent({
        title: "First Hire Badge Earned!",
        description: "🎉 Congratulations! You've earned $100 JobON Credit for completing your first service!",
        amount: 100
      });
      setShowRewardDialog(true);
    } else if (totalJobsCompleted === 3) {
      setRewardDialogContent({
        title: "Loyal Customer Badge Earned!",
        description: "🏆 Amazing! You've completed 3 jobs and earned an additional $150 JobON Credit!",
        amount: 150
      });
      setShowRewardDialog(true);
    }
  }, [totalJobsCompleted]);

  // Calculate progress for the next badge
  const getNextBadgeProgress = () => {
    if (totalJobsCompleted < 3) {
      return {
        current: totalJobsCompleted,
        target: 3,
        name: "Loyal Customer",
        reward: 150,
        progress: (totalJobsCompleted / 3) * 100
      };
    } else if (totalJobsCompleted < 10) {
      return {
        current: totalJobsCompleted,
        target: 10,
        name: "Power User",
        reward: 200,
        progress: (totalJobsCompleted / 10) * 100
      };
    }
    return null;
  };

  const nextBadge = getNextBadgeProgress();

  return (
    <div className="p-4 space-y-4 pb-16">
      {/* Wallet Balance Card - Highlighted at the top */}
      <Card className="border border-primary/20 bg-primary/5 shadow-sm">
        <CardContent className="p-4 flex items-center justify-between">
          <div className="flex flex-col">
            <span className="text-sm text-muted-foreground">Your Balance</span>
            <span className="text-3xl font-bold">${walletBalance}</span>
          </div>
          <div className="bg-primary/10 p-3 rounded-full">
            <Gift className="h-6 w-6 text-primary" />
          </div>
        </CardContent>
      </Card>
      
      {/* Info Alert */}
      <Alert className="bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-900 p-3">
        <Shield className="h-4 w-4 text-blue-500" />
        <AlertTitle className="text-sm">Earn JobON Credits!</AlertTitle>
        <AlertDescription className="text-xs">
          Complete jobs and unlock rewards that apply automatically to future bookings.
        </AlertDescription>
      </Alert>

      {/* Next Badge Progress */}
      {nextBadge && (
        <Card className="border-dashed border-primary/50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-2">
              <div className="bg-primary/10 p-2 rounded-full">
                <Gift className="h-4 w-4 text-primary" />
              </div>
              <div className="font-medium text-sm">Next Reward Progress</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm">
                Complete {nextBadge.target - nextBadge.current} more jobs to earn ${nextBadge.reward}
              </div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>{nextBadge.current}</span>
                <Progress value={nextBadge.progress} className="h-2 flex-1" />
                <span>{nextBadge.target}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Badges Section */}
      <div className="pt-2">
        <h3 className="text-base font-medium mb-3 px-1">Your Achievement Badges</h3>
        <div className="space-y-3">
          {badges.map((badge) => {
            const unlocked = isBadgeUnlocked(badge.requirement);
            return (
              <Card 
                key={badge.id} 
                className={`overflow-hidden ${unlocked ? 'shadow-sm' : 'opacity-70'}`}
              >
                <div className={`h-1 ${unlocked ? 'bg-green-500' : 'bg-gray-200'}`} />
                <CardContent className="p-3 flex items-center gap-3">
                  <Avatar className={`size-12 ${unlocked ? 'bg-primary/10' : 'bg-gray-100'}`}>
                    <AvatarFallback className={unlocked ? 'text-primary' : 'text-gray-400'}>
                      <badge.icon className="h-5 w-5" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex justify-between items-center">
                      <h4 className="text-sm font-medium">{badge.name}</h4>
                      {unlocked ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100 text-xs">
                          Earned
                        </Badge>
                      ) : (
                        <Badge className="bg-gray-100 text-gray-600 hover:bg-gray-100 text-xs">
                          {badge.requirement} Jobs
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {badge.description}
                    </p>
                    {unlocked && (
                      <div className="text-xs mt-1 text-green-600">
                        +${badge.reward} reward earned
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
      
      {/* First Service Bonus Card (only if not completed) */}
      {!hasCompletedFirstJob && (
        <Card className="mt-4">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-2">
              <div className="bg-primary/10 p-2 rounded-full">
                <Award className="h-4 w-4 text-primary" />
              </div>
              <h3 className="text-sm font-medium">First Service Bonus</h3>
            </div>
            <p className="text-sm">
              Complete your first service and earn $100 in JobON Credit!
            </p>
            <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg mt-2">
              <p className="text-xs font-medium">Progress: 0/1 Jobs Completed</p>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div className="bg-primary h-2 rounded-full w-0"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reward Popup Modal - Same as desktop */}
      <Dialog open={showRewardDialog} onOpenChange={setShowRewardDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl">{rewardDialogContent.title}</DialogTitle>
            <DialogDescription className="text-center">
              {rewardDialogContent.description}
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col items-center py-6">
            <div className="bg-primary/10 p-4 rounded-full mb-4">
              <Gift className="h-12 w-12 text-primary" />
            </div>
            <p className="text-3xl font-bold mb-2">${rewardDialogContent.amount}</p>
            <p className="text-center text-muted-foreground">
              Added to your wallet balance and will apply to your next booking!
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
