
import React from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "@/components/ui/carousel";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText, Star, Wallet, Users } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useSwipeGesture } from "@/hooks/use-swipe-gesture";
import Autoplay from 'embla-carousel-autoplay';

export function GetStartedSection() {
  const navigate = useNavigate();
  const { toast } = useToast();

  const tips = [
    {
      icon: <FileText className="h-8 w-8 text-primary" />,
      title: "Post Your First Job",
      description: "Need help at home or work? Post a job and get quotes fast from trusted pros!"
    },
    {
      icon: <Star className="h-8 w-8 text-amber-500" />,
      title: "Check Provider Reviews",
      description: "Browse real reviews and ratings before you choose a service provider."
    },
    {
      icon: <Wallet className="h-8 w-8 text-green-500" />,
      title: "Use Your Wallet Credits",
      description: "Earn credits through rewards and referrals. Apply them to your next service!"
    },
    {
      icon: <Users className="h-8 w-8 text-blue-500" />,
      title: "Invite Friends, Get $50",
      description: "Invite friends to JobON — when they book, you both get $50 credits!"
    },
    {
      icon: <Star className="h-8 w-8 text-purple-500" />,
      title: "Leave a Review",
      description: "Help the community! Leave a review after your service and earn bonuses."
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Get Started on JobON 🚀</h2>
        <p className="text-muted-foreground mt-1">
          Here are quick ways to make the most out of your JobON experience!
        </p>
      </div>

      <Carousel
        opts={{
          align: "start",
          loop: true,
        }}
        plugins={[
          Autoplay({
            delay: 5000,
          }),
        ]}
        className="w-full"
      >
        <CarouselContent>
          {tips.map((tip, index) => (
            <CarouselItem key={index} className="md:basis-full">
              <Card className="border-2 border-primary/10">
                <CardContent className="flex flex-col items-center gap-4 p-6">
                  <div className="rounded-full bg-primary/10 p-4">
                    {tip.icon}
                  </div>
                  <div className="text-center">
                    <h3 className="font-semibold text-lg mb-2">{tip.title}</h3>
                    <p className="text-muted-foreground">{tip.description}</p>
                  </div>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
        <div className="flex items-center justify-center gap-2 mt-4">
          <CarouselPrevious />
          <CarouselNext />
        </div>
      </Carousel>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Button 
          onClick={() => navigate('/post-job')} 
          className="w-full"
          size="lg"
        >
          <FileText className="mr-2 h-4 w-4" />
          Post a Job
        </Button>
        <Button 
          onClick={() => navigate('/customer/dashboard?tab=payments')} 
          variant="secondary"
          className="w-full"
          size="lg"
        >
          <Wallet className="mr-2 h-4 w-4" />
          View My Wallet
        </Button>
        <Button 
          onClick={() => navigate('/customer/dashboard?tab=referrals')} 
          variant="outline"
          className="w-full"
          size="lg"
        >
          <Users className="mr-2 h-4 w-4" />
          Invite Friends
        </Button>
      </div>
    </div>
  );
}
