
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { CardIcon } from "@/components/ui/card-icon";
import { Progress } from "@/components/ui/progress";
import { Award, Gift, Star, Trophy, Shield } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileCustomerRewards } from './MobileCustomerRewards';

export function CustomerRewards() {
  const isMobile = useIsMobile();
  
  // If on a mobile device, show the mobile-optimized version
  if (isMobile) {
    return <MobileCustomerRewards />;
  }
  
  const { toast } = useToast();
  // Mock data - In real app, this would come from user context/API
  const hasCompletedFirstJob = true;
  const [totalJobsCompleted, setTotalJobsCompleted] = React.useState<number>(2);
  const walletBalance = 100; // This would be $100 from first job reward
  const [showRewardDialog, setShowRewardDialog] = React.useState(false);
  const [rewardDialogContent, setRewardDialogContent] = React.useState({
    title: '',
    description: '',
    amount: 0
  });

  // Badge definitions
  const badges = [
    {
      id: 'first-hire',
      name: 'First Hire',
      icon: Award,
      description: 'Earned after completing your first job on JobOn',
      requirement: 1,
      reward: 100
    },
    {
      id: 'loyal-customer',
      name: 'Loyal Customer',
      icon: Trophy,
      description: 'Earned after completing three jobs on JobOn',
      requirement: 3,
      reward: 150
    },
    {
      id: 'power-user',
      name: 'Power User',
      icon: Star,
      description: 'Earned after completing ten jobs on JobOn',
      requirement: 10,
      reward: 200
    }
  ];

  // Helper function to check if a badge is unlocked
  const isBadgeUnlocked = (requiredJobs: number) => {
    return totalJobsCompleted >= requiredJobs;
  };

  // Simulate showing notification for newly earned badge
  React.useEffect(() => {
    // Use explicit number comparisons
    if (totalJobsCompleted === 1) {
      setRewardDialogContent({
        title: "First Hire Badge Earned!",
        description: "🎉 Congratulations! You've earned $100 JobON Credit for completing your first service!",
        amount: 100
      });
      setShowRewardDialog(true);
    } else if (totalJobsCompleted === 3) {
      setRewardDialogContent({
        title: "Loyal Customer Badge Earned!",
        description: "🏆 Amazing! You've completed 3 jobs and earned an additional $150 JobON Credit!",
        amount: 150
      });
      setShowRewardDialog(true);
    }
  }, [totalJobsCompleted]);

  // Calculate progress for the next badge
  const getNextBadgeProgress = () => {
    if (totalJobsCompleted < 3) {
      return {
        current: totalJobsCompleted,
        target: 3,
        name: "Loyal Customer",
        reward: 150,
        progress: (totalJobsCompleted / 3) * 100
      };
    } else if (totalJobsCompleted < 10) {
      return {
        current: totalJobsCompleted,
        target: 10,
        name: "Power User",
        reward: 200,
        progress: (totalJobsCompleted / 10) * 100
      };
    }
    return null;
  };

  const nextBadge = getNextBadgeProgress();

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Rewards</h1>
      
      <Alert className="bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-900">
        <Shield className="h-4 w-4 text-blue-500" />
        <AlertTitle>Earn JobON Credits!</AlertTitle>
        <AlertDescription>
          Complete jobs and unlock rewards. Your credits will automatically be applied to your future bookings.
        </AlertDescription>
      </Alert>

      {nextBadge && (
        <Card className="border-dashed border-primary/50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <CardIcon color="bg-primary/10">
                <Gift className="h-5 w-5 text-primary" />
              </CardIcon>
              <div className="flex-1 space-y-1">
                <h3 className="font-medium">
                  🎁 Complete {nextBadge.target} jobs to earn ${nextBadge.reward} extra! 
                  You've completed {nextBadge.current}/{nextBadge.target} so far!
                </h3>
                <Progress value={nextBadge.progress} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
        {/* Badges Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-primary" />
              My Achievement Badges
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {badges.map((badge) => {
                const unlocked = isBadgeUnlocked(badge.requirement);
                return (
                  <div 
                    key={badge.id} 
                    className={`flex flex-col items-center p-4 rounded-lg border ${
                      unlocked ? 'bg-primary/5 border-primary/20' : 'bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                    }`}
                  >
                    <div className={`p-3 rounded-full mb-2 ${
                      unlocked ? 'bg-primary/10' : 'bg-gray-200 dark:bg-gray-700'
                    }`}>
                      <badge.icon className={`h-6 w-6 ${
                        unlocked ? 'text-primary' : 'text-gray-400 dark:text-gray-500'
                      }`} />
                    </div>
                    <h3 className={`font-medium text-center mb-1 ${
                      unlocked ? '' : 'text-gray-400 dark:text-gray-500'
                    }`}>{badge.name}</h3>
                    <p className="text-xs text-center text-muted-foreground">
                      {badge.description}
                    </p>
                    {unlocked && (
                      <Badge className="mt-2 bg-green-100 text-green-800 hover:bg-green-100">
                        Unlocked
                      </Badge>
                    )}
                    {!unlocked && (
                      <Badge className="mt-2 bg-gray-100 text-gray-500 hover:bg-gray-100">
                        {badge.requirement} Jobs Required
                      </Badge>
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Job Rewards Progress Cards */}
        <div className="grid gap-6 grid-cols-1">
          {/* First Job Bonus Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-primary" />
                First Service Bonus
              </CardTitle>
            </CardHeader>
            <CardContent>
              {!hasCompletedFirstJob ? (
                <div className="space-y-4">
                  <p className="text-lg font-medium">
                    🎯 Complete your first service and earn $100 in JobON Credit!
                  </p>
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <p className="text-sm font-medium">Progress: 0/1 Jobs Completed</p>
                    <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                      <div className="bg-primary h-2.5 rounded-full w-0"></div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-lg font-medium text-green-600 dark:text-green-500">
                    ✅ You've earned $100 JobON Credit!
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Your credit will be automatically applied at checkout.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Wallet Balance Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gift className="h-5 w-5 text-primary" />
                Wallet Balance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-3xl font-bold">${walletBalance}</p>
                <p className="text-sm text-muted-foreground">
                  Your credits will automatically apply to your next service booking.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Reward Popup Modal */}
      <Dialog open={showRewardDialog} onOpenChange={setShowRewardDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl">{rewardDialogContent.title}</DialogTitle>
            <DialogDescription className="text-center">
              {rewardDialogContent.description}
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col items-center py-6">
            <div className="bg-primary/10 p-4 rounded-full mb-4">
              <Gift className="h-12 w-12 text-primary" />
            </div>
            <p className="text-3xl font-bold mb-2">${rewardDialogContent.amount}</p>
            <p className="text-center text-muted-foreground">
              Added to your wallet balance and will apply to your next booking!
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
