
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Filter, Star, Calendar, ArrowRight, FileCheck, Check } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetFooter,
  Sheet<PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from '@/hooks/use-toast';
import { ReviewFormDialog } from './ReviewFormDialog';
import { JobSummaryDialog } from './JobSummaryDialog';

export function MobileCompletedJobs() {
  const [filterOpen, setFilterOpen] = useState(false);
  const [timeFilter, setTimeFilter] = useState("all");
  const [reviewFilter, setReviewFilter] = useState("all");
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [isSummaryDialogOpen, setIsSummaryDialogOpen] = useState(false);
  const { toast } = useToast();

  // Mock data for completed jobs
  const completedJobs = [
    {
      id: "job1",
      title: "Garage Door Repair",
      provider: "JJ's Garage Services",
      completionDate: "Mar 25, 2025",
      amount: "$350.00",
      reviewed: true,
      description: "Fixed garage door spring and lubricated all moving parts. Adjusted door balance and checked safety sensors.",
      location: "123 Main St"
    },
    {
      id: "job2",
      title: "Lawn Mowing and Trimming",
      provider: "Green Thumb Landscaping",
      completionDate: "Apr 2, 2025",
      amount: "$85.00",
      reviewed: false,
      description: "Full lawn service including mowing, edging, and cleanup of all clippings from walkways.",
      location: "123 Main St"
    },
    {
      id: "job3",
      title: "Water Heater Installation",
      provider: "Mike's Plumbing",
      completionDate: "Apr 8, 2025",
      amount: "$650.00",
      reviewed: true,
      description: "Removed old water heater and installed new 50-gallon tank. Connected to existing plumbing and tested all connections.",
      location: "123 Main St"
    }
  ];

  // Apply filters to jobs
  const filteredJobs = completedJobs.filter(job => {
    // Apply time filter logic (in a real app, this would use actual date comparisons)
    if (timeFilter !== "all") {
      const currentDate = new Date();
      const jobDate = new Date(job.completionDate);
      
      if (timeFilter === "last-30-days" && 
          (currentDate.getTime() - jobDate.getTime() > 30 * 24 * 60 * 60 * 1000)) {
        return false;
      }
      
      if (timeFilter === "last-90-days" && 
          (currentDate.getTime() - jobDate.getTime() > 90 * 24 * 60 * 60 * 1000)) {
        return false;
      }
    }
    
    // Apply review filter
    if (reviewFilter === "reviewed" && !job.reviewed) {
      return false;
    }
    
    if (reviewFilter === "not-reviewed" && job.reviewed) {
      return false;
    }
    
    return true;
  });

  const handleViewJobDetails = (job: any) => {
    setSelectedJob(job);
    setIsSummaryDialogOpen(true);
  };

  const handleWriteReview = (job: any) => {
    setSelectedJob(job);
    setIsReviewDialogOpen(true);
  };

  return (
    <div className="p-4 pb-16">
      {/* Header with filter */}
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-xl font-bold">Completed Jobs</h1>
        <Sheet open={filterOpen} onOpenChange={setFilterOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="h-9 w-9">
              <Filter className="h-4 w-4" />
            </Button>
          </SheetTrigger>
          <SheetContent side="right">
            <SheetHeader>
              <SheetTitle>Filter Completed Jobs</SheetTitle>
              <SheetDescription>
                Filter your completed jobs by time period or review status.
              </SheetDescription>
            </SheetHeader>
            
            <div className="py-6 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="time-period">Time Period</Label>
                <Select 
                  value={timeFilter} 
                  onValueChange={setTimeFilter}
                >
                  <SelectTrigger id="time-period">
                    <SelectValue placeholder="All time" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All time</SelectItem>
                    <SelectItem value="last-30-days">Last 30 days</SelectItem>
                    <SelectItem value="last-90-days">Last 90 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="review-status">Review Status</Label>
                <Select 
                  value={reviewFilter} 
                  onValueChange={setReviewFilter}
                >
                  <SelectTrigger id="review-status">
                    <SelectValue placeholder="All reviews" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="reviewed">Reviewed</SelectItem>
                    <SelectItem value="not-reviewed">Not reviewed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <SheetFooter>
              <Button 
                variant="outline" 
                onClick={() => {
                  setTimeFilter("all");
                  setReviewFilter("all");
                }}
              >
                Reset Filters
              </Button>
              <Button onClick={() => setFilterOpen(false)}>Apply</Button>
            </SheetFooter>
          </SheetContent>
        </Sheet>
      </div>
      
      <p className="text-muted-foreground text-sm mb-5">
        View and manage your past service projects
      </p>

      {/* Completed job cards */}
      <div className="space-y-3 mb-4">
        {filteredJobs.length > 0 ? (
          filteredJobs.map(job => (
            <Card key={job.id} className="overflow-hidden border shadow-sm">
              <div className={job.reviewed ? 
                "bg-gradient-to-r from-green-50 to-green-100 h-1.5" : 
                "bg-gradient-to-r from-amber-50 to-amber-100 h-1.5"} 
              />
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-lg font-medium leading-tight">{job.title}</h3>
                  <Badge 
                    className={job.reviewed ? 
                      "bg-green-100 text-green-700 hover:bg-green-200" : 
                      "bg-amber-100 text-amber-700 hover:bg-amber-200"}
                  >
                    {job.reviewed ? "Reviewed" : "Needs Review"}
                  </Badge>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Provider:</span>
                    <span className="font-medium">{job.provider}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Completed:</span>
                    <span className="font-medium flex items-center">
                      <Calendar className="h-3.5 w-3.5 mr-1.5 text-green-600" />
                      {job.completionDate}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Amount:</span>
                    <span className="font-medium">{job.amount}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="px-4 py-3 border-t bg-gray-50">
                <div className="w-full">
                  {job.reviewed ? (
                    <Button 
                      variant="outline" 
                      className="w-full justify-between"
                      onClick={() => handleViewJobDetails(job)}
                    >
                      <span className="flex items-center">
                        <FileCheck className="h-4 w-4 mr-2 text-green-600" />
                        View Job Summary
                      </span>
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  ) : (
                    <Button 
                      className="w-full justify-between bg-amber-600 hover:bg-amber-700"
                      onClick={() => handleWriteReview(job)}
                    >
                      <span className="flex items-center">
                        <Star className="h-4 w-4 mr-2" />
                        Leave a Review
                      </span>
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardFooter>
            </Card>
          ))
        ) : (
          <div className="flex flex-col items-center justify-center py-12 px-4">
            <div className="bg-gray-100 p-4 rounded-full mb-3">
              <FileCheck className="h-8 w-8 text-gray-500" />
            </div>
            <h3 className="text-lg font-medium mb-1">No completed jobs found</h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              You don't have any completed jobs matching your filters.
            </p>
            <Button 
              variant="outline" 
              onClick={() => {
                setTimeFilter("all");
                setReviewFilter("all");
              }}
            >
              <Filter className="mr-2 h-4 w-4" /> Clear Filters
            </Button>
          </div>
        )}
      </div>

      {/* Dialogs */}
      {selectedJob && (
        <>
          <ReviewFormDialog 
            isOpen={isReviewDialogOpen}
            onClose={() => setIsReviewDialogOpen(false)}
            job={selectedJob}
          />
          
          <JobSummaryDialog
            isOpen={isSummaryDialogOpen}
            onClose={() => setIsSummaryDialogOpen(false)}
            job={selectedJob}
          />
        </>
      )}
    </div>
  );
}
