
import React from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MessageSquare, Calendar, ArrowRight } from "lucide-react";

export function JobManage() {
  const { jobId } = useParams();
  
  // Mock data - in real app, fetch job details based on jobId
  const job = {
    status: "Scheduled",
    provider: "Mike's Plumbing",
    scheduledDate: "Apr 25, 2025",
    title: "Kitchen Sink Repair"
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Manage Job</h1>
      <Card>
        <CardHeader>
          <CardTitle>{job.title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="font-medium">Provider</p>
            <p className="text-muted-foreground">{job.provider}</p>
          </div>
          <div>
            <p className="font-medium">Scheduled Date</p>
            <p className="text-muted-foreground">{job.scheduledDate}</p>
          </div>
          <div className="space-y-2">
            <Button className="w-full">
              <Calendar className="mr-2 h-4 w-4" />
              Reschedule Appointment
            </Button>
            <Button variant="outline" className="w-full">
              <MessageSquare className="mr-2 h-4 w-4" />
              Contact Provider
            </Button>
            <Button variant="destructive" className="w-full">
              Cancel Job
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
