
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { CalendarIcon, FileCheck, DollarSign, User, MapPin } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface JobSummaryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  job: {
    id: string;
    title: string;
    provider: string;
    completionDate: string;
    amount: string;
    reviewed: boolean;
    location?: string;
    description?: string;
  };
}

export function JobSummaryDialog({ isOpen, onClose, job }: JobSummaryDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Job Summary</DialogTitle>
          <DialogDescription>
            Details for your completed service
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-2">
          <div className="border-b pb-3">
            <h3 className="text-lg font-semibold mb-1">{job.title}</h3>
            <Badge 
              className={job.reviewed ? 
                "bg-green-100 text-green-700 hover:bg-green-200" : 
                "bg-amber-100 text-amber-700 hover:bg-amber-200"}
            >
              {job.reviewed ? "Reviewed" : "Needs Review"}
            </Badge>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-start gap-2">
              <User className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Service Provider</p>
                <p className="text-sm text-muted-foreground">{job.provider}</p>
              </div>
            </div>
            
            <div className="flex items-start gap-2">
              <CalendarIcon className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Completion Date</p>
                <p className="text-sm text-muted-foreground">{job.completionDate}</p>
              </div>
            </div>
            
            <div className="flex items-start gap-2">
              <DollarSign className="h-5 w-5 text-amber-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Total Amount</p>
                <p className="text-sm text-muted-foreground">{job.amount}</p>
              </div>
            </div>
            
            {job.location && (
              <div className="flex items-start gap-2">
                <MapPin className="h-5 w-5 text-purple-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Location</p>
                  <p className="text-sm text-muted-foreground">{job.location}</p>
                </div>
              </div>
            )}
          </div>
          
          {job.description && (
            <Card className="bg-gray-50">
              <CardContent className="pt-4">
                <h4 className="text-sm font-medium mb-1">Job Description</h4>
                <p className="text-sm text-muted-foreground">{job.description}</p>
              </CardContent>
            </Card>
          )}
          
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium mb-2">Job Status</h4>
            <div className="flex items-center gap-2">
              <div className="bg-green-100 p-1.5 rounded-full">
                <FileCheck className="h-4 w-4 text-green-600" />
              </div>
              <span className="text-sm">This job has been completed</span>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end gap-3 mt-2">
          <DialogClose asChild>
            <Button variant="outline" onClick={onClose}>Close</Button>
          </DialogClose>
        </div>
      </DialogContent>
    </Dialog>
  );
}
