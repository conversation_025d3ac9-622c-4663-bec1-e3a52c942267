
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Plus, Filter, ArrowRight } from "lucide-react";
import { MobileJobCard } from './MobileJobCard';
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetFooter,
  Sheet<PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { Link } from 'react-router-dom';

export function MobileActiveJobs() {
  const [filterOpen, setFilterOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");
  const { toast } = useToast();

  // Mock data for active jobs
  const activeJobs = [
    {
      id: "job1",
      title: "Bathroom Renovation",
      provider: "Home Renovation Experts",
      status: "In Progress",
      date: "Started Apr 15, 2025",
      messages: 3,
      bids: 4
    },
    {
      id: "job2",
      title: "Kitchen Plumbing Fix",
      provider: "Mike's Plumbing",
      status: "Scheduled",
      date: "Starting Apr 25, 2025",
      messages: 2,
      bids: 5
    },
    {
      id: "job3",
      title: "Lawn Mowing Service",
      provider: "Pending Selection",
      status: "Awaiting Provider",
      date: "Posted Apr 10, 2025",
      messages: 0,
      bids: 3
    }
  ];

  // Filter jobs based on selected status
  const filteredJobs = statusFilter === "all" 
    ? activeJobs 
    : activeJobs.filter(job => job.status.toLowerCase() === statusFilter);

  const handleRescheduleJob = (jobId: string) => {
    toast({
      title: "Reschedule requested",
      description: `You requested to reschedule job ${jobId}`
    });
  };

  const handleCancelJob = (jobId: string) => {
    toast({
      title: "Cancel requested", 
      description: `You requested to cancel job ${jobId}`
    });
  };

  return (
    <div className="p-4 pb-16">
      {/* Header with filter and new job button */}
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-xl font-bold">Active Jobs</h1>
        <div className="flex gap-2">
          <Sheet open={filterOpen} onOpenChange={setFilterOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="h-9 w-9">
                <Filter className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <SheetHeader>
                <SheetTitle>Filter Jobs</SheetTitle>
                <SheetDescription>
                  Filter your active jobs by status or date.
                </SheetDescription>
              </SheetHeader>
              
              <div className="py-6 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select 
                    value={statusFilter} 
                    onValueChange={setStatusFilter}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="in progress">In Progress</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="awaiting provider">Awaiting Provider</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <SheetFooter>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setStatusFilter("all");
                  }}
                >
                  Reset Filters
                </Button>
                <Button onClick={() => setFilterOpen(false)}>Apply</Button>
              </SheetFooter>
            </SheetContent>
          </Sheet>
          
          <Button size="sm" className="h-9" asChild>
            <Link to="/create-job">
              <Plus className="h-4 w-4 mr-1" /> New
            </Link>
          </Button>
        </div>
      </div>
      
      <p className="text-muted-foreground text-sm mb-5">
        Manage your ongoing service requests
      </p>

      {/* Job list with swipeable cards */}
      <div className="space-y-3 mb-4">
        {filteredJobs.length > 0 ? (
          filteredJobs.map(job => (
            <MobileJobCard 
              key={job.id} 
              job={job} 
              onReschedule={handleRescheduleJob}
              onCancel={handleCancelJob}
            />
          ))
        ) : (
          <div className="flex flex-col items-center justify-center py-12 px-4">
            <div className="bg-gray-100 p-4 rounded-full mb-3">
              <FileText className="h-8 w-8 text-gray-500" />
            </div>
            <h3 className="text-lg font-medium mb-1">No active jobs</h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              You don't have any active jobs with the selected filter.
            </p>
            <Button asChild>
              <Link to="/create-job">
                <Plus className="mr-2 h-4 w-4" /> Post a New Job
              </Link>
            </Button>
          </div>
        )}
      </div>
      
      {filteredJobs.length > 0 && (
        <Link 
          to="/create-job"
          className="fixed bottom-20 right-4 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg flex items-center justify-center"
        >
          <Plus className="h-6 w-6" />
        </Link>
      )}
    </div>
  );
}
