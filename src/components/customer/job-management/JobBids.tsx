
import React from 'react';
import { useParams } from 'react-router-dom';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MessageSquare, Calendar, ArrowRight } from "lucide-react";

export function JobBids() {
  const { jobId } = useParams();

  // Mock data - in real app, fetch bids for this specific job
  const bids = [];

  if (bids.length === 0) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Job Bids</h1>
        <Card>
          <CardContent className="py-8 text-center">
            <h3 className="text-lg font-medium mb-2">No bids yet</h3>
            <p className="text-muted-foreground mb-4">
              We'll notify you when pros respond to your job request!
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Review Bids</h1>
      {/* Bid cards would go here when implemented */}
    </div>
  );
}
