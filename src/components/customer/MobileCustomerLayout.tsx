
import React from 'react';
import { MobileCustomerHeader } from './MobileCustomerHeader';

interface MobileCustomerLayoutProps {
  children: React.ReactNode;
  title?: string;
}

export function MobileCustomerLayout({ children, title = 'Dashboard' }: MobileCustomerLayoutProps) {
  return (
    <div className="flex flex-col h-full">
      <MobileCustomerHeader title={title} />
      <div className="flex-1 overflow-auto pt-14">
        {children}
      </div>
    </div>
  );
}
