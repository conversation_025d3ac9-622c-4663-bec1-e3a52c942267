
import React from 'react';
import { Outlet } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { CustomerSidebar } from './CustomerSidebar';
import { SidebarProvider, useSidebar } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { MobileCustomerHeader } from './MobileCustomerHeader';
import Footer from '@/components/Footer';
import { MobileNavigation } from '@/components/MobileNavigation';
import { MobileFooter } from '@/components/MobileFooter';

const CustomerLayout: React.FC = () => {
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <div className="flex flex-col min-h-screen">
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex-1">
          <MobileCustomerHeader title="Dashboard" />
          <div className="flex-1 overflow-auto pt-14">
            <Outlet />
          </div>
        </div>
        <MobileNavigation />
        <MobileFooter />
      </div>
    );
  }

  return (
    <SidebarProvider defaultOpen={true}>
      <DesktopCustomerLayout />
    </SidebarProvider>
  );
};

const DesktopCustomerLayout: React.FC = () => {
  const { state: sidebarState } = useSidebar();

  return (
    <div className="flex flex-col min-h-screen w-full">
      <div className="flex flex-1">
        <CustomerSidebar />
        <div
          className={cn(
            "flex-1 overflow-auto transition-all",
            sidebarState === "collapsed" ? "md:pl-12" : "md:pl-60"
          )}
        >
          <div className="container py-6 mx-auto">
            <Outlet />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default CustomerLayout;
