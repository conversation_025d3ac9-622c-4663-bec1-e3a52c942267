
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from "@/components/ui/card";
import { GetStartedSection } from './GetStartedSection';
import { Button } from "@/components/ui/button";
import {
  FileText,
  Wrench,
  Users,
  Calendar as CalendarIcon,
  CreditCard,
  Lightbulb,
  Clock,
  Upload
} from "lucide-react";
import { Link } from "react-router-dom";
import { useIsMobile } from '@/hooks/use-mobile';
import { MobileCustomerDashboard } from './MobileCustomerDashboard';
import { useAuth } from '@/features/auth/hooks/useAuth';

export function DashboardHome() {
  const { user } = useAuth();

  // Get first name from full name or use fallback
  const getFirstName = () => {
    if (!user?.name) return "Guest";
    return user.name.split(' ')[0];
  };

  const userName = getFirstName();
  const isMobile = useIsMobile();

  const dashboardData = {
    activeJobs: 3,
    bidsReceived: 8,
    upcomingAppointment: {
      title: "Kitchen Plumbing",
      date: "May 5, 2025",
      time: "10:00 AM",
      provider: "Mike's Plumbing"
    },
    recentPayments: [
      { id: "pay1", job: "Bathroom Remodel", amount: "$750.00", date: "Apr 15, 2025", status: "Paid" },
      { id: "pay2", job: "Lawn Service", amount: "$120.00", date: "Apr 8, 2025", status: "Paid" },
    ],
    tips: [
      { id: "tip1", title: "How to choose the right provider", content: "Look for reviews, verify credentials, and ask about insurance before hiring." },
      { id: "tip2", title: "Get faster bids by uploading photos", content: "Providers respond 3x faster when your job post includes clear photos." }
    ]
  };

  if (isMobile) {
    return <MobileCustomerDashboard />;
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Welcome back, {userName}!</h1>
          <p className="text-muted-foreground">Manage your service requests and providers.</p>
        </div>
        <Button size="lg" className="mt-4 md:mt-0">
          <FileText className="mr-2 h-4 w-4" /> Post a New Job
        </Button>
      </div>

      {/* Get Started Section */}
      <GetStartedSection />

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.activeJobs}</div>
            <p className="text-xs text-muted-foreground">Open service requests</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Bids Received</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.bidsReceived}</div>
            <p className="text-xs text-muted-foreground">Providers interested</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Providers Hired</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">Total to date</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Completed Jobs</CardTitle>
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">9</div>
            <p className="text-xs text-muted-foreground">Successfully finished</p>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Appointment Card */}
      {dashboardData.upcomingAppointment && (
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Appointment</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="bg-primary/10 p-3 rounded-full">
                <Clock className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="font-medium">{dashboardData.upcomingAppointment.title}</h3>
                <p className="text-sm text-muted-foreground">
                  {dashboardData.upcomingAppointment.date} at {dashboardData.upcomingAppointment.time} with {dashboardData.upcomingAppointment.provider}
                </p>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" size="sm" asChild>
              <Link to="/customer/dashboard?tab=calendar">View Calendar</Link>
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Flexible Content Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Recent Payments */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Payments</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-4">
              {dashboardData.recentPayments.map(payment => (
                <li key={payment.id} className="flex justify-between items-center border-b pb-2">
                  <div>
                    <p className="font-medium">{payment.job}</p>
                    <p className="text-sm text-muted-foreground">{payment.date}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs ${payment.status === 'Paid' ? 'bg-green-100 text-green-800' : 'bg-amber-100 text-amber-800'}`}>
                      {payment.status}
                    </span>
                    <span className="font-medium">{payment.amount}</span>
                  </div>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button variant="outline" size="sm" asChild>
              <Link to="/customer/dashboard?tab=payments">View All Payments</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Tips Card */}
        <Card>
          <CardHeader className="flex flex-row items-center">
            <CardTitle>JobON Tips</CardTitle>
            <Lightbulb className="h-4 w-4 ml-2 text-amber-500" />
          </CardHeader>
          <CardContent>
            <ul className="space-y-4">
              {dashboardData.tips.map(tip => (
                <li key={tip.id} className="border-b pb-3">
                  <div className="flex items-start gap-3">
                    {tip.id === "tip2" ? (
                      <Upload className="h-5 w-5 text-primary mt-1" />
                    ) : (
                      <Lightbulb className="h-5 w-5 text-amber-500 mt-1" />
                    )}
                    <div>
                      <h4 className="font-medium">{tip.title}</h4>
                      <p className="text-sm text-muted-foreground">{tip.content}</p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
