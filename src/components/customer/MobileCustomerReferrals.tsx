
import React from 'react';
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Gift, Copy, Trophy } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

type ReferralActivity = {
  friendName: string;
  date: string;
  amount: number;
};

export function MobileCustomerReferrals() {
  const { toast } = useToast();
  const [showRewardDialog, setShowRewardDialog] = React.useState(false);
  const [rewardDialogContent, setRewardDialogContent] = React.useState({
    title: '',
    description: '',
    amount: 0
  });

  // In a real app, these would come from user profile stored in state/context
  // For now using mock data
  const firstName = "John";
  const lastName = "Doe";
  
  // Generate a referral code based on user info
  const generateReferralCode = () => {
    // Combine firstName and lastName
    let baseCode = `${firstName.toLowerCase()}${lastName.toLowerCase()}`;
    
    // Replace special characters
    baseCode = baseCode.replace(/[^a-z0-9]/g, '');
    
    // In a real app, we would check for duplicates in the database
    // and append a number if necessary
    // Here we'll simulate with a random number to demonstrate
    const userCount = Math.floor(Math.random() * 3); // Simulate 0-2 duplicates
    
    if (userCount > 0) {
      baseCode = `${baseCode}${userCount}`;
    }
    
    return baseCode;
  };
  
  const referralCode = generateReferralCode();
  const referralLink = `https://jobon.com/ref/${referralCode}`;
  
  const [referralStats, setReferralStats] = React.useState({
    totalReferred: 7,
    totalEarned: 350,
  });

  // Recent activity - Would come from API
  const [recentActivity, setRecentActivity] = React.useState<ReferralActivity[]>([
    { friendName: "Sarah T.", date: "2025-04-21", amount: 50 },
    { friendName: "Michael R.", date: "2025-04-20", amount: 50 },
    { friendName: "Emma P.", date: "2025-04-19", amount: 50 },
  ]);

  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralLink);
    toast({
      title: "Referral Link Copied!",
      description: "Share it with your friends to earn rewards.",
    });
  };

  // Calculate progress percentage for the next milestone
  const getNextMilestone = () => {
    if (referralStats.totalReferred < 3) {
      return { target: 3, progress: (referralStats.totalReferred / 3) * 100 };
    } else if (referralStats.totalReferred < 5) {
      return { target: 5, progress: (referralStats.totalReferred / 5) * 100 };
    } else {
      const nextTarget = Math.ceil(referralStats.totalReferred / 5) * 5;
      return {
        target: nextTarget,
        progress: (referralStats.totalReferred / nextTarget) * 100
      };
    }
  };

  // Get badge status based on referral count
  const getBadges = () => {
    return {
      starter: referralStats.totalReferred >= 1,
      expert: referralStats.totalReferred >= 3,
      champion: referralStats.totalReferred >= 5,
    };
  };

  const nextMilestone = getNextMilestone();
  const badges = getBadges();

  return (
    <div className="p-4 space-y-4 pb-16">
      {/* Main Referral Card - Mobile Optimized */}
      <Card className="border-primary/20 shadow-sm">
        <CardContent className="p-4 space-y-4">
          <div className="flex items-center gap-2">
            <div className="bg-primary/10 p-2 rounded-full">
              <Gift className="h-5 w-5 text-primary" />
            </div>
            <h3 className="font-medium text-lg">Invite Friends & Earn</h3>
          </div>
          
          <p className="text-sm">
            Get $50 JobON Credit for every friend who completes their first job. No limits!
          </p>

          <div className="relative">
            <Input 
              value={referralLink} 
              readOnly 
              className="pr-12 font-mono text-sm" 
            />
            <Button 
              size="sm" 
              className="absolute right-1 top-1 h-7"
              onClick={copyReferralLink}
            >
              <Copy className="h-3.5 w-3.5 mr-1" />
              Copy
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards - Side by Side */}
      <div className="grid grid-cols-2 gap-3">
        <Card className="bg-primary/5">
          <CardContent className="p-4 flex flex-col items-center justify-center">
            <span className="text-sm text-muted-foreground mb-1">Referred</span>
            <span className="text-2xl font-bold">{referralStats.totalReferred}</span>
            <span className="text-xs text-muted-foreground mt-1">friends</span>
          </CardContent>
        </Card>
        
        <Card className="bg-primary/5">
          <CardContent className="p-4 flex flex-col items-center justify-center">
            <span className="text-sm text-muted-foreground mb-1">Earned</span>
            <span className="text-2xl font-bold">${referralStats.totalEarned}</span>
            <span className="text-xs text-muted-foreground mt-1">in credits</span>
          </CardContent>
        </Card>
      </div>

      {/* Progress Tracker */}
      <Card>
        <CardContent className="p-4 space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">Next milestone: {nextMilestone.target} referrals</span>
            <span>{referralStats.totalReferred}/{nextMilestone.target}</span>
          </div>
          <Progress value={nextMilestone.progress} className="h-2" />
          
          <div className="flex gap-2 flex-wrap pt-2">
            {badges.starter && (
              <Badge className="bg-blue-50 text-blue-600 border border-blue-200">
                🌟 Referral Starter
              </Badge>
            )}
            {badges.expert && (
              <Badge className="bg-indigo-50 text-indigo-600 border border-indigo-200">
                ⭐ Referral Expert
              </Badge>
            )}
            {badges.champion && (
              <Badge className="bg-purple-50 text-purple-600 border border-purple-200">
                🏆 Referral Champion
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <div>
        <h3 className="font-medium text-base mb-2 px-1 flex items-center gap-2">
          <Trophy className="h-4 w-4 text-primary" />
          Recent Activity
        </h3>
        <div className="space-y-2">
          {recentActivity.map((activity, index) => (
            <Card key={index} className="overflow-hidden">
              <div className="h-1 bg-primary/30" />
              <CardContent className="p-3">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium text-sm">{activity.friendName}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(activity.date).toLocaleDateString()}
                    </p>
                  </div>
                  <Badge className="bg-blue-50 text-blue-600 border border-blue-100">
                    +${activity.amount}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* How It Works Card */}
      <Card className="bg-muted/20 border-dashed">
        <CardContent className="p-4 space-y-3">
          <h3 className="font-medium text-base">How It Works</h3>
          <ol className="list-decimal list-inside text-sm space-y-2 pl-1">
            <li>Share your unique referral link with friends</li>
            <li>Friend signs up and books their first service</li>
            <li>After service completion, you get $50 credit</li>
            <li>Credits automatically apply to future bookings</li>
          </ol>
        </CardContent>
      </Card>

      {/* Reward Dialog */}
      <Dialog open={showRewardDialog} onOpenChange={setShowRewardDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl">{rewardDialogContent.title}</DialogTitle>
            <DialogDescription className="text-center">
              {rewardDialogContent.description}
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col items-center py-6">
            <div className="bg-blue-50 p-4 rounded-full mb-4">
              <Gift className="h-12 w-12 text-blue-600" />
            </div>
            <p className="text-3xl font-bold mb-2">${rewardDialogContent.amount}</p>
            <p className="text-center text-muted-foreground">
              Added to your wallet balance!
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
