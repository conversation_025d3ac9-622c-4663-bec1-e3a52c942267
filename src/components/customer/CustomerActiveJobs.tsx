
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, MessageSquare, Users, Clock, ArrowRight, Loader2 } from "lucide-react";
import { Link } from "react-router-dom";
import { apiService } from "@/services/api";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";

// Define interfaces for API response and job data
interface JobBooking {
  jobId: string;
  projectCode: string;
  createdAt: string;
  status: string;
  jobType: string;
  property: {
    type: string;
  };
  service: {
    category: string;
    tasks: string[];
    customTask: string | null;
  };
  schedule: {
    date: string;
    timePreference: string;
  };
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  contact: {
    fullName: string;
    email: string;
    phone: string;
  };
  user: {
    id: number;
    name: string;
    email: string;
  };
}

interface PaginationData {
  current_page: number;
  per_page: number;
  total: number;
  last_page: number;
}

interface ApiResponse {
  success: boolean;
  data: JobBooking[];
  pagination: PaginationData;
}

interface ActiveJob {
  id: string;
  title: string;
  provider: string;
  status: string;
  date: string;
  messages: number;
  bids: number;
}

export function CustomerActiveJobs() {
  const [activeJobs, setActiveJobs] = useState<ActiveJob[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    current_page: 1,
    per_page: 15,
    total: 0,
    last_page: 1
  });

  const { token } = useAuth();
  const { toast } = useToast();

  // Function to fetch job bookings from the API
  const fetchMyJobBookings = async () => {
    setIsLoading(true);
    setError(null);

    // Get the token without 'Bearer ' prefix
    const authToken = token ? token.replace('Bearer ', '') : '';

    if (!authToken) {
      setError('Authentication token is missing. Please log in again.');
      setIsLoading(false);
      toast({
        title: 'Authentication Error',
        description: 'You need to be logged in to view your jobs.',
        variant: 'destructive'
      });
      return;
    }

    try {
      const response = await apiService<ApiResponse>('/api/job-bookings/my-job-bookings', {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.isSuccess && response.data) {
        // Transform API response to match component's expected format
        const transformedJobs = response.data.data.map(job => ({
          id: job.jobId,
          title: job.service.tasks.join(", ") || job.service.category,
          provider: job.jobType === 'send_bids' ? 'Pending Selection' : (job.contact?.fullName || 'Unknown Provider'),
          status: mapStatusToDisplayStatus(job.status),
          date: formatDate(job.createdAt, job.status),
          messages: 0, // Assuming this would come from a different API
          bids: 0 // Assuming this would come from a different API
        }));

        setActiveJobs(transformedJobs);
        setPagination(response.data.pagination);
      } else {
        throw new Error(response.error || 'Failed to fetch job bookings');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching job bookings';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to map API status to display status
  const mapStatusToDisplayStatus = (status: string): string => {
    const statusMap: Record<string, string> = {
      'pending': 'Awaiting Provider',
      'confirmed': 'Scheduled',
      'in_progress': 'In Progress',
      'completed': 'Completed',
      'cancelled': 'Cancelled'
    };

    return statusMap[status] || status;
  };

  // Helper function to format date based on status
  const formatDate = (dateString: string, status: string): string => {
    const date = new Date(dateString);
    const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });

    if (status === 'pending') {
      return `Posted ${formattedDate}`;
    } else if (status === 'confirmed') {
      return `Starting ${formattedDate}`;
    } else if (status === 'in_progress') {
      return `Started ${formattedDate}`;
    }

    return formattedDate;
  };

  // Fetch job bookings when component mounts
  useEffect(() => {
    fetchMyJobBookings();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">
            Active Jobs {!isLoading && pagination.total > 0 && `(${pagination.total})`}
          </h1>
          <p className="text-muted-foreground">Manage your ongoing service requests</p>
        </div>
        <Button asChild>
          <Link to="/create-job">
            <FileText className="mr-2 h-4 w-4" /> Post a New Job
          </Link>
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-3 text-lg">Loading your active jobs...</span>
        </div>
      ) : error ? (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="py-8 text-center">
            <h3 className="text-lg font-medium text-red-700">Error loading jobs</h3>
            <p className="text-sm text-red-600 mb-4">
              {error}
            </p>
            <Button onClick={fetchMyJobBookings}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      ) : activeJobs.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="py-8 text-center">
            <div className="flex justify-center mb-4">
              <FileText className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium">No active jobs</h3>
            <p className="text-sm text-muted-foreground mb-4">
              You don't have any active jobs at the moment. Post a new job to get started.
            </p>
            <Button asChild>
              <Link to="/create-job">
                <FileText className="mr-2 h-4 w-4" /> Post a Job
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {activeJobs.map(job => (
            <Card key={job.id} className="flex flex-col">
              <CardHeader className="pb-2">
                <CardTitle className="text-xl">{job.title}</CardTitle>
                <div className="flex items-center mt-1">
                  <span className={`text-sm px-2 py-1 rounded-full ${
                    job.status === 'In Progress' ? 'bg-blue-100 text-blue-700' :
                    job.status === 'Scheduled' ? 'bg-purple-100 text-purple-700' :
                    job.status === 'Awaiting Provider' ? 'bg-amber-100 text-amber-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {job.status}
                  </span>
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <div className="space-y-3">
                  <div className="flex items-center text-sm">
                    <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{job.provider}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{job.date}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    {job.messages > 0 && (
                      <div className="flex items-center text-sm">
                        <MessageSquare className="h-4 w-4 mr-1 text-muted-foreground" />
                        <span>{job.messages}</span>
                      </div>
                    )}
                    {job.status === 'Awaiting Provider' && (
                      <div className="text-sm text-primary">
                        {job.bids} bids received
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
              <div className="p-4 pt-0">
                <Button
                  variant={job.status === "Awaiting Provider" ? "default" : "outline"}
                  className="w-full justify-between"
                  asChild
                >
                  <Link to={`/customer/jobs/${job.id}/${job.status === "Awaiting Provider" ? 'bids' : 'manage'}`}>
                    {job.status === "Awaiting Provider" ? "View Bids" : "Manage Job"}
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
