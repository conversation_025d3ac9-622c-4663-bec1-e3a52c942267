import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  FileText,
  Wrench,
  Clock,
  PlusCircle,
  ChevronRight,
  Star,
  MessageSquare
} from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/features/auth/hooks/useAuth';

export function MobileCustomerDashboard() {
  const { user } = useAuth();

  // Get first name from full name or use fallback
  const getFirstName = () => {
    if (!user?.name) return "Guest";
    return user.name.split(' ')[0];
  };

  const userName = getFirstName();
  const { toast } = useToast();

  const dashboardData = {
    activeJobs: 3,
    bidsReceived: 8,
    messagesUnread: 2,
    upcomingAppointment: {
      title: "Kitchen Plumbing",
      date: "May 5, 2025",
      time: "10:00 AM",
      provider: "<PERSON>'s Plumbing"
    },
    recentMessages: [
      {
        id: "conv1",
        providerName: "Mike's Plumbing",
        providerInitials: "MP",
        lastMessage: "Can you confirm the appointment time?",
        time: "11:42 AM",
        unread: true
      },
      {
        id: "conv2",
        providerName: "Green Thumb Landscaping",
        providerInitials: "GT",
        providerAvatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=1964&auto=format&fit=crop",
        lastMessage: "Your quote is ready to review",
        time: "Yesterday",
        unread: false
      }
    ]
  };

  const handleMessageClick = () => {
    toast({
      title: "Opening messages",
      description: "Navigating to messages page"
    });
  };

  return (
    <div className="space-y-4 mb-16 p-4">
      {/* Welcome Bar */}
      <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4">
        <h1 className="text-xl font-bold mb-0.5">Hi {userName}!</h1>
        <p className="text-sm text-muted-foreground">What service do you need today?</p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-3">
        <Button
          className="h-auto py-4 flex flex-col items-center justify-center bg-blue-600 hover:bg-blue-700"
          asChild
        >
          <Link to="/create-job">
            <PlusCircle className="h-6 w-6 mb-1" />
            <span>Post Job</span>
          </Link>
        </Button>
        <Button
          className="h-auto py-4 flex flex-col items-center justify-center bg-green-600 hover:bg-green-700"
          variant="secondary"
          asChild
        >
          <Link to="/professionals">
            <Wrench className="h-6 w-6 mb-1" />
            <span>Find Pro</span>
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 gap-3">
        <Card className="shadow-sm border-blue-100">
          <CardContent className="p-4 flex flex-col items-center">
            <div className="bg-blue-50 p-2 rounded-full mb-2">
              <FileText className="h-5 w-5 text-blue-600" />
            </div>
            <div className="text-xl font-bold">{dashboardData.activeJobs}</div>
            <p className="text-xs text-muted-foreground">Active Jobs</p>
          </CardContent>
        </Card>

        <Card className="shadow-sm border-amber-100">
          <CardContent className="p-4 flex flex-col items-center">
            <div className="bg-amber-50 p-2 rounded-full mb-2">
              <Wrench className="h-5 w-5 text-amber-600" />
            </div>
            <div className="text-xl font-bold">{dashboardData.bidsReceived}</div>
            <p className="text-xs text-muted-foreground">Bids Received</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Messages Card */}
      <Card className="shadow-sm">
        <CardHeader className="pb-0 pt-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-primary" />
              <CardTitle className="text-base">Recent Messages</CardTitle>
            </div>
            {dashboardData.messagesUnread > 0 && (
              <Badge className="bg-blue-600">{dashboardData.messagesUnread} new</Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="pt-2 pb-3">
          <div className="space-y-2">
            {dashboardData.recentMessages.map(message => (
              <Link
                to="/messages"
                key={message.id}
                className="flex items-start gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-md transition-colors"
              >
                <Avatar className="h-9 w-9">
                  {message.providerAvatar ? (
                    <AvatarImage src={message.providerAvatar} alt={message.providerName} />
                  ) : (
                    <AvatarFallback className="bg-gradient-to-br from-blue-400 to-indigo-500 text-white">
                      {message.providerInitials}
                    </AvatarFallback>
                  )}
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between">
                    <h4 className="font-medium text-sm truncate">{message.providerName}</h4>
                    <span className="text-xs text-muted-foreground">{message.time}</span>
                  </div>
                  <p className="text-xs truncate">{message.lastMessage}</p>
                </div>
                {message.unread && (
                  <div className="w-2 h-2 rounded-full bg-blue-600 mt-2"></div>
                )}
              </Link>
            ))}
          </div>
        </CardContent>
        <CardFooter className="pt-0 pb-3 px-3">
          <Button variant="outline" size="sm" asChild className="w-full">
            <Link to="/messages">
              View All Messages
              <ChevronRight className="h-3 w-3 ml-1" />
            </Link>
          </Button>
        </CardFooter>
      </Card>

      {/* Upcoming Appointment Card */}
      {dashboardData.upcomingAppointment && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Upcoming Appointment</CardTitle>
          </CardHeader>
          <CardContent className="pb-3">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 p-2 rounded-full">
                <Clock className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-medium text-sm">{dashboardData.upcomingAppointment.title}</h3>
                <p className="text-xs text-muted-foreground">
                  {dashboardData.upcomingAppointment.date} at {dashboardData.upcomingAppointment.time}
                </p>
                <p className="text-xs text-muted-foreground">
                  {dashboardData.upcomingAppointment.provider}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Links */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base">Quick Access</CardTitle>
        </CardHeader>
        <CardContent className="px-2 py-0">
          <Link to="/customer/dashboard?tab=active-jobs" className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-md">
            <div className="flex items-center">
              <div className="bg-blue-50 p-1.5 rounded-full mr-3">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
              <span className="text-sm font-medium">My Jobs</span>
            </div>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </Link>

          <Link to="/customer/dashboard?tab=messages" className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-md">
            <div className="flex items-center">
              <div className="bg-green-50 p-1.5 rounded-full mr-3">
                <MessageSquare className="h-4 w-4 text-green-600" />
              </div>
              <span className="text-sm font-medium">Messages</span>
              {dashboardData.messagesUnread > 0 && (
                <Badge className="ml-2 bg-blue-600 text-white">{dashboardData.messagesUnread}</Badge>
              )}
            </div>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </Link>

          <Link to="/customer/dashboard?tab=calendar" className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-md">
            <div className="flex items-center">
              <div className="bg-purple-50 p-1.5 rounded-full mr-3">
                <Clock className="h-4 w-4 text-purple-600" />
              </div>
              <span className="text-sm font-medium">Appointments</span>
            </div>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </Link>

          <Link to="/customer/dashboard?tab=reviews" className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-md">
            <div className="flex items-center">
              <div className="bg-amber-50 p-1.5 rounded-full mr-3">
                <Star className="h-4 w-4 text-amber-600" />
              </div>
              <span className="text-sm font-medium">Reviews</span>
            </div>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </Link>
        </CardContent>
      </Card>
    </div>
  );
}
