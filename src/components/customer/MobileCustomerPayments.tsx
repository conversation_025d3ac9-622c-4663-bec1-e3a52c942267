
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ChevronDown, CreditCard, Download, Search } from "lucide-react";
import { cn } from "@/lib/utils";

export function MobileCustomerPayments() {
  // Mock payments data
  const payments = [
    {
      id: "pay1",
      date: "Apr 15, 2025",
      job: "Bathroom Remodel",
      provider: "Deluxe Renovations",
      amount: "$750.00",
      status: "Paid"
    },
    {
      id: "pay2",
      date: "Apr 8, 2025",
      job: "Lawn Service",
      provider: "Green Thumb Landscaping",
      amount: "$120.00",
      status: "Paid"
    },
    {
      id: "pay3",
      date: "Apr 2, 2025",
      job: "Plumbing Repair",
      provider: "<PERSON>'s Plumbing",
      amount: "$350.00",
      status: "Paid"
    },
    {
      id: "pay4",
      date: "Mar 25, 2025",
      job: "HVAC Maintenance",
      provider: "Cool Air Systems",
      amount: "$275.00",
      status: "Refunded"
    },
    {
      id: "pay5",
      date: "Mar 18, 2025",
      job: "Electrical Wiring",
      provider: "Elite Electricians",
      amount: "$425.00",
      status: "Paid"
    }
  ];
  
  // Calculate total spending
  const totalSpending = payments
    .filter(payment => payment.status === "Paid")
    .reduce((total, payment) => total + parseFloat(payment.amount.replace('$', '')), 0);
    
  const [showSummary, setShowSummary] = useState(true);

  return (
    <div className="p-4 space-y-4 mb-16">
      <div className="px-0">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input placeholder="Search payments..." className="pl-10 shadow-sm" />
        </div>
      </div>
      
      <Card className="shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Payment History</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="grid grid-cols-12 bg-muted/40 text-xs font-medium text-muted-foreground px-3 py-2">
            <div className="col-span-3">Date</div>
            <div className="col-span-5">Job</div>
            <div className="col-span-4 text-right">Amount</div>
          </div>
          
          {payments.map((payment) => (
            <div key={payment.id} className="border-b last:border-0">
              <div className="px-3 py-3">
                <div className="grid grid-cols-12 items-center mb-1">
                  <div className="col-span-3 text-sm">{payment.date}</div>
                  <div className="col-span-5 text-sm font-medium">{payment.job}</div>
                  <div className="col-span-4 text-sm font-medium text-right">{payment.amount}</div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">{payment.provider}</div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className={cn(
                      "text-xs",
                      payment.status === "Paid" 
                        ? "bg-green-100 text-green-800 hover:bg-green-100"
                        : "bg-red-100 text-red-800 hover:bg-red-100"
                    )}>
                      {payment.status}
                    </Badge>
                    <Download className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
      
      <Card className="shadow-sm">
        <Button
          variant="ghost"
          onClick={() => setShowSummary(!showSummary)}
          className="flex w-full items-center justify-between p-4 h-auto"
        >
          <div className="flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-muted-foreground" />
            <span className="font-medium">Payment Summary</span>
          </div>
          <ChevronDown className={cn(
            "h-5 w-5 text-muted-foreground transition-transform",
            showSummary ? "transform rotate-180" : ""
          )} />
        </Button>
        
        {showSummary && (
          <CardContent className="pt-0">
            <Separator className="mb-4" />
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="text-sm text-muted-foreground">Total Spending</div>
                <div className="text-2xl font-bold">${totalSpending.toFixed(2)}</div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="text-sm text-muted-foreground">Transactions</div>
                <div className="text-xl font-medium">{payments.length}</div>
              </div>
              
              <div className="pt-4 border-t">
                <div className="text-sm font-medium mb-2">Payment Methods</div>
                <div className="flex items-center gap-2 bg-gray-50 p-2 rounded">
                  <CreditCard className="h-4 w-4" />
                  <span className="text-sm">Visa ending in 4242</span>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}
