
import React from 'react';
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { 
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
  CardDescription
} from "@/components/ui/card";
import { 
  Lock, 
  Bell, 
  Mail, 
  MessageSquare, 
  CreditCard, 
  ShieldAlert,
  AlertTriangle,
  ChevronRight,
  User
} from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { AvatarUploader } from "@/components/AvatarUploader";
import { useAuth } from "@/features/auth/hooks/useAuth";

export function MobileCustomerSettings() {
  const { toast } = useToast();
  const { user, updateAvatar } = useAuth();
  const [openSection, setOpenSection] = React.useState<string | null>("profile");

  const handleSavePassword = () => {
    toast({
      title: "Password Updated",
      description: "Your password has been changed successfully.",
    });
  };

  const handleSaveNotifications = () => {
    toast({
      title: "Preferences Saved",
      description: "Your notification preferences have been updated.",
    });
  };

  const handleSavePrivacy = () => {
    toast({
      title: "Privacy Settings Saved",
      description: "Your privacy settings have been updated.",
    });
  };

  const handleDeactivateAccount = () => {
    // In a real app, this would show a confirmation dialog first
    toast({
      title: "Account Deactivated",
      description: "Your account has been temporarily deactivated.",
      variant: "destructive",
    });
  };

  const toggleSection = (section: string) => {
    setOpenSection(openSection === section ? null : section);
  };

  return (
    <div className="pb-20 space-y-5">
      {/* Profile Section */}
      <Collapsible
        open={openSection === "profile"}
        onOpenChange={() => toggleSection("profile")}
        className="border rounded-lg overflow-hidden"
      >
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-gradient-to-r from-white to-blue-50 dark:from-gray-900 dark:to-gray-800">
          <div className="flex items-center gap-3">
            <div className="bg-green-100 p-2 rounded-full">
              <User className="h-5 w-5 text-green-600" />
            </div>
            <h3 className="font-medium text-lg">Profile</h3>
          </div>
          <ChevronRight className={`h-5 w-5 transition-transform ${openSection === "profile" ? "rotate-90" : ""}`} />
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="p-4 space-y-6">
            <div className="flex justify-center">
              <AvatarUploader
                currentAvatar={user?.avatar}
                userName={user?.name}
                onAvatarUpdate={updateAvatar}
              />
            </div>
            
            <Separator />
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="fullName">Full Name</Label>
                <Input id="fullName" defaultValue={user?.name || ''} className="border-gray-300" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" defaultValue={user?.email || ''} className="border-gray-300" />
              </div>
            </div>
            
            <Button className="w-full">Save Profile</Button>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Account Section */}
      <Collapsible
        open={openSection === "account"}
        onOpenChange={() => toggleSection("account")}
        className="border rounded-lg overflow-hidden"
      >
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-gradient-to-r from-white to-blue-50 dark:from-gray-900 dark:to-gray-800">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-2 rounded-full">
              <Lock className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="font-medium text-lg">Account</h3>
          </div>
          <ChevronRight className={`h-5 w-5 transition-transform ${openSection === "account" ? "rotate-90" : ""}`} />
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="p-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="currentPassword">Current Password</Label>
              <Input id="currentPassword" type="password" className="border-gray-300" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="newPassword">New Password</Label>
              <Input id="newPassword" type="password" className="border-gray-300" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <Input id="confirmPassword" type="password" className="border-gray-300" />
            </div>
            
            <Button onClick={handleSavePassword} className="w-full">Update Password</Button>
            
            <Separator className="my-4" />
            
            <h4 className="font-medium text-base flex items-center gap-2 mb-2">
              <CreditCard className="h-4 w-4" /> Payment Methods
            </h4>
            
            <div className="border rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-gray-100 p-1.5 rounded">
                    <CreditCard className="h-4 w-4" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Visa •••• 4242</p>
                    <p className="text-xs text-muted-foreground">Expires 04/2026</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <Button variant="outline" className="w-full">
              <CreditCard className="h-4 w-4 mr-2" /> Add New Payment Method
            </Button>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Notifications Section */}
      <Collapsible
        open={openSection === "notifications"}
        onOpenChange={() => toggleSection("notifications")}
        className="border rounded-lg overflow-hidden"
      >
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-gradient-to-r from-white to-blue-50 dark:from-gray-900 dark:to-gray-800">
          <div className="flex items-center gap-3">
            <div className="bg-indigo-100 p-2 rounded-full">
              <Bell className="h-5 w-5 text-indigo-600" />
            </div>
            <h3 className="font-medium text-lg">Notifications</h3>
          </div>
          <ChevronRight className={`h-5 w-5 transition-transform ${openSection === "notifications" ? "rotate-90" : ""}`} />
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="p-4 space-y-4">
            <h4 className="font-medium text-base flex items-center gap-2">
              <Mail className="h-4 w-4" /> Email Notifications
            </h4>
            <Separator className="my-2" />
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="mb-0.5 block" htmlFor="email-new-bids">New Bids</Label>
                  <p className="text-xs text-muted-foreground">Get notified when providers bid on your job</p>
                </div>
                <Switch id="email-new-bids" defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="mb-0.5 block" htmlFor="email-messages">New Messages</Label>
                  <p className="text-xs text-muted-foreground">Get notified about new messages</p>
                </div>
                <Switch id="email-messages" defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="mb-0.5 block" htmlFor="email-appointments">Appointment Reminders</Label>
                  <p className="text-xs text-muted-foreground">Get reminders about scheduled appointments</p>
                </div>
                <Switch id="email-appointments" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="mb-0.5 block" htmlFor="email-marketing">Marketing & Promotions</Label>
                  <p className="text-xs text-muted-foreground">Receive special offers and promotions</p>
                </div>
                <Switch id="email-marketing" />
              </div>
            </div>

            <h4 className="font-medium text-base flex items-center gap-2 mt-6">
              <MessageSquare className="h-4 w-4" /> SMS Notifications
            </h4>
            <Separator className="my-2" />
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="mb-0.5 block" htmlFor="sms-new-bids">New Bids</Label>
                  <p className="text-xs text-muted-foreground">Receive text when providers bid on your job</p>
                </div>
                <Switch id="sms-new-bids" />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="mb-0.5 block" htmlFor="sms-messages">New Messages</Label>
                  <p className="text-xs text-muted-foreground">Get text alerts for new messages</p>
                </div>
                <Switch id="sms-messages" />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="mb-0.5 block" htmlFor="sms-appointments">Appointment Reminders</Label>
                  <p className="text-xs text-muted-foreground">Get text reminders about appointments</p>
                </div>
                <Switch id="sms-appointments" defaultChecked />
              </div>
            </div>
            
            <Button onClick={handleSaveNotifications} className="w-full mt-2">Save Preferences</Button>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Privacy & Security Section */}
      <Collapsible
        open={openSection === "privacy"}
        onOpenChange={() => toggleSection("privacy")}
        className="border rounded-lg overflow-hidden"
      >
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-gradient-to-r from-white to-blue-50 dark:from-gray-900 dark:to-gray-800">
          <div className="flex items-center gap-3">
            <div className="bg-purple-100 p-2 rounded-full">
              <ShieldAlert className="h-5 w-5 text-purple-600" />
            </div>
            <h3 className="font-medium text-lg">Privacy & Security</h3>
          </div>
          <ChevronRight className={`h-5 w-5 transition-transform ${openSection === "privacy" ? "rotate-90" : ""}`} />
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="mb-0.5 block" htmlFor="profile-visibility">Profile Visibility</Label>
                <p className="text-xs text-muted-foreground">Allow providers to see your profile info</p>
              </div>
              <Switch id="profile-visibility" defaultChecked />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label className="mb-0.5 block" htmlFor="data-collection">Data Collection</Label>
                <p className="text-xs text-muted-foreground">Allow us to collect usage data</p>
              </div>
              <Switch id="data-collection" defaultChecked />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label className="mb-0.5 block" htmlFor="third-party">Third-Party Sharing</Label>
                <p className="text-xs text-muted-foreground">Allow sharing with trusted partners</p>
              </div>
              <Switch id="third-party" />
            </div>
            
            <Button onClick={handleSavePrivacy} className="w-full mt-2">Save Privacy Settings</Button>
            
            <Separator className="my-4" />
            
            <Card className="border-red-100">
              <CardHeader className="pb-2">
                <CardTitle className="text-red-500 text-lg flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Account Actions
                </CardTitle>
                <CardDescription>Manage your account status</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-0">
                <div className="space-y-2">
                  <h3 className="font-medium">Deactivate Account</h3>
                  <p className="text-xs text-muted-foreground">
                    Temporarily deactivate your account. You can reactivate by signing back in.
                  </p>
                  <Button 
                    variant="outline" 
                    className="text-amber-500 border-amber-200 hover:bg-amber-50 w-full"
                    onClick={handleDeactivateAccount}
                  >
                    Deactivate Account
                  </Button>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">Delete Account</h3>
                  <p className="text-xs text-muted-foreground">
                    Permanently delete your account and all data. This cannot be undone.
                  </p>
                  <Button variant="outline" className="text-red-500 border-red-200 hover:bg-red-50 w-full">
                    Delete Account
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
