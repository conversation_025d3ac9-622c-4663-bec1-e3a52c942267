
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "./ui/button";
import { CheckCircle } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface ServiceNeedProps {
  icon: React.ReactNode;
  label: string;
  serviceId: string;
  estimates: Array<{
    tier: string;
    price: string;
    description: string;
    features: string[];
  }>;
}

const ServiceNeed: React.FC<ServiceNeedProps> = ({ icon, label, serviceId, estimates }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const isMobile = useIsMobile();

  // Function to simplify service labels by removing redundant words
  const getSimplifiedLabel = (fullLabel: string) => {
    // For the Cleaning service page, remove redundant words like "Cleaning"
    if (serviceId === "cleaning" && fullLabel.includes("Cleaning")) {
      return fullLabel.replace(" Cleaning", "");
    }
    return fullLabel;
  };

  const simplifiedLabel = getSimplifiedLabel(label);

  return (
    <>
      <div 
        className="flex flex-col items-center justify-center p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors group cursor-pointer"
        onClick={() => setIsDialogOpen(true)}
      >
        <div className="flex items-center justify-center w-full">
          <div className={`${isMobile ? 'w-10 h-10' : 'w-16 h-16'} rounded-full bg-primary/10 flex items-center justify-center mb-1 group-hover:bg-primary/20 transition-colors`}>
            {React.cloneElement(icon as React.ReactElement, { 
              className: `${isMobile ? 'h-4 w-4' : 'h-6 w-6'} text-primary`, 
              strokeWidth: 1.5 
            })}
          </div>
        </div>
        <span className={`${isMobile ? 'text-xs' : 'text-sm'} text-center line-clamp-2 text-black dark:text-white`}>
          {simplifiedLabel}
        </span>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold flex items-center gap-2">
              {icon && React.cloneElement(icon as React.ReactElement, { 
                className: "h-6 w-6 text-primary", 
                strokeWidth: 1.5 
              })}
              <span>{label} Service Estimate</span>
            </DialogTitle>
            <DialogDescription>
              Here's our pricing estimate for {label.toLowerCase()} services
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 my-4">
            {estimates.map((estimate, index) => (
              <div 
                key={index} 
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-semibold text-lg">{estimate.tier}</h4>
                    <p className="text-sm text-gray-500">{estimate.description}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-primary">{estimate.price}</p>
                  </div>
                </div>
                
                <div className="mt-4 space-y-2">
                  {estimate.features.slice(0, 3).map((feature, i) => (
                    <div key={i} className="flex items-start text-sm">
                      <CheckCircle className="h-4 w-4 text-primary mr-2 mt-0.5 flex-shrink-0" strokeWidth={1.5} />
                      <span>{feature}</span>
                    </div>
                  ))}
                  {estimate.features.length > 3 && (
                    <div className="text-sm text-primary font-medium">
                      +{estimate.features.length - 3} more features
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Link to={`/create-job?category=${serviceId}&subcategory=${encodeURIComponent(label)}`}>
              <Button size="lg" className="w-full">
                Get Offers for {label}
              </Button>
            </Link>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

interface ServiceNeedsProps {
  serviceId: string;
  needs: Array<{
    icon: React.ReactNode;
    label: string;
  }>;
  estimates: Array<{
    tier: string;
    price: string;
    description: string;
    features: string[];
  }>;
}

export const ServiceNeeds: React.FC<ServiceNeedsProps> = ({ serviceId, needs, estimates }) => {
  const isMobile = useIsMobile();
  
  return (
    <div className="w-full">
      {isMobile ? (
        <div className="grid grid-cols-4 gap-0.5">
          {needs.slice(0, 8).map((need, i) => (
            <ServiceNeed
              key={i}
              icon={need.icon}
              label={need.label}
              serviceId={serviceId}
              estimates={estimates}
            />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-5 md:grid-cols-5 lg:grid-cols-5 gap-2 md:gap-4">
          {needs.slice(0, 10).map((need, i) => (
            <ServiceNeed 
              key={i} 
              icon={need.icon} 
              label={need.label} 
              serviceId={serviceId}
              estimates={estimates}
            />
          ))}
        </div>
      )}
    </div>
  );
};
