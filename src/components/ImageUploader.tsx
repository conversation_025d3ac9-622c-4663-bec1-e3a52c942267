import React, {useState, useRef, useEffect} from 'react';
import { Button } from "@/components/ui/button";
import { Camera, Upload, X, CameraOff, Video, Circle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

type ImageUploaderProps = {
  onImagesSelected: (images: string[], files?: File[]) => void;
  maxImages?: number;
  currentImages?: string[];
  multiple?: boolean;
  onTranscriptionComplete?: (images: string[]) => void;
  assetUuids?: string[];
  setAssetUuids?: (uuids: string[] | []) => void;
};

export const ImageUploader = ({ 
  onImagesSelected, 
  maxImages = 10,
  currentImages = [],
  multiple = true,
  onTranscriptionComplete,
  assetUuids,
  setAssetUuids
}: ImageUploaderProps) => {
  const { toast } = useToast();
  const [selectedImages, setSelectedImages] = useState<string[]>(currentImages);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [showCamera, setShowCamera] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  useEffect(() => {
    if (currentImages.length){
      setSelectedImages(currentImages)
    }
  }, [currentImages])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newImages: string[] = [];
      const newFiles: File[] = [];

      Array.from(e.target.files).forEach(file => {
        if (selectedImages.length + newImages.length < maxImages) {
          newFiles.push(file);

          const reader = new FileReader();
          reader.onload = (event) => {
            if (event.target?.result) {
              newImages.push(event.target.result.toString());

              if (newImages.length === Math.min(e.target.files!.length, maxImages - selectedImages.length)) {
                const updatedImages = [...selectedImages, ...newImages];
                const updatedFiles = [...selectedFiles, ...newFiles];
                setSelectedImages(updatedImages);
                setSelectedFiles(updatedFiles);
                onImagesSelected(updatedImages, updatedFiles);
              }
            }
          };
          reader.readAsDataURL(file);
        }
      });
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const removeImage = (indexToRemove: number) => {
    const updatedImages = selectedImages.filter((_, index) => index !== indexToRemove);
    const updatedFiles = selectedFiles.filter((_, index) => index !== indexToRemove);

    // Also update assetUuids if it exists
    if (assetUuids && setAssetUuids) {
      const updatedUuids = assetUuids.filter((_, index) => index !== indexToRemove);
      setAssetUuids(updatedUuids);
    }

    setSelectedImages(updatedImages);
    setSelectedFiles(updatedFiles);
    onImagesSelected(updatedImages, updatedFiles);

  };

  const startCamera = async () => {
    try {
      setCameraError(null);
      const mediaStream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' },
        audio: isRecording 
      });

      setStream(mediaStream);
      setShowCamera(true);

      setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream;
          videoRef.current.play().catch(e => {
            console.error('Error playing video:', e);
            toast({
              title: "Camera Error",
              description: "Unable to play video stream. Please check your permissions.",
              variant: "destructive"
            });
          });
        }
      }, 100);
    } catch (error) {
      console.error('Error accessing camera:', error);
      setCameraError("Unable to access camera. Please check your permissions.");
      toast({
        title: "Camera Error",
        description: "Unable to access camera. Please check your permissions.",
        variant: "destructive"
      });
    }
  };

  const startRecording = () => {
    if (stream && videoRef.current) {
      chunksRef.current = [];
      const mediaRecorder = new MediaRecorder(stream);

      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunksRef.current.push(e.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'video/webm' });
        const videoUrl = URL.createObjectURL(blob);
        const file = new File([blob], `video-recording-${Date.now()}.webm`, { type: 'video/webm' });
        const updatedImages = [...selectedImages, videoUrl];
        const updatedFiles = [...selectedFiles, file];
        setSelectedImages(updatedImages);
        setSelectedFiles(updatedFiles);
        onImagesSelected(updatedImages, updatedFiles);
        setIsRecording(false);
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setShowCamera(false);
    setIsRecording(false);
  };

  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;

      canvas.width = video.videoWidth || 640;
      canvas.height = video.videoHeight || 480;

      const context = canvas.getContext('2d');
      if (context) {
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        const imageDataUrl = canvas.toDataURL('image/jpeg');

        if (selectedImages.length < maxImages) {
          // Convert data URL to File object
          canvas.toBlob((blob) => {
            if (blob) {
              const file = new File([blob], `camera-capture-${Date.now()}.jpg`, { type: 'image/jpeg' });
              const updatedImages = [...selectedImages, imageDataUrl];
              const updatedFiles = [...selectedFiles, file];
              setSelectedImages(updatedImages);
              setSelectedFiles(updatedFiles);
              onImagesSelected(updatedImages, updatedFiles);
              toast({
                title: "Photo Captured",
                description: "Your photo has been added to the gallery.",
              });
            }
          }, 'image/jpeg');
        }
      }
    }
  };

  return (
    <div className="w-full">
      <input 
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        multiple={multiple}
        className="hidden"
      />

      {showCamera ? (
        <div className="relative bg-black w-full rounded-lg overflow-hidden">
          <video 
            ref={videoRef} 
            autoPlay 
            playsInline
            className="w-full h-[300px] object-cover rounded-lg"
          />
          <canvas ref={canvasRef} className="hidden" />

          {cameraError && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900 text-white p-4 text-center">
              <CameraOff className="h-12 w-12 mb-4 text-red-500" />
              <p>{cameraError}</p>
              <Button 
                onClick={stopCamera}
                className="mt-4"
                variant="secondary"
              >
                Close Camera
              </Button>
            </div>
          )}

          <div className="absolute bottom-3 left-0 right-0 flex justify-center space-x-3">
            {!isRecording && (
              <Button 
                onClick={capturePhoto}
                className="rounded-full bg-white border border-gray-300 shadow-lg"
                size="icon"
                type="button"
              >
                <Camera className="h-5 w-5 text-gray-800" />
              </Button>
            )}

            <Button 
              onClick={isRecording ? stopRecording : startRecording}
              className={cn(
                "rounded-full border border-gray-300 shadow-lg",
                isRecording ? "bg-red-500 hover:bg-red-600" : "bg-white"
              )}
              size="icon"
              type="button"
            >
              <Video className={cn("h-5 w-5", isRecording ? "text-white" : "text-gray-800")} />
            </Button>

            <Button 
              onClick={stopCamera}
              className="rounded-full bg-white border border-gray-300 shadow-lg"
              variant="outline"
              size="icon"
              type="button"
            >
              <X className="h-5 w-5 text-gray-800" />
            </Button>
          </div>
        </div>
      ) : (
        <div className={cn("grid grid-cols-2 gap-2 mt-2", selectedImages.length > 0 && "mb-2")}>
          {selectedImages.length < maxImages && (
            <Button 
              onClick={openFileDialog} 
              variant="outline" 
              className="h-12 text-xs"
              type="button"
            >
              <Upload className="h-4 w-4 mr-1" />
              Upload
            </Button>
          )}

          {selectedImages.length < maxImages && (
            <Button 
              onClick={startCamera} 
              variant="outline" 
              className="h-12 text-xs"
              type="button"
            >
              <Camera className="h-4 w-4 mr-1" />
              Camera
            </Button>
          )}
        </div>
      )}

      {selectedImages.length > 0 && (
        <div className="grid grid-cols-5 gap-2 my-2">
          {selectedImages.map((image, index) => (
            <div key={index} className="relative group">
              <img 
                src={image} 
                alt={`Selected ${index}`} 
                className="w-full h-16 object-cover rounded"
              />
              <button
                onClick={() => removeImage(index)}
                className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-0.5 opacity-0 group-hover:opacity-100 transition-opacity"
                type="button"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
