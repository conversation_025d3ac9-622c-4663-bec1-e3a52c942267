
import React, { useEffect } from 'react';
import { Navbar } from './Navbar';
import Footer from './Footer';
import { Toaster } from './ui/toaster';
import { MobileNavigation } from './MobileNavigation';
import { useIsMobile } from '@/hooks/use-mobile';
import { useLocation } from 'react-router-dom';
import { MobileFooter } from './MobileFooter';

interface LayoutProps {
  children: React.ReactNode;
  hideNav?: boolean;
  hideMobileNav?: boolean;
  fullWidth?: boolean;
  className?: string;
}

export const Layout = ({ 
  children, 
  hideNav = false, 
  hideMobileNav = false,
  fullWidth = false,
  className = ''
}: LayoutProps) => {
  const isMobile = useIsMobile();
  const location = useLocation();
  const isMessagesPage = location.pathname === '/messages';
  const isProfessionalsPage = location.pathname.includes('/professionals');
  const isCreateJobFlow = location.pathname === '/create-job';

  useEffect(() => {
    // Scroll to top on page load
    window.scrollTo(0, 0);
  }, []);

  // Determine if we should show the mobile footer
  const showMobileFooter = isMobile && isProfessionalsPage;

  // Show navbar on desktop for create-job, hide on mobile
  const showNavbar = !hideNav && (!isCreateJobFlow || (isCreateJobFlow && !isMobile));

  // Add top padding for mobile when navbar is present
  const needsTopPadding = showNavbar && isMobile;

  return (
    <div className={`flex flex-col min-h-screen ${className}`}>
      {showNavbar && <Navbar />}

      <main className={`${fullWidth ? 'flex-1' : 'container mx-auto flex-1 px-4'} ${className} ${isMessagesPage ? 'messages-page-main' : ''} ${isCreateJobFlow && !isMobile ? 'pt-20' : ''} ${needsTopPadding ? 'pt-16' : ''} pb-16`}>
        {children}
      </main>

      <Toaster />

      {isMobile ? (
        <>
          {!hideMobileNav && <MobileNavigation />}
          {/*{showMobileFooter && <MobileFooter />}*/}
        </>
      ) : (
        <Footer />
      )}
    </div>
  );
};
