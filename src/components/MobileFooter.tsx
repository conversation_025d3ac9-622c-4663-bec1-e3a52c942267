
import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Home, Search, PlusCircle, Bookmark, User } from 'lucide-react';
import { setPreviousPage } from '@/utils/navigationUtils';
import { useAuth } from '@/features/auth/hooks/useAuth';

export const MobileFooter = () => {
  const location = useLocation();
  const pathname = location.pathname;
  const { isAuthenticated } = useAuth();

  const isProRoute = pathname.includes('/professionals') || pathname.includes('/solar') || 
                      pathname.includes('/hvac') || pathname.includes('/handyman') ||
                      pathname.includes('/landscaping') || pathname.includes('/roofing') ||
                      pathname.includes('/appliance');

  return (
    <div className={`fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 py-2 z-50 px-4 h-20 ${isProRoute ? 'block' : 'hidden md:hidden'}`}>
      <div className="flex justify-between items-center h-full">
        <NavItem 
          icon={Home} 
          label="Home" 
          to="/" 
          active={pathname === '/'} 
        />
        <NavItem 
          icon={Search} 
          label="Explore" 
          to="/services" 
          active={pathname === '/services'} 
        />
        <PostJobButton />
        <NavItem 
          icon={Bookmark} 
          label="Saved" 
          to="/saved" 
          active={pathname === '/saved'} 
        />
        <ProfileNavItem 
          active={pathname === '/profile'} 
          isAuthenticated={isAuthenticated}
        />
      </div>
    </div>
  );
};

interface NavItemProps {
  icon: React.ComponentType<any>;
  label: string;
  to: string;
  active: boolean;
}

const NavItem = ({ icon: Icon, label, to, active }: NavItemProps) => {
  return (
    <Link to={to} className={`flex flex-col items-center min-h-[48px] min-w-[48px] justify-center transition-all active:scale-95 ${active ? 'text-primary' : 'text-gray-500 dark:text-gray-400'}`}>
      <div className={`${active ? 'scale-110 transition-transform' : ''}`}>
        <Icon size={20} />
      </div>
      <span className="text-xs mt-1">{label}</span>
    </Link>
  );
};

interface ProfileNavItemProps {
  active: boolean;
  isAuthenticated: boolean;
}

const ProfileNavItem = ({ active, isAuthenticated }: ProfileNavItemProps) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (isAuthenticated) {
      navigate('/profile');
    } else {
      // Store current page for redirection after login
      setPreviousPage();
      // Redirect to auth page
      navigate('/auth');
    }
  };

  return (
    <button 
      onClick={handleClick}
      className={`flex flex-col items-center min-h-[48px] min-w-[48px] justify-center transition-all active:scale-95 ${active ? 'text-primary' : 'text-gray-500 dark:text-gray-400'}`}
    >
      <div className={`${active ? 'scale-110 transition-transform' : ''}`}>
        <User size={20} />
      </div>
      <span className="text-xs mt-1">Profile</span>
    </button>
  );
};

const PostJobButton = () => {
  const navigate = useNavigate();

  const handleClick = () => {
    setPreviousPage(); // Set the previous page in the cookie
    navigate('/create-job'); // Navigate to create-job page
  };

  return (
    <div 
      onClick={handleClick}
      className="flex flex-col items-center justify-center relative cursor-pointer min-h-[48px] min-w-[48px] transition-all active:scale-95"
    >
      <div className="bg-primary text-white rounded-full p-3 -mt-8 shadow-lg border-4 border-white dark:border-gray-900">
        <PlusCircle className="h-6 w-6" />
      </div>
      <span className="text-xs mt-3 font-medium text-primary">Post Job</span>
    </div>
  );
};
