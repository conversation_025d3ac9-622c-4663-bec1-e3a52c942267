// src/components/ProfessionalsPage/ProfessionalsPage.tsx
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Layout } from '@/components/Layout';
import { ProviderCard } from '@/components/ui/provider-card';
import { Filter, X, ChevronDown, Crown, Search, Loader2, MapPin } from 'lucide-react';
import useEmblaCarousel from 'embla-carousel-react';
import { Button } from '@/components/ui/button';
import { SEO } from '@/components/SEO';
import { useIsMobile } from '@/hooks/use-mobile';
import { getMockProviders, PaginationData } from '@/data/mockProviders';
import { TextRotate } from '@/components/ui/text-rotate';
import { motion } from 'framer-motion';
import { TrustAuthority } from '@/components/TrustAuthority';
import { Pagination } from '@/components/ui/pagination';
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from "@/components/ui/drawer";
import { ProfileDialog } from '@/components/ProfileDialog';
import { Progress } from "@/components/ui/progress";
import { useDebounce, useDebounceValue } from '@/hooks/use-debounce';
import { Badge } from '@/components/ui/badge';
import { EnhancedFilterSidebar } from '@/components/EnhancedFilterSidebar';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { DataType } from "@/types/common.ts";
import {useGeolocation} from "@/hooks/use-geolocation.ts";


// Constants
const ITEMS_PER_PAGE = 15;
const DEFAULT_ACTIVE_FILTERS = {
  searchQuery: '',
  zipCode: '',
  distance: 25,
  minRating: 0,
  availableNow: false,
  verifiedOnly: false,
  selectedServiceId: null
};
const DEFAULT_ROTATING_WORDS = ["Top-Rated", "Certified", "Professional", "Experienced", "Licensed", "Trusted", "Local"];
const DEFAULT_GRADIENT_BACKGROUND = "linear-gradient(to bottom, #fafafa 0%, #ffffff 100%)";

// Types
export interface ProfessionalsPageProps {
  serviceId: string;
  pageTitle: string;
  pageDescription: string;
  serviceName: string;
  rotatingWords?: string[];
  gradientBackground?: string;
}

interface CarouselProvidersProps {
  providers: DataType[];
  onViewProfile: (provider: DataType) => void;
}

interface ActiveFilters {
  searchQuery: string;
  zipCode: string;
  distance: number;
  minRating: number;
  availableNow: boolean;
  verifiedOnly: boolean;
  selectedServiceId: string | null;
}

// Utility functions
const calculateAverageRating = (reviews: Array<{ rating: string }>): number => {
  if (!reviews || reviews.length === 0) return 0;
  const sum = reviews.reduce((total, review) => total + parseInt(review.rating), 0);
  return parseFloat((sum / reviews.length).toFixed(1));
};

// CarouselProviders component
const CarouselProviders: React.FC<CarouselProvidersProps> = ({ providers, onViewProfile }) => {
  const [emblaRef] = useEmblaCarousel({
    align: 'start',
    containScroll: 'trimSnaps',
    dragFree: true
  });

  return (
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex gap-3">
          {providers.map(provider => (
              <div key={provider.businessId} className="flex-shrink-0 w-[230px]">
                <ProviderCard
                    provider={provider}
                    onViewProfile={() => onViewProfile(provider)}
                    className="h-full"
                />
              </div>
          ))}
        </div>
      </div>
  );
};

// Main component
export const ProfessionalsPage: React.FC<ProfessionalsPageProps> = ({serviceId, pageTitle, pageDescription, serviceName, rotatingWords = DEFAULT_ROTATING_WORDS, gradientBackground = DEFAULT_GRADIENT_BACKGROUND}) => {
  // Hooks
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const contentRef = useRef<HTMLDivElement>(null);
  const {loading } = useGeolocation();
  // Extract URL params
  const initialPage = parseInt(searchParams.get('page') || '1');
  const initialSearch = searchParams.get('search') || '';

  // State
  const [currentPage, setCurrentPage] = useState(initialPage > 0 ? initialPage : 1);
  const [providers, setProviders] = useState<DataType[]>([]);
  const [filteredProviders, setFilteredProviders] = useState<DataType[]>([]);
  const [loadedProviders, setLoadedProviders] = useState<DataType[]>([]);
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<DataType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [sortOption, setSortOption] = useState('recommended');
  const [localSearchQuery, setLocalSearchQuery] = useState(initialSearch);
  const [pagination, setPagination] = useState<PaginationData>({
    current_page: initialPage > 0 ? initialPage : 1,
    per_page: ITEMS_PER_PAGE,
    total: 0,
    last_page: 1
  });
  // State for mobile sections
  const [topRatedProviders, setTopRatedProviders] = useState<DataType[]>([]);
  const [fastestResponseProviders, setFastestResponseProviders] = useState<DataType[]>([]);
  const [activeFilters, setActiveFilters] = useState<ActiveFilters>({
    ...DEFAULT_ACTIVE_FILTERS,
    searchQuery: initialSearch,
    selectedServiceId: serviceId
  });
  const [loadingTopRatedProviders, setLoadingTopRatedProviders] = useState(true);
  const [loadingFastestResponseProviders, setLoadingFastestResponseProviders] = useState(true);

  // Derived state
  const debouncedSearchQuery = useDebounceValue(localSearchQuery, 300);
  const [isSearching, setIsSearching] = useState(false);
  const totalPages = useMemo(() => pagination.last_page, [pagination.last_page]);

  // Transform provider data
  const convertData = useCallback((providers: DataType[], serviceId: string) => {
    return providers
        .filter(provider => provider?.address?.length > 0)
        .map((provider, index) => {
          return {
            ...provider,
            isFeatured: index % 3 === 0,
            badges: provider.badges || ["verified", "fully_vetted", "background_checked"],
            specialty: provider.category || serviceId,
            key: provider.businessId,
            rating: provider.reviews?.length ? calculateAverageRating(provider.reviews) : 4.5 + Math.random() * 0.5,
            reviewCount: provider.reviews?.length || Math.floor(Math.random() * 50) + 5,
            distance: provider.distance || `${(Math.random() * 10).toFixed(1)} miles`,
            available: provider.available !== undefined ? provider.available : Math.random() > 0.3,
          };
        });
  }, []);

  // Data fetching
  const fetchData = useCallback(async (page = 1, isLoadMore = false) => {
    if (isLoadMore) {
      setIsLoadingMore(true);
    } else {
      setIsLoading(true);
    }

    try {
      const hasSearch = !!activeFilters.searchQuery;
      const baseOptions = {
        page,
        zip_code: activeFilters.zipCode,
      };

      const response = await getMockProviders(serviceId, baseOptions);
      const newProviders = convertData(response.data || [], serviceId);

      if (isLoadMore) {
        // Append new providers for "Load more" functionality
        setLoadedProviders(prev => [...prev, ...newProviders]);
        setProviders(prev => [...prev, ...newProviders]);
        setFilteredProviders(prev => [...prev, ...newProviders]);
      } else {
        setLoadedProviders(newProviders);
        setProviders(newProviders);
        setFilteredProviders(newProviders);
      }

      setPagination(response.pagination);

      if (currentPage !== response.pagination.current_page && !isLoadMore) {
        setCurrentPage(response.pagination.current_page);
        setSearchParams(prev => {
          const newParams = new URLSearchParams(prev);
          newParams.set('page', response.pagination.current_page.toString());
          if (hasSearch) newParams.set('search', activeFilters.searchQuery);
          return newParams;
        });
      }
    } catch (error) {
      if (!isLoadMore) {
        setProviders([]);
        setFilteredProviders([]);
        setLoadedProviders([]);
      }
    } finally {
      if (isLoadMore) {
        setIsLoadingMore(false);
      } else {
        setIsLoading(false);
      }
    }
  }, [serviceId, currentPage, setSearchParams, activeFilters.searchQuery, activeFilters.zipCode, convertData]);

  // Fetch data for Top-Rated Pros section (page 2, 25 items)
  const fetchTopRatedProviders = useCallback(async () => {
    setLoadingTopRatedProviders(true);
    try {
      const options = {
        page: 2,
        per_page: 25,
        zip_code: activeFilters.zipCode,
      };

      const response = await getMockProviders(serviceId, options);
      const newProviders = convertData(response.data || [], serviceId);
      setTopRatedProviders(newProviders);
      setLoadingTopRatedProviders(false);
    } catch (error) {
      console.error("Error fetching top rated providers:", error);
      setTopRatedProviders([]);
      setLoadingTopRatedProviders(false);
    }
  }, [serviceId, activeFilters.zipCode, convertData]);

  // Fetch data for Fastest Response Pros section (page 4, 25 items)
  const fetchFastestResponseProviders = useCallback(async () => {
    setLoadingFastestResponseProviders(true);
    try {
      const options = {
        page: 3,
        per_page: 25,
        zip_code: activeFilters.zipCode,
      };

      const response = await getMockProviders(serviceId, options);
      const newProviders = convertData(response.data || [], serviceId);
      setFastestResponseProviders(newProviders);
      setLoadingFastestResponseProviders(false);
    } catch (error) {
      console.error("Error fetching fastest response providers:", error);
      setFastestResponseProviders([]);
      setLoadingFastestResponseProviders(false);
    }
  }, [serviceId, activeFilters.zipCode, convertData]);

  // Filtering function
  const debouncedFilterFunction = useCallback((filterValues: ActiveFilters) => {
    if (providers.length === 0) return;
    if (!isLoading) setIsLoading(true);

    let filtered = [...providers];
    const { searchQuery, distance, minRating, availableNow, verifiedOnly } = filterValues;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(provider =>
          provider.category?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Distance radius filter
    if (distance < 25) {
      filtered = filtered.filter(provider => {
        const providerDistance = parseFloat(provider.distance || "25");
        return providerDistance <= distance;
      });
    }

    // Rating filter
    if (minRating > 0) {
      filtered = filtered.filter(provider => (provider.rating || 0) >= minRating);
    }

    // Available now filter
    if (availableNow) {
      filtered = filtered.filter(provider => provider.available);
    }

    // Verified only filter
    if (verifiedOnly) {
      filtered = filtered.filter(provider => provider?.badges?.includes('verified'));
    }

    // Reset to page 1 and update URL
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');

      if (filterValues.searchQuery) {
        newParams.set('search', filterValues.searchQuery);
      } else {
        newParams.delete('search');
      }

      return newParams;
    });

    // Update filtered providers
    setFilteredProviders(filtered);
    setIsLoading(false);
  }, [providers, isLoading, setSearchParams]);

  // Debounced filtering
  const debouncedFilter = useDebounce(debouncedFilterFunction, 300);

  // Event handlers
  const handleFilterChange = useCallback((filterValues: Partial<ActiveFilters>) => {
    if ('searchQuery' in filterValues) {
      setLocalSearchQuery(filterValues.searchQuery || '');

      // Remove searchQuery from filterValues to prevent double processing
      const { searchQuery, ...otherFilters } = filterValues;

      // Only update other filters immediately
      if (Object.keys(otherFilters).length > 0) {
        setActiveFilters(prev => ({
          ...prev,
          ...otherFilters
        }));
        debouncedFilter({
          ...activeFilters,
          ...otherFilters
        });
      }
    } else {
      setActiveFilters(prev => ({
        ...prev,
        ...filterValues
      }));
      debouncedFilter({
        ...activeFilters,
        ...filterValues
      });
    }
  }, [debouncedFilter, activeFilters]);

  const handleOpenProfile = useCallback((provider: DataType) => {
    setSelectedProvider(provider);
    setIsProfileOpen(true);
  }, []);

  const handleCloseProfile = useCallback(() => {
    setSelectedProvider(null);
    setIsProfileOpen(false);
  }, []);

  const handleCloseFilterDrawer = useCallback(() => {
    setFilterDrawerOpen(false);
  }, []);

  const handleSortChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSortOption = e.target.value;
    setSortOption(newSortOption);

    // Reset to page 1 and update URL
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');

      if (activeFilters.searchQuery) {
        newParams.set('search', activeFilters.searchQuery);
      }

      return newParams;
    });

  }, [setSearchParams, activeFilters.searchQuery]);

  const handleServiceChange = useCallback((newServiceId: string) => {
    // Navigate to the new service page
    if (newServiceId !== serviceId) {
      navigate(`/professionals/${newServiceId}`);
    }
  }, [navigate, serviceId]);

  const clearFilter = useCallback((filterKey: string) => {
    if (filterKey === 'all') {
      // Clear all filters
      setLocalSearchQuery('');
      const resetFilters = {
        ...DEFAULT_ACTIVE_FILTERS,
        selectedServiceId: serviceId
      };

      setActiveFilters(resetFilters);
      debouncedFilter(resetFilters);
    } else {
      // Clear specific filter
      const updatedFilters = { ...activeFilters };

      if (filterKey === 'searchQuery') {
        setLocalSearchQuery('');
        updatedFilters.searchQuery = '';
      }
      if (filterKey === 'zipCode') updatedFilters.zipCode = '';
      if (filterKey === 'distance') updatedFilters.distance = 25;
      if (filterKey === 'minRating') updatedFilters.minRating = 0;
      if (filterKey === 'availableNow') updatedFilters.availableNow = false;
      if (filterKey === 'verifiedOnly') updatedFilters.verifiedOnly = false;

      setActiveFilters(updatedFilters);
      debouncedFilter(updatedFilters);
    }
  }, [activeFilters, debouncedFilter, serviceId]);

  // Update search state when debounced query changes
  useEffect(() => {
    setIsSearching(localSearchQuery !== debouncedSearchQuery);

    if (debouncedSearchQuery !== activeFilters.searchQuery) {
      setActiveFilters(prev => ({
        ...prev,
        searchQuery: debouncedSearchQuery
      }));

      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        if (debouncedSearchQuery) {
          newParams.set('search', debouncedSearchQuery);
        } else {
          newParams.delete('search');
        }
        return newParams;
      });
    }
  }, [debouncedSearchQuery, localSearchQuery, activeFilters.searchQuery, setSearchParams]);

  const fetchAllProviders = useCallback(async () => {
    try {
      await Promise.all([
        fetchTopRatedProviders(),
        fetchFastestResponseProviders()
      ]);
    } catch (error) {
      console.error("Error fetching providers:", error);
    }
  }, [fetchTopRatedProviders, fetchFastestResponseProviders]);

  // Fetch data when zipCode changes
  useEffect(() => {
    const debounceTimer = setTimeout(async () => {
      if (!loading) {
        await fetchData(currentPage);
        if(isMobile && activeFilters.zipCode.length){
          await fetchAllProviders()
        }
      }
    }, activeFilters.zipCode.length ? 1000 : 0);
    return () => clearTimeout(debounceTimer);
  }, [activeFilters.zipCode, currentPage, fetchData, isMobile, fetchAllProviders, loading]);

  // Scroll to top when page changes
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  }, [currentPage]);

  // Handle case where current page is greater than total pages
  useEffect(() => {
    if (totalPages > 0 && currentPage > totalPages) {
      setCurrentPage(totalPages);
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set('page', totalPages.toString());

        if (activeFilters.searchQuery) {
          newParams.set('search', activeFilters.searchQuery);
        }

        return newParams;
      });
    }
  }, [totalPages, currentPage, setSearchParams, activeFilters.searchQuery]);

  // Memoized values
  const paginatedProviders = useMemo(() => {
    return isMobile ? loadedProviders : filteredProviders;
  }, [filteredProviders, loadedProviders, isMobile]);

  const hasActiveFilters = useMemo(() => {
    return localSearchQuery !== '' ||
        activeFilters.zipCode !== '' ||
        activeFilters.distance < 25 ||
        activeFilters.minRating > 0 ||
        activeFilters.availableNow ||
        activeFilters.verifiedOnly;
  }, [localSearchQuery, activeFilters]);

  // Render
  return (
      <Layout>
        <SEO
            title={pageTitle}
            description={pageDescription}
            localBusinessSchema={true}
            serviceType={serviceId}
            serviceSlug={serviceId}
            canonicalUrl={`/professionals/${serviceId}`}
        />

        {/* Hero Section */}
        <div className="pt-20 pb-8" style={{ background: gradientBackground }}>
          <div className="container mx-auto px-4 md:px-6">
            <div className="md:text-center mb-6 md:mb-10">
              <h1 className="text-3xl md:text-5xl font-bold mb-2 md:mb-4 text-gray-900 dark:text-white">
                {isMobile ? `Find ${serviceName} Near You` : (
                    <motion.div className="flex flex-col items-center">
                      <div className="flex items-center justify-center">
                        <span>Find </span>
                        <TextRotate
                            texts={rotatingWords}
                            mainClassName="overflow-hidden text-primary mx-3 dark:text-blue-300"
                            staggerDuration={0.03}
                            staggerFrom="last"
                            rotationInterval={3000}
                            transition={{
                              type: "spring",
                              damping: 30,
                              stiffness: 400
                            }}
                        />
                        <span>{serviceName}</span>
                      </div>
                      <span>In Your Area</span>
                    </motion.div>
                )}
              </h1>
              <p className="text-base md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Browse through our network of trusted {serviceName.toLowerCase()} for all your {serviceId} needs.
              </p>
            </div>
          </div>
        </div>

        {/* Location Section */}
        <div className="mb-4 py-3 bg-slate-100">
          <div className="container mx-auto px-4">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-2">
              <div className="flex items-center text-primary gap-2">
                <MapPin className="h-5 w-5" />
                <span className="font-medium">Your Location:</span>
              </div>
              <div className="flex items-center gap-2">
                <input
                    type="text"
                    placeholder="Enter your ZIP code"
                    value={activeFilters.zipCode}
                    onChange={e => handleFilterChange({ zipCode: e.target.value })}
                    className="px-3 py-2 border rounded-md text-sm w-40"
                />
                <Button
                    size="sm"
                    className="whitespace-nowrap"
                >
                  Update Location
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Filter Section */}
        {isMobile && (
            <div className="border-white dark:border-gray-700">
              <EnhancedFilterSidebar
                  currentServiceId={serviceId}
                  onFilterChange={handleFilterChange}
                  onServiceChange={handleServiceChange}
                  initialSearchQuery={initialSearch}
                  zipCode={activeFilters?.zipCode}
              />
            </div>
        )}

        {/* Mobile Top Providers Carousel */}
        {isMobile && (
            <div className="container mx-auto px-0 py-4">
              <div className="text-xl font-bold mb-2">Top-Rated Pros Near You</div>
              <div className="relative">
                { loadingTopRatedProviders?
                    <div className="text-center py-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                      <p className="text-gray-500 dark:text-gray-400">Loading...</p>
                    </div> :
                    topRatedProviders.length > 0 ? (
                    <CarouselProviders
                        providers={topRatedProviders}
                        onViewProfile={handleOpenProfile}
                    />
                ) : (
                    <div className="text-center py-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                      <p className="text-gray-500 dark:text-gray-400">No providers to display</p>
                    </div>
                )}
              </div>
            </div>
        )}

        {/* Mobile Fast Response Carousel */}
        {isMobile && (
            <div className="container mx-auto px-0 py-4">
              <div className="text-xl font-bold mb-2">Fastest Response Pros</div>
              <div className="relative">
                {loadingFastestResponseProviders ?
                    <div className="text-center py-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                      <p className="text-gray-500 dark:text-gray-400">Loading...</p>
                    </div> :
                  fastestResponseProviders.length > 0 ? (
                    <CarouselProviders
                        providers={fastestResponseProviders}
                        onViewProfile={handleOpenProfile}
                    />
                ) : (
                    <div className="text-center py-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                      <p className="text-gray-500 dark:text-gray-400">No providers to display</p>
                    </div>
                )}
              </div>
              {fastestResponseProviders.length > 0 && (
                  <p className="text-center mt-2 text-primary text-sm">Responds in 5 min</p>
              )}
            </div>
        )}

        {/* Main Content */}
        <div className="container mx-auto px-0 md:px-6 mb-8">
          <div className="flex flex-col md:flex-row">
            {/* Desktop Sidebar */}
            {!isMobile && (
                <div className="w-[260px] flex-shrink-0">
                  <div className="pr-5 border-r border-gray-200 dark:border-gray-700">
                    <EnhancedFilterSidebar
                        currentServiceId={serviceId}
                        onFilterChange={handleFilterChange}
                        onServiceChange={handleServiceChange}
                        initialSearchQuery={initialSearch}
                        zipCode={activeFilters?.zipCode}
                    />
                  </div>
                </div>
            )}

            {/* Main Content Area */}
            <div
                ref={contentRef}
                className="flex-grow max-w-[1089px] pt-3 md:pt-0 md:pl-6 px-0 py-2 mx-0 my-0"
            >
              {/* Results Header */}
              <div className="flex justify-between items-center mb-4">
                <div className="hidden md:block">
                  {pagination.total} results
                </div>

                <div className="flex items-center gap-3">
                  {isMobile && (
                      <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setFilterDrawerOpen(true)}
                          className="hidden md:flex items-center gap-1.5"
                      >
                        <Filter className="h-4 w-4" />
                        Filters
                      </Button>
                  )}

                  <div className="relative hidden md:block">
                    <select
                        className="appearance-none bg-transparent border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1.5 pr-8 text-gray-800 dark:text-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                        onChange={handleSortChange}
                        value={sortOption}
                    >
                      <option value="recommended">Recommended</option>
                      <option value="rating">Highest Rated</option>
                    </select>
                    <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 pointer-events-none" />
                  </div>
                </div>
              </div>

              {/* Active Filters */}
              {hasActiveFilters && (
                  <div className="mb-4 hidden md:flex flex-wrap gap-2">
                    {localSearchQuery && (
                        <Badge variant="secondary" className="text-xs flex items-center">
                          {isSearching ? (
                              <>
                                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                Searching: {localSearchQuery}
                              </>
                          ) : (
                              <>
                                <Search className="h-3 w-3 mr-1" />
                                Search: {localSearchQuery}
                              </>
                          )}
                          <button className="ml-1 text-gray-500" onClick={() => clearFilter('searchQuery')}>×</button>
                        </Badge>
                    )}

                    {activeFilters.zipCode && (
                        <Badge variant="secondary" className="text-xs">
                          ZIP: {activeFilters.zipCode}
                          <button className="ml-1 text-gray-500" onClick={() => clearFilter('zipCode')}>×</button>
                        </Badge>
                    )}

                    {activeFilters.distance < 25 && (
                        <Badge variant="secondary" className="text-xs">
                          ≤ {activeFilters.distance} miles
                          <button className="ml-1 text-gray-500" onClick={() => clearFilter('distance')}>×</button>
                        </Badge>
                    )}

                    {activeFilters.minRating > 0 && (
                        <Badge variant="secondary" className="text-xs flex items-center">
                          {activeFilters.minRating}+ stars
                          <button className="ml-1 text-gray-500" onClick={() => clearFilter('minRating')}>×</button>
                        </Badge>
                    )}

                    {activeFilters.availableNow && (
                        <Badge variant="secondary" className="text-xs">
                          Available Now
                          <button className="ml-1 text-gray-500" onClick={() => clearFilter('availableNow')}>×</button>
                        </Badge>
                    )}

                    {activeFilters.verifiedOnly && (
                        <Badge variant="secondary" className="text-xs">
                          Verified Only
                          <button className="ml-1 text-gray-500" onClick={() => clearFilter('verifiedOnly')}>×</button>
                        </Badge>
                    )}

                    <Button variant="ghost" size="sm" className="text-xs" onClick={() => clearFilter('all')}>
                      Clear All
                    </Button>
                  </div>
              )}

              {/* Loading Indicator */}
              {isLoading && (
                  <div className="py-4">
                    <Progress value={65} className="h-1" />
                    <p className="text-sm text-gray-500 mt-2">Loading results...</p>
                  </div>
              )}

              {/* Provider Results */}
              {!isLoading && (
                  <div className="space-y-4">
                    {paginatedProviders.length === 0 ? (
                        <div className="text-center py-12 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                          <p className="text-gray-500 dark:text-gray-400">No providers match your filters</p>
                          <Button variant="outline" className="mt-4" onClick={() => clearFilter('all')}>
                            Reset Filters
                          </Button>
                        </div>
                    ) : (
                        <>
                          <div className="text-xl font-bold mb-4 block md:hidden">More Local Pros Near You</div>
                          <div className="min-h-[200px]">
                            {paginatedProviders.map(provider => (
                                <div
                                    key={provider.businessId}
                                    className={`mb-5 ${provider.isFeatured ? "relative" : ""}`}
                                >
                                  {provider.isFeatured && (
                                      <div className="absolute -top-3 left-0 right-0 flex justify-center z-10">
                              <span className="bg-amber-400 text-amber-900 px-4 py-1 rounded-full text-sm font-semibold flex items-center shadow-md">
                                <Crown className="h-4 w-4 mr-1.5" />
                                Featured Provider
                              </span>
                                      </div>
                                  )}
                                  <ProviderCard
                                      provider={provider}
                                      onViewProfile={() => handleOpenProfile(provider)}
                                      className={provider.isFeatured ? "mt-4 border-2 border-amber-300 shadow-lg shadow-amber-100" : ""}
                                  />
                                </div>
                            ))}
                          </div>
                        </>
                    )}
                  </div>
              )}

              {/* Mobile Load More Button */}
              {isMobile && pagination.current_page < pagination.last_page && !isLoading && (
                  <div className="mt-5">
                    <Button
                        variant="outline"
                        className="w-full py-2 bg-blue-100"
                        onClick={async() => {
                          await fetchData(pagination.current_page + 1, true);
                        }}
                        disabled={isLoadingMore}
                        type='button'
                    >
                      {isLoadingMore ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Loading more...
                          </>
                      ) : "Load more"}
                    </Button>
                  </div>
              )}

              {/* Desktop Pagination */}
              {!isMobile && totalPages > 1 && !isLoading && (
                  <div className="mt-8">
                    <Pagination
                        totalItems={pagination.total}
                        itemsPerPage={pagination.per_page}
                        currentPage={pagination.current_page}
                        onPageChange={async (page) => {
                          setCurrentPage(page);
                          setSearchParams(prev => {
                            const newParams = new URLSearchParams(prev);
                            newParams.set('page', page.toString());
                            if (activeFilters.searchQuery) {
                              newParams.set('search', activeFilters.searchQuery);
                            }
                            return newParams;
                          });

                          // Fetch new data for the selected page
                          await fetchData(page);
                        }}
                        className="w-full justify-center"
                    />
                  </div>
              )}

              {/* Mobile CTA */}
              {isMobile && (
                  <div className="p-4 border flex flex-col gap-3 mt-5 rounded-md border-gray-200 dark:border-gray-700">
                    <h2 className="text-lg font-bold">Get quotes for any job</h2>
                    <p className="text-sm text-gray-500">From home repairs to deep cleaning, find trusted pros near you.</p>
                    <Button
                        onClick={() => navigate('/create-job')}
                        className="group/btn w-full md:w-auto flex items-center gap-2 transition-all bg-[#2263eb] hover:bg-[#2263eb]/90 h-11 md:h-10 text-white"
                    >
                      Post a Job
                    </Button>
                  </div>
              )}

              {/* Trust Section */}
              <div className="mt-8">
                <TrustAuthority />
              </div>
            </div>
          </div>
        </div>

        {/* Profile Dialog */}
        {selectedProvider && (
            <ProfileDialog
                isOpen={isProfileOpen}
                onClose={handleCloseProfile}
                provider={selectedProvider}
            />
        )}

        {/* Mobile Filter Drawer */}
        <Drawer open={filterDrawerOpen} onOpenChange={setFilterDrawerOpen}>
          <DrawerContent className="max-h-[90%]">
            <DrawerHeader className="flex items-center justify-between">
              <DrawerTitle>Filters</DrawerTitle>
              <Button variant="ghost" size="sm" onClick={handleCloseFilterDrawer}>
                <X className="h-5 w-5" />
              </Button>
            </DrawerHeader>
            <div className="px-4 pb-6">
              <EnhancedFilterSidebar
                  currentServiceId={serviceId}
                  onFilterChange={handleFilterChange}
                  onServiceChange={handleServiceChange}
                  initialSearchQuery={initialSearch}
                  zipCode={activeFilters?.zipCode}
              />

              <div className="mt-4 pt-4 border-t">
                <Button className="w-full" onClick={handleCloseFilterDrawer}>
                  Apply Filters
                </Button>
                {hasActiveFilters && (
                    <Button
                        variant="outline"
                        className="w-full mt-2"
                        onClick={() => {
                          clearFilter('all');
                          handleCloseFilterDrawer();
                        }}
                    >
                      Clear All Filters
                    </Button>
                )}
              </div>
            </div>
          </DrawerContent>
        </Drawer>
      </Layout>
  );
};

export default ProfessionalsPage;
