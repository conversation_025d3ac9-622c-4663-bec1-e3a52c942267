
import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Provider } from './ProviderCard';
import { UserRegistrationForm } from './UserRegistrationForm';
import { isUserRegistered, saveProviderToMessage } from '@/utils/messagingUtils';
import { useToast } from '@/hooks/use-toast';

interface MessageProviderDialogProps {
  isOpen: boolean;
  onClose: () => void;
  provider: Provider | null;
}

export const MessageProviderDialog: React.FC<MessageProviderDialogProps> = ({
  isOpen,
  onClose,
  provider
}) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const handleCompleteRegistration = () => {
    if (provider) {
      // Save provider info to be accessed in the messages page
      saveProviderToMessage(provider);
      
      // Close dialog and navigate to messages
      onClose();
      
      toast({
        title: "Ready to chat",
        description: `You can now message ${provider.name}`,
      });
      
      // Navigate to messages page
      navigate('/messages');
    }
  };
  
  // Check if user is already registered
  React.useEffect(() => {
    if (isOpen && provider && isUserRegistered()) {
      handleCompleteRegistration();
    }
  }, [isOpen, provider]);
  
  if (!provider) return null;
  
  // If user is registered, don't show dialog, it will redirect automatically
  if (isUserRegistered()) return null;
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Message {provider.name}</DialogTitle>
        </DialogHeader>
        
        <UserRegistrationForm 
          onComplete={handleCompleteRegistration} 
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  );
};
