
import { useState } from "react";
import { 
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow 
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Search, MoreVertical, Eye, RefreshCw, Filter, X, Calendar, DollarSign, CreditCard } from "lucide-react";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { MobilePaymentDetailCard } from "./payments/MobilePaymentDetailCard";
import { useMediaQuery } from "@/hooks/use-media-query";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export const PaymentsManagement = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPayment, setSelectedPayment] = useState<any | null>(null);
  const [showPaymentDetails, setShowPaymentDetails] = useState(false);
  const [showMobileDetails, setShowMobileDetails] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Mock data - in a real app, this would come from an API
  const payments = [
    {
      id: "TRX-1001",
      customerName: "Sarah Johnson",
      providerName: "Mike's Home Improvement",
      amount: 450.00,
      status: "Paid",
      date: "2025-04-15",
      cardLast4: "4242",
      serviceType: "Plumbing"
    },
    {
      id: "TRX-1002",
      customerName: "David Wilson",
      providerName: "Electro Solutions",
      amount: 285.75,
      status: "Pending",
      date: "2025-04-16",
      cardLast4: "8735",
      serviceType: "Electrical"
    },
    {
      id: "TRX-1003",
      customerName: "Emily Davis",
      providerName: "Green Thumb Landscaping",
      amount: 125.00,
      status: "Paid",
      date: "2025-04-10",
      cardLast4: "9012",
      serviceType: "Landscaping"
    },
    {
      id: "TRX-1004",
      customerName: "James Miller",
      providerName: "Mike's Home Improvement",
      amount: 750.00,
      status: "Refunded",
      date: "2025-04-08",
      cardLast4: "6789",
      serviceType: "Renovation"
    },
    {
      id: "TRX-1005",
      customerName: "Lisa Parker",
      providerName: "Clean Pro Services",
      amount: 195.00,
      status: "Pending",
      date: "2025-04-18",
      cardLast4: "3456",
      serviceType: "Cleaning"
    }
  ];

  // Filter payments based on search query and status filter
  const filteredPayments = payments.filter(payment => {
    const matchesSearch = 
      payment.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.providerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.id.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || payment.status.toLowerCase() === statusFilter.toLowerCase();
    
    return matchesSearch && matchesStatus;
  });

  // Helper function for status badge styling
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "Paid":
        return <Badge variant="outline" className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-green-300">Paid</Badge>;
      case "Pending":
        return <Badge variant="outline" className="bg-gradient-to-r from-amber-400 to-yellow-500 text-white border-yellow-300">Pending</Badge>;
      case "Refunded":
        return <Badge variant="outline" className="bg-gradient-to-r from-red-400 to-rose-500 text-white border-red-300">Refunded</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleViewDetails = (payment: any) => {
    setSelectedPayment(payment);
    if (isMobile) {
      setShowMobileDetails(true);
    } else {
      setShowPaymentDetails(true);
    }
  };

  const handleRefund = (payment: any) => {
    // In a real app, this would be an API call
    toast({
      title: "Payment refunded",
      description: `Payment ${payment.id} has been refunded successfully.`,
    });
    
    // Update the payment status in the UI
    if (selectedPayment && selectedPayment.id === payment.id) {
      setSelectedPayment({
        ...selectedPayment,
        status: "Refunded"
      });
    }
  };

  // Mobile payment card component
  const MobilePaymentCard = ({ payment }: { payment: any }) => (
    <div 
      className="border rounded-lg shadow-sm mb-4 overflow-hidden animate-fade-in transition-all duration-300 hover:shadow-md bg-white dark:bg-gray-800"
      onClick={() => handleViewDetails(payment)}
    >
      <div className={`p-3 border-l-4 ${
        payment.status === "Paid" ? "border-l-green-500" : 
        payment.status === "Pending" ? "border-l-yellow-500" : 
        "border-l-red-500"
      }`}>
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium">{payment.id}</h3>
            <div className="flex items-center text-sm text-gray-500 mt-1">
              <Calendar className="h-3.5 w-3.5 mr-1.5" />
              {new Date(payment.date).toLocaleDateString()}
            </div>
          </div>
          {getStatusBadge(payment.status)}
        </div>
        
        <div className="flex justify-between items-center mt-3 mb-2">
          <div className="flex items-center text-lg font-semibold text-green-600 dark:text-green-400">
            <DollarSign className="h-5 w-5 mr-0.5" />
            {payment.amount.toFixed(2)}
          </div>
          <div className="flex items-center text-sm">
            <CreditCard className="h-4 w-4 mr-1.5 text-gray-500" />
            <span className="text-gray-600">•••• {payment.cardLast4}</span>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-y-2 text-sm mt-3 pt-3 border-t">
          <div>
            <p className="text-gray-500 text-xs">Customer</p>
            <p className="truncate font-medium">{payment.customerName}</p>
          </div>
          <div>
            <p className="text-gray-500 text-xs">Provider</p>
            <p className="truncate font-medium">{payment.providerName}</p>
          </div>
          <div className="col-span-2 mt-1">
            <p className="text-gray-500 text-xs">Service</p>
            <div className="flex items-center">
              <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                payment.serviceType === "Plumbing" ? "bg-blue-500" :
                payment.serviceType === "Electrical" ? "bg-yellow-500" :
                payment.serviceType === "Landscaping" ? "bg-green-500" :
                payment.serviceType === "Renovation" ? "bg-orange-500" :
                payment.serviceType === "Cleaning" ? "bg-indigo-500" : "bg-gray-500"
              }`}></span>
              <p className="font-medium">{payment.serviceType}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Render desktop or mobile view based on screen size
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Manage Payments</h1>
        <div className="flex items-center gap-2">
          {isMobile ? (
            <>
              <Sheet open={filterOpen} onOpenChange={setFilterOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon" className="border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700">
                    <Filter className="h-4 w-4" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="bottom" className="h-[50vh] rounded-t-xl">
                  <SheetHeader className="mb-4">
                    <SheetTitle className="text-center text-lg bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Filter Payments</SheetTitle>
                  </SheetHeader>
                  <div className="space-y-6">
                    <div>
                      <label className="text-sm font-medium mb-1 block">Search</label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Search payments..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-9 border-blue-200 focus:border-blue-400"
                        />
                        {searchQuery && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 hover:bg-blue-50"
                            onClick={() => setSearchQuery("")}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium mb-1 block">Payment Status</label>
                      <Select 
                        value={statusFilter} 
                        onValueChange={setStatusFilter}
                      >
                        <SelectTrigger className="border-blue-200 focus:border-blue-400">
                          <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="paid">Paid</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="refunded">Refunded</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="pt-4">
                      <Button 
                        className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700" 
                        onClick={() => setFilterOpen(false)}
                      >
                        Apply Filters
                      </Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
              
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search payments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9 w-full border-blue-200 focus:border-blue-400"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 hover:bg-blue-50"
                    onClick={() => setSearchQuery("")}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </>
          ) : (
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search payments..."
                className="pl-8 w-[250px] md:w-[300px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          )}
        </div>
      </div>

      {!isMobile ? (
        <Table>
          <TableCaption>A list of all payments on the platform.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Transaction ID</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Provider</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPayments.map((payment) => (
              <TableRow key={payment.id}>
                <TableCell className="font-medium">{payment.id}</TableCell>
                <TableCell>{payment.customerName}</TableCell>
                <TableCell>{payment.providerName}</TableCell>
                <TableCell>${payment.amount.toFixed(2)}</TableCell>
                <TableCell>{getStatusBadge(payment.status)}</TableCell>
                <TableCell>{new Date(payment.date).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewDetails(payment)}>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>View Details</span>
                      </DropdownMenuItem>
                      {payment.status === "Paid" && (
                        <DropdownMenuItem onClick={() => handleRefund(payment)}>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          <span>Issue Refund</span>
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="space-y-2">
          {filteredPayments.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
              <p className="text-gray-500">No payments found matching your filters.</p>
            </div>
          ) : (
            filteredPayments.map((payment, index) => (
              <div key={payment.id} className="animate-fade-in" style={{ animationDelay: `${index * 50}ms` }}>
                <MobilePaymentCard payment={payment} />
              </div>
            ))
          )}
        </div>
      )}

      {/* Payment Details Dialog (Desktop) */}
      <Dialog open={showPaymentDetails} onOpenChange={setShowPaymentDetails}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Payment Details</DialogTitle>
            <DialogDescription>
              View detailed information about this payment.
            </DialogDescription>
          </DialogHeader>
          {selectedPayment && (
            <div className="py-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Transaction ID</h3>
                  <p className="font-medium">{selectedPayment.id}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                  <p>{getStatusBadge(selectedPayment.status)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Customer</h3>
                  <p className="font-medium">{selectedPayment.customerName}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Provider</h3>
                  <p className="font-medium">{selectedPayment.providerName}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Amount</h3>
                  <p className="font-medium">${selectedPayment.amount.toFixed(2)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
                  <p className="font-medium">{new Date(selectedPayment.date).toLocaleDateString()}</p>
                </div>
              </div>

              {/* Additional payment details would be displayed here */}
              <div className="pt-4 border-t">
                <h3 className="text-sm font-medium mb-2">Payment Method</h3>
                <div className="flex items-center gap-2">
                  <div className="p-1 rounded bg-gray-100">
                    <img src="https://placehold.co/40x25?text=Card" alt="Card" className="h-6" />
                  </div>
                  <span>•••• {selectedPayment.cardLast4 || "4242"}</span>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="flex gap-2 sm:gap-0">
            {selectedPayment && selectedPayment.status === "Paid" && (
              <Button 
                variant="outline"
                className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                onClick={() => handleRefund(selectedPayment)}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Issue Refund
              </Button>
            )}
            <Button onClick={() => setShowPaymentDetails(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Mobile Payment Detail View */}
      {isMobile && selectedPayment && (
        <div className={`fixed inset-0 bg-background z-50 ${showMobileDetails ? 'block' : 'hidden'}`}>
          <MobilePaymentDetailCard
            payment={selectedPayment}
            onClose={() => setShowMobileDetails(false)}
            onRefund={handleRefund}
          />
        </div>
      )}
    </div>
  );
};
