import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { emailTrackingService, EmailTrackingStatistics } from '@/services/emailTrackingService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Pagination } from '@/components/ui/pagination';
import { DatePicker } from '@/components/ui/date-picker';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Mail, CheckCircle, MousePointer, AlertTriangle, Ban, XCircle, RefreshCw, Download, BarChart3, PieChart, Calendar, Filter, Search } from 'lucide-react';
import { format, subDays } from 'date-fns';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Status badge variants
const statusVariants: Record<string, { color: string; icon: React.ReactNode; bgColor: string }> = {
  delivered: { color: 'text-blue-600', icon: <Mail className="h-3 w-3 mr-1" />, bgColor: 'bg-blue-50' },
  opened: { color: 'text-green-600', icon: <CheckCircle className="h-3 w-3 mr-1" />, bgColor: 'bg-green-50' },
  clicked: { color: 'text-purple-600', icon: <MousePointer className="h-3 w-3 mr-1" />, bgColor: 'bg-purple-50' },
  bounced: { color: 'text-amber-600', icon: <AlertTriangle className="h-3 w-3 mr-1" />, bgColor: 'bg-amber-50' },
  spam: { color: 'text-red-600', icon: <Ban className="h-3 w-3 mr-1" />, bgColor: 'bg-red-50' },
  unsubscribed: { color: 'text-gray-600', icon: <XCircle className="h-3 w-3 mr-1" />, bgColor: 'bg-gray-50' },
};

// Stats card component with animation
const StatsCard = ({ 
  title, 
  value, 
  icon, 
  description, 
  change, 
  bgColor = 'bg-white'
}: { 
  title: string; 
  value: number; 
  icon: React.ReactNode; 
  description?: string;
  change?: number;
  bgColor?: string;
}) => (
  <Card className={`overflow-hidden transition-all duration-200 hover:shadow-md ${bgColor}`}>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <div className="rounded-full p-1.5 bg-gray-100">
        {icon}
      </div>
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {description && <p className="text-xs text-muted-foreground mt-1">{description}</p>}
      {change !== undefined && (
        <div className={`flex items-center mt-2 text-xs ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {change >= 0 ? '↑' : '↓'} {Math.abs(change)}% from previous period
        </div>
      )}
    </CardContent>
  </Card>
);

// Time period options
const timePeriods = [
  { label: 'Last 7 days', value: '7days', startDate: subDays(new Date(), 7) },
  { label: 'Last 30 days', value: '30days', startDate: subDays(new Date(), 30) },
  { label: 'Last 90 days', value: '90days', startDate: subDays(new Date(), 90) },
  { label: 'Custom range', value: 'custom', startDate: null },
] as const;

export const EmailTrackingDashboard = () => {
  const { token } = useAuth();
  const { toast } = useToast();
  
  // State for tracking statistics
  const [statistics, setStatistics] = useState<EmailTrackingStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [timePeriod, setTimePeriod] = useState('7days');
  
  // Filter states
  const [filters, setFilters] = useState<{
    type: string;
    startDate: Date | null;
    endDate: Date | null;
    notificationType: string;
    email: string;
    page: number;
    perPage: number;
  }>({
    type: 'all',
    startDate: subDays(new Date(), 7),
    endDate: new Date(),
    notificationType: 'all',
    email: '',
    page: 1,
    perPage: 10,
  });

  // Fetch email tracking statistics
  const fetchStatistics = async () => {
    if (!token) {
      toast({
        title: 'Authentication Error',
        description: 'No authentication token available',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      const response = await emailTrackingService.getEmailTrackingStatistics(
        token,
        {
          type: filters.type === 'all' ? undefined : filters.type,
          start_date: filters.startDate ? format(filters.startDate, 'yyyy-MM-dd') : undefined,
          end_date: filters.endDate ? format(filters.endDate, 'yyyy-MM-dd') : undefined,
          notification_type: filters.notificationType === 'all' ? undefined : filters.notificationType,
          email: filters.email || undefined,
          page: filters.page,
          per_page: filters.perPage,
        }
      );

      if (response.data) {
        setStatistics(response.data);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch email tracking statistics',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchStatistics();
  }, [filters.page, filters.perPage]);

  // Handle time period change
  const handleTimePeriodChange = (value: string) => {
    setTimePeriod(value);
    
    if (value !== 'custom') {
      const period = timePeriods.find(p => p.value === value);
      if (period) {
        setFilters(prev => ({
          ...prev,
          startDate: period.startDate,
          endDate: new Date(),
        }));
      }
    }
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Apply filters
  const applyFilters = () => {
    setFilters(prev => ({ ...prev, page: 1 })); // Reset to first page
    fetchStatistics();
  };

  // Reset filters
  const resetFilters = () => {
    const defaultStartDate = timePeriods.find(p => p.value === timePeriod)?.startDate ?? subDays(new Date(), 7);

    setFilters({
      type: 'all',
      startDate: defaultStartDate,
      endDate: new Date(),
      notificationType: 'all',
      email: '',
      page: 1,
      perPage: 10,
    });
    fetchStatistics();
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  // Calculate engagement rate
  const engagementRate = useMemo(() => {
    if (!statistics) return 0;
    const totalOpened = statistics.opened || 0;
    const totalDelivered = statistics.delivered || 0;
    return totalDelivered > 0 ? Math.round((totalOpened / totalDelivered) * 100) : 0;
  }, [statistics]);

  // Calculate click-through rate
  const clickThroughRate = useMemo(() => {
    if (!statistics) return 0;
    const totalClicked = statistics.clicked || 0;
    const totalOpened = statistics.opened || 0;
    return totalOpened > 0 ? Math.round((totalClicked / totalOpened) * 100) : 0;
  }, [statistics]);

  // Calculate bounce rate
  const bounceRate = useMemo(() => {
    if (!statistics) return 0;
    const totalBounced = statistics.bounced || 0;
    const totalSent = statistics.total || 0;
    return totalSent > 0 ? Math.round((totalBounced / totalSent) * 100) : 0;
  }, [statistics]);

  return (
    <div className="p-4 md:p-6 lg:p-8 space-y-6 max-w-7xl mx-auto">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Email Tracking Dashboard</h2>
          <p className="text-muted-foreground">Monitor and analyze your email campaign performance</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timePeriod} onValueChange={handleTimePeriodChange}>
            <SelectTrigger className="w-[180px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              {timePeriods.map((period) => (
                <SelectItem key={period.value} value={period.value}>
                  {period.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={fetchStatistics}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Refresh data</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon">
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export data</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:w-auto md:grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="data">Email Data</TabsTrigger>
          <TabsTrigger value="analytics" className="hidden md:block">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <StatsCard 
              title="Total Emails" 
              value={statistics?.total || 0} 
              icon={<Mail className="h-4 w-4 text-gray-600" />} 
              change={2.5}
            />
            <StatsCard 
              title="Delivered" 
              value={statistics?.delivered || 0} 
              icon={<Mail className="h-4 w-4 text-blue-600" />} 
              bgColor="bg-blue-50"
              change={1.8}
            />
            <StatsCard 
              title="Opened" 
              value={statistics?.opened || 0} 
              icon={<CheckCircle className="h-4 w-4 text-green-600" />} 
              bgColor="bg-green-50"
              change={3.2}
            />
            <StatsCard 
              title="Clicked" 
              value={statistics?.clicked || 0} 
              icon={<MousePointer className="h-4 w-4 text-purple-600" />} 
              bgColor="bg-purple-50"
              change={-1.5}
            />
            <StatsCard 
              title="Bounced" 
              value={statistics?.bounced || 0} 
              icon={<AlertTriangle className="h-4 w-4 text-amber-600" />} 
              bgColor="bg-amber-50"
              change={-0.5}
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{engagementRate}%</div>
                <div className="mt-4 h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-green-500 rounded-full" 
                    style={{ width: `${engagementRate}%` }}
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-2">Percentage of delivered emails that were opened</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Click-Through Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{clickThroughRate}%</div>
                <div className="mt-4 h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-purple-500 rounded-full" 
                    style={{ width: `${clickThroughRate}%` }}
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-2">Percentage of opened emails that were clicked</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Bounce Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{bounceRate}%</div>
                <div className="mt-4 h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-amber-500 rounded-full" 
                    style={{ width: `${bounceRate}%` }}
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-2">Percentage of emails that bounced</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="data" className="space-y-4 mt-4">
          {/* Filters */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Filters</CardTitle>
                  <CardDescription className="text-sm">Refine your search criteria</CardDescription>
                </div>
                <Button variant="ghost" size="sm" onClick={resetFilters}>
                  <Filter className="h-4 w-4 mr-2" />
                  Clear filters
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className="space-y-2">
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Status</label>
                  <Select
                    value={filters.type}
                    onValueChange={(value) => handleFilterChange('type', value)}
                  >
                    <SelectTrigger className="h-9 text-sm">
                      <SelectValue placeholder="All Statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="delivered">Delivered</SelectItem>
                      <SelectItem value="opened">Opened</SelectItem>
                      <SelectItem value="clicked">Clicked</SelectItem>
                      <SelectItem value="bounced">Bounced</SelectItem>
                      <SelectItem value="spam">Spam</SelectItem>
                      <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Start Date</label>
                  <DatePicker
                    selected={filters.startDate}
                    onSelect={(date: Date | null) => handleFilterChange('startDate', date)}
                    placeholderText="Select start date"
                    className="h-9 text-sm"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">End Date</label>
                  <DatePicker
                    selected={filters.endDate}
                    onSelect={(date: Date | null) => handleFilterChange('endDate', date)}
                    placeholderText="Select end date"
                    className="h-9 text-sm"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Type</label>
                  <Select
                    value={filters.notificationType}
                    onValueChange={(value) => handleFilterChange('notificationType', value)}
                  >
                    <SelectTrigger className="h-9 text-sm">
                      <SelectValue placeholder="All Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="booking_confirmation">Booking Confirmation</SelectItem>
                      <SelectItem value="booking_reminder">Booking Reminder</SelectItem>
                      <SelectItem value="payment_receipt">Payment Receipt</SelectItem>
                      <SelectItem value="account_verification">Account Verification</SelectItem>
                      <SelectItem value="password_reset">Password Reset</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Email</label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by email"
                      value={filters.email}
                      onChange={(e) => handleFilterChange('email', e.target.value)}
                      className="pl-8 h-9 text-sm"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-4 pt-4 border-t">
                <Button onClick={applyFilters} size="sm" className="px-6">
                  Apply Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Email tracking data table */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Email Tracking Data</CardTitle>
                  <CardDescription className="text-sm">
                    Detailed information about email delivery and engagement
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-muted-foreground">Show</span>
                  <Select
                    value={filters.perPage.toString()}
                    onValueChange={(value) => handleFilterChange('perPage', parseInt(value))}
                  >
                    <SelectTrigger className="w-[70px] h-8">
                      <SelectValue placeholder="10" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-xs text-muted-foreground">entries</span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <>
                  <div className="rounded-lg border border-border/50 overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow className="bg-muted/30">
                          <TableHead className="font-semibold">Email</TableHead>
                          <TableHead className="font-semibold">Subject</TableHead>
                          <TableHead className="font-semibold">Sent At</TableHead>
                          <TableHead className="font-semibold">Status</TableHead>
                          <TableHead className="font-semibold">Type</TableHead>
                          <TableHead className="font-semibold">Last Updated</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {statistics?.data && statistics.data.length > 0 ? (
                          statistics.data.map((item) => (
                            <TableRow key={item.id} className="hover:bg-muted/50">
                              <TableCell className="font-medium">{item.email}</TableCell>
                              <TableCell>{item.subject}</TableCell>
                              <TableCell>{formatDate(item.sent_at)}</TableCell>
                              <TableCell>
                                <Badge 
                                  variant="outline" 
                                  className={`${statusVariants[item.status]?.color} ${statusVariants[item.status]?.bgColor} border-0`}
                                >
                                  <span className="flex items-center">
                                    {statusVariants[item.status]?.icon}
                                    {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                                  </span>
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="secondary" className="font-normal">
                                  {item.notification_type}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {item.status === 'delivered' && formatDate(item.sent_at)}
                                {item.status === 'opened' && formatDate(item.opened_at)}
                                {item.status === 'clicked' && formatDate(item.clicked_at)}
                                {item.status === 'bounced' && formatDate(item.bounced_at)}
                                {item.status === 'spam' && formatDate(item.spam_at)}
                                {item.status === 'unsubscribed' && formatDate(item.unsubscribed_at)}
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                              No email tracking data found
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  {statistics?.pagination && statistics.pagination.total > 0 && (
                    <div className="flex items-center justify-between mt-6 pt-4 border-t border-border/50">
                      <div className="text-xs text-muted-foreground">
                        Showing {(statistics.pagination.current_page - 1) * statistics.pagination.per_page + 1} to {Math.min(statistics.pagination.current_page * statistics.pagination.per_page, statistics.pagination.total)} of {statistics.pagination.total} results
                      </div>
                      <Pagination
                        currentPage={statistics.pagination.current_page}
                        totalItems={statistics.pagination.total}
                        itemsPerPage={statistics.pagination.per_page}
                        onPageChange={(page) => handleFilterChange('page', page)}
                      />
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Email Performance</CardTitle>
                <CardDescription>Visualization of email metrics over time</CardDescription>
              </CardHeader>
              <CardContent className="h-80 flex items-center justify-center">
                <div className="flex flex-col items-center justify-center text-center p-6 border border-dashed rounded-lg w-full h-full">
                  <BarChart3 className="h-10 w-10 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">Analytics Visualization</h3>
                  <p className="text-sm text-muted-foreground mt-2 max-w-md">
                    This is a placeholder for email performance charts. In a real implementation, this would show trends of opens, clicks, and bounces over time.
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Email Distribution</CardTitle>
                <CardDescription>Breakdown of email statuses</CardDescription>
              </CardHeader>
              <CardContent className="h-80 flex items-center justify-center">
                <div className="flex flex-col items-center justify-center text-center p-6 border border-dashed rounded-lg w-full h-full">
                  <PieChart className="h-10 w-10 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">Distribution Chart</h3>
                  <p className="text-sm text-muted-foreground mt-2 max-w-md">
                    This is a placeholder for email distribution charts. In a real implementation, this would show the proportion of different email statuses.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};