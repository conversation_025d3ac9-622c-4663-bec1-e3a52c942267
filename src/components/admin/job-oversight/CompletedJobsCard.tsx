
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";

interface CompletedJobsCardProps {
  completedJobsThisMonth: number;
}

export const CompletedJobsCard = ({ completedJobsThisMonth }: CompletedJobsCardProps) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Completed Jobs</CardTitle>
      </CardHeader>
      <CardContent className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-3">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <p className="text-3xl font-bold">{completedJobsThisMonth}</p>
          <p className="text-sm text-muted-foreground">This Month</p>
        </div>
      </CardContent>
    </Card>
  );
};
