
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Badge } from "@/components/ui/badge";

export const ProviderPerformance = () => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  
  // Sample provider performance data
  const providers = [
    {
      id: "provider-001",
      name: "Alex's Plumbing",
      avatar: "/placeholder.svg",
      jobsCompleted: 24,
      responseRate: 98,
      averageRating: 4.8,
      onTimeCompletion: 95,
      customerSatisfaction: 97,
      bidWinRate: 68,
      status: "active"
    },
    {
      id: "provider-002",
      name: "Mike's Home Service",
      avatar: "/placeholder.svg",
      jobsCompleted: 36,
      responseRate: 92,
      averageRating: 4.9,
      onTimeCompletion: 98,
      customerSatisfaction: 99,
      bidWinRate: 75,
      status: "active"
    },
    {
      id: "provider-003",
      name: "Premier Plumbing",
      avatar: "/placeholder.svg",
      jobsCompleted: 18,
      responseRate: 85,
      averageRating: 4.7,
      onTimeCompletion: 90,
      customerSatisfaction: 94,
      bidWinRate: 62,
      status: "active"
    },
    {
      id: "provider-004",
      name: "Electro Solutions",
      avatar: "/placeholder.svg",
      jobsCompleted: 29,
      responseRate: 89,
      averageRating: 4.6,
      onTimeCompletion: 92,
      customerSatisfaction: 93,
      bidWinRate: 70,
      status: "on_probation"
    },
    {
      id: "provider-005",
      name: "CleanPro Services",
      avatar: "/placeholder.svg",
      jobsCompleted: 42,
      responseRate: 97,
      averageRating: 4.9,
      onTimeCompletion: 99,
      customerSatisfaction: 98,
      bidWinRate: 78,
      status: "active"
    }
  ];

  const MobileProviderCard = ({ provider }: { provider: any }) => (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex items-start gap-3 mb-4">
          <Avatar className="h-12 w-12 border border-gray-200">
            <AvatarImage src={provider.avatar} />
            <AvatarFallback>{provider.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-medium">{provider.name}</h3>
            <div className="flex items-center gap-1.5 mt-0.5">
              <span className="text-sm bg-yellow-50 text-yellow-700 px-1.5 py-0.5 rounded font-medium">
                ★ {provider.averageRating}
              </span>
              <span className="text-xs text-muted-foreground">
                ({provider.jobsCompleted} jobs)
              </span>
            </div>
          </div>
          <Badge 
            className={`ml-auto ${
              provider.status === "active" 
                ? "bg-green-100 text-green-800 border-green-200"
                : "bg-amber-100 text-amber-800 border-amber-200"
            }`}
          >
            {provider.status === "active" ? "Active" : "On Probation"}
          </Badge>
        </div>
        
        <div className="space-y-3">
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-gray-500">Response Rate</span>
              <span className="text-xs font-medium">{provider.responseRate}%</span>
            </div>
            <Progress value={provider.responseRate} className="h-1.5" />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-gray-500">On-Time Completion</span>
              <span className="text-xs font-medium">{provider.onTimeCompletion}%</span>
            </div>
            <Progress value={provider.onTimeCompletion} className="h-1.5" />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-gray-500">Customer Satisfaction</span>
              <span className="text-xs font-medium">{provider.customerSatisfaction}%</span>
            </div>
            <Progress value={provider.customerSatisfaction} className="h-1.5" />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-gray-500">Bid Win Rate</span>
              <span className="text-xs font-medium">{provider.bidWinRate}%</span>
            </div>
            <Progress value={provider.bidWinRate} className="h-1.5" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {isMobile ? (
        <div>
          {providers.map(provider => (
            <MobileProviderCard key={provider.id} provider={provider} />
          ))}
        </div>
      ) : (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Provider Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {providers.map(provider => (
                <div key={provider.id} className="flex flex-wrap gap-4 items-center justify-between pb-4 border-b last:border-0 last:pb-0">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={provider.avatar} />
                      <AvatarFallback>{provider.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{provider.name}</h3>
                        <Badge 
                          className={`${
                            provider.status === "active" 
                              ? "bg-green-100 text-green-800 border-green-200"
                              : "bg-amber-100 text-amber-800 border-amber-200"
                          }`}
                        >
                          {provider.status === "active" ? "Active" : "On Probation"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1 mt-0.5">
                        <span className="text-sm">★ {provider.averageRating}</span>
                        <span className="text-xs text-muted-foreground">
                          ({provider.jobsCompleted} jobs)
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 flex-1">
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-500">Response Rate</span>
                        <span className="text-xs font-medium">{provider.responseRate}%</span>
                      </div>
                      <Progress value={provider.responseRate} className="h-1.5" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-500">On-Time</span>
                        <span className="text-xs font-medium">{provider.onTimeCompletion}%</span>
                      </div>
                      <Progress value={provider.onTimeCompletion} className="h-1.5" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-500">Satisfaction</span>
                        <span className="text-xs font-medium">{provider.customerSatisfaction}%</span>
                      </div>
                      <Progress value={provider.customerSatisfaction} className="h-1.5" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-500">Bid Win Rate</span>
                        <span className="text-xs font-medium">{provider.bidWinRate}%</span>
                      </div>
                      <Progress value={provider.bidWinRate} className="h-1.5" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
