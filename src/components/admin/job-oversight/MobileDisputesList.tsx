
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { DisputeStatusBadge } from "./DisputeStatusBadge";
import { JobDispute, DisputeSeverity } from "@/types/jobs";
import { AlertTriangle, ShieldAlert, Flag, Clock, ChevronRight } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

interface MobileDisputesListProps {
  disputes: JobDispute[];
  onViewDispute: (dispute: JobDispute) => void;
}

export const MobileDisputesList: React.FC<MobileDisputesListProps> = ({ 
  disputes, 
  onViewDispute 
}) => {
  // Helper function to get severity icon and color
  const getSeverityDetails = (severity: DisputeSeverity) => {
    switch (severity) {
      case "urgent":
        return { 
          icon: <AlertTriangle className="h-4 w-4" />, 
          color: "bg-red-100 text-red-600", 
          borderColor: "#ea384c"
        };
      case "high":
        return { 
          icon: <ShieldAlert className="h-4 w-4" />, 
          color: "bg-orange-100 text-orange-600",
          borderColor: "#F97316"
        };
      case "medium":
        return { 
          icon: <Flag className="h-4 w-4" />, 
          color: "bg-yellow-100 text-yellow-600",
          borderColor: "#FACC15"
        };
      case "low":
        return { 
          icon: <Clock className="h-4 w-4" />, 
          color: "bg-blue-100 text-blue-600",
          borderColor: "#0EA5E9"
        };
    }
  };

  return (
    <div className="space-y-4">
      {disputes.map((dispute) => {
        const severityDetails = getSeverityDetails(dispute.severity);
        
        return (
          <Card 
            key={dispute.id}
            className="overflow-hidden border-l-4 cursor-pointer hover:bg-gray-50 transition-all"
            style={{ 
              borderLeftColor: severityDetails.borderColor
            }}
            onClick={() => onViewDispute(dispute)}
          >
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <p className="text-xs text-gray-500">Dispute #{dispute.id.substring(0, 6)}</p>
                  <h3 className="font-medium text-lg">{dispute.title}</h3>
                </div>
                <div className="flex items-center">
                  <DisputeStatusBadge status={dispute.status} />
                  <ChevronRight className="h-5 w-5 text-gray-400 ml-1" />
                </div>
              </div>
              
              <div className="flex items-center gap-3 mt-3">
                <Avatar className="h-10 w-10">
                  {dispute.initiatorAvatar ? (
                    <AvatarImage src={dispute.initiatorAvatar} alt={dispute.initiatorName} />
                  ) : null}
                  <AvatarFallback>{dispute.initiatorName.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{dispute.initiatorName}</p>
                  <p className="text-sm text-gray-500 capitalize">{dispute.initiator}</p>
                </div>
              </div>
              
              <p className="text-gray-600 mt-4 mb-3">{dispute.description}</p>
              
              <Badge className={`flex items-center gap-1.5 ${severityDetails.color} py-1 px-3`}>
                {severityDetails.icon}
                <span className="capitalize">{dispute.severity}</span>
              </Badge>
              
              <div className="mt-4 pt-3 border-t border-gray-200 flex justify-between text-sm text-gray-500">
                <span>Job: {dispute.jobId}</span>
                <span>{new Date(dispute.createdAt).toLocaleDateString()}</span>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
