
import React from "react";
import { Check } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useUIHelpers } from "@/hooks/use-ui-helpers";

export interface WorkflowStage {
  id: string;
  label: string; 
  description: string;
  icon: React.ReactNode;
  completed: boolean;
  progressPercentage: number;
  guidance?: string;
}

interface WorkflowStageItemProps {
  stage: WorkflowStage;
  index: number;
  currentStageIndex: number;
  isLastStage: boolean;
  isDisputeResolved: boolean;
  onStageClick: (stageId: string) => void;
}

export const WorkflowStageItem: React.FC<WorkflowStageItemProps> = ({
  stage,
  index,
  currentStageIndex,
  isLastStage,
  isDisputeResolved,
  onStageClick
}) => {
  const { isMobile } = useUIHelpers();
  const isCurrent = index === currentStageIndex;
  
  return (
    <div 
      key={stage.id}
      className={`flex items-start relative ${isMobile ? 'p-2' : 'p-3'} rounded-md transition-colors ${
        isCurrent 
          ? "bg-blue-50 border border-blue-200" 
          : stage.completed 
            ? "bg-green-50 border border-green-100" 
            : "bg-gray-50 border border-gray-200"
      } ${index > currentStageIndex ? "opacity-70" : "opacity-100"}`}
    >
      {/* Stage connector line */}
      {!isLastStage && (
        <div className={`absolute left-6 top-11 w-0.5 ${isMobile ? 'h-8' : 'h-10'} bg-gray-200`}></div>
      )}
      
      <div className={`z-10 flex items-center justify-center ${isMobile ? 'w-8 h-8' : 'w-10 h-10'} rounded-full shrink-0 
        ${stage.completed 
          ? "bg-green-100 text-green-600 border-green-200" 
          : isCurrent 
            ? "bg-blue-100 text-blue-600 border-blue-200"
            : "bg-gray-100 text-gray-400 border-gray-200"} 
        border-2`}
      >
        {stage.completed ? <Check className={isMobile ? "h-4 w-4" : "h-5 w-5"} /> : 
          <span className={isMobile ? "h-4 w-4" : ""}>
            {stage.icon}
          </span>
        }
      </div>
      
      <div className="ml-3 flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-sm">{stage.label}</h4>
          {isCurrent && !stage.completed && (
            <Badge className="ml-2 bg-blue-100 text-blue-700 border-blue-200 text-xs">Current</Badge>
          )}
        </div>
        
        <p className="text-xs text-muted-foreground mt-1">{stage.description}</p>
        
        {/* Stage progress indicator */}
        {stage.progressPercentage > 0 && stage.progressPercentage < 100 && (
          <div className="mt-2 space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">Stage progress</span>
              <span className="font-medium">{stage.progressPercentage}%</span>
            </div>
            <Progress value={stage.progressPercentage} className="h-1.5 bg-gray-100" />
          </div>
        )}
        
        {/* Stage-specific guidance - hide on mobile to save space */}
        {isCurrent && !stage.completed && !isMobile && stage.guidance && (
          <div className="mt-3 text-xs text-blue-600 bg-blue-50 p-2 rounded-md">
            {stage.guidance}
          </div>
        )}
        
        {/* Navigation controls for adjacent stages */}
        {index !== currentStageIndex && 
         Math.abs(index - currentStageIndex) === 1 &&
         !isDisputeResolved && (
          <button 
            className="mt-2 text-xs font-medium text-blue-600 hover:text-blue-800"
            onClick={() => onStageClick(stage.id)}
          >
            {index < currentStageIndex 
              ? "← Move back to this stage" 
              : "Advance to this stage →"}
          </button>
        )}
      </div>
    </div>
  );
};
