
import React from "react";
import { DisputeStatus } from "@/types/jobs";
import { Check, Clock, FileText, MessageSquare, Shield } from "lucide-react";

export const getWorkflowStages = (disputeStatus: DisputeStatus, hasEvidence: boolean) => {
  return [
    { 
      id: "submitted", 
      label: "Submitted", 
      description: "Initial dispute filing",
      icon: <Clock className="h-5 w-5" />,
      completed: true,
      progressPercentage: 100
    },
    { 
      id: "under_review", 
      label: "Triage & Review", 
      description: "Admin reviewing details",
      icon: <FileText className="h-5 w-5" />,
      completed: ["under_review", "mediation", "resolved", "closed"].includes(disputeStatus),
      progressPercentage: disputeStatus === "under_review" ? 50 : 
                         ["mediation", "resolved", "closed"].includes(disputeStatus) ? 100 : 0,
      guidance: "Review case details and decide if mediation is needed. Collect initial evidence."
    },
    { 
      id: "mediation", 
      label: "Mediation", 
      description: "Gathering statements and evidence",
      icon: <MessageSquare className="h-5 w-5" />,
      completed: ["resolved", "closed"].includes(disputeStatus),
      progressPercentage: disputeStatus === "mediation" ? 
                         (hasEvidence ? 75 : 50) : 
                         ["resolved", "closed"].includes(disputeStatus) ? 100 : 0,
      guidance: "Communicate with both parties to gather necessary information and evidence."
    },
    { 
      id: "admin_decision", 
      label: "Decision", 
      description: "Final assessment and resolution",
      icon: <Shield className="h-5 w-5" />,
      completed: ["resolved", "closed"].includes(disputeStatus),
      progressPercentage: disputeStatus === "resolved" || disputeStatus === "closed" ? 100 : 0,
      guidance: "Review all evidence and make a final decision on how to resolve the dispute."
    },
    { 
      id: "resolved", 
      label: "Resolution", 
      description: "Implementation and notification",
      icon: <Check className="h-5 w-5" />,
      completed: ["resolved", "closed"].includes(disputeStatus),
      progressPercentage: disputeStatus === "resolved" || disputeStatus === "closed" ? 100 : 0
    }
  ];
};

// Calculate overall progress for the dispute resolution
export const getOverallProgress = (status: DisputeStatus) => {
  const stageValues: Record<DisputeStatus, number> = {
    "submitted": 10,
    "under_review": 30,
    "mediation": 60,
    "resolved": 100,
    "closed": 100,
    "appealed": 25 // Added the missing appealed status
  };
  
  return stageValues[status] || 0;
};
