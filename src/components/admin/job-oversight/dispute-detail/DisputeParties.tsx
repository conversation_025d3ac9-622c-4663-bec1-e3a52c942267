
import React from "react";
import { JobDispute } from "@/types/jobs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUIHelpers } from "@/hooks/use-ui-helpers";

interface DisputePartiesProps {
  dispute: JobDispute;
}

export const DisputeParties: React.FC<DisputePartiesProps> = ({ dispute }) => {
  const { isMobile } = useUIHelpers();
  
  return (
    <div className={isMobile ? "space-y-2" : "space-y-3"}>
      <div>
        <h4 className="text-sm font-medium mb-1">Initiator</h4>
        <div className={`flex items-center gap-2 rounded-md bg-slate-50 ${isMobile ? 'p-1.5' : 'p-3'}`}>
          <Avatar className={isMobile ? "h-8 w-8" : "h-12 w-12"}>
            <AvatarImage src={dispute.initiatorAvatar} alt={dispute.initiatorName} />
            <AvatarFallback>{dispute.initiatorName.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{dispute.initiatorName}</div>
            <div className="text-sm text-muted-foreground">{dispute.initiator === "customer" ? "Customer" : "Provider"}</div>
          </div>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-1">Respondent</h4>
        <div className={`flex items-center gap-2 rounded-md bg-slate-50 ${isMobile ? 'p-1.5' : 'p-3'}`}>
          <Avatar className={isMobile ? "h-8 w-8" : "h-12 w-12"}>
            <AvatarImage src={dispute.respondentAvatar} alt={dispute.respondentName} />
            <AvatarFallback>{dispute.respondentName.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{dispute.respondentName}</div>
            <div className="text-sm text-muted-foreground">{dispute.initiator === "provider" ? "Customer" : "Provider"}</div>
          </div>
        </div>
      </div>
    </div>
  );
};
