import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { JobDispute, DisputeStatus } from "@/types/jobs";
import { useToast } from "@/hooks/use-toast";
import { DisputeHeader } from "./DisputeHeader";
import { MobileDisputeHeader } from "./MobileDisputeHeader";
import { DisputeDetailContent } from "./DisputeDetailContent";
import { DisputeEvidence } from "./DisputeEvidence";
import { DisputeAdminActions } from "./DisputeAdminActions";
import { DisputeOverview } from "./DisputeOverview";
import { AdminNotes } from "@/components/admin/shared/AdminNotes";
import { DisputeCustomerCommunication } from "./DisputeCustomerCommunication";
import { DisputeProviderCommunication } from "./DisputeProviderCommunication";
import { DisputeResolutionTemplate } from "./DisputeResolutionTemplate";
import { DisputeStageWorkflow } from "./DisputeStageWorkflow";
import { DisputeStageActions } from "./DisputeStageActions";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
interface DisputeDetailProps {
  dispute: JobDispute;
  onBack: () => void;
  onStatusChange?: (disputeId: string, newStatus: DisputeStatus) => void;
}
const DisputeDetail: React.FC<DisputeDetailProps> = ({
  dispute,
  onBack,
  onStatusChange
}) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [showResolveDialog, setShowResolveDialog] = useState(false);
  const {
    toast
  } = useToast();
  const {
    isMobile,
    isTablet
  } = useUIHelpers();

  // Sample admin notes for the notes tab
  const adminNotes = [{
    text: "Customer has provided images showing water damage. Provider claims work was done to specifications.",
    timestamp: "2023-04-09T09:15:00Z",
    admin: "Jane Admin"
  }, {
    text: "Reviewed the contract and it states a 30-day warranty on plumbing repairs. Provider should honor this.",
    timestamp: "2023-04-10T14:20:00Z",
    admin: "John Supervisor"
  }];
  useEffect(() => {
    // Reset active tab when dispute changes
    setActiveTab("overview");
  }, [dispute.id]);
  return <div className={`space-y-1 md:space-y-4 ${isMobile ? 'bg-gray-50' : ''}`}>
      {/* Responsive Header */}
      {isMobile ? <>
          <MobileDisputeHeader dispute={dispute} onBack={onBack} onStatusChange={onStatusChange} />
          {/* Add minimal padding to compensate for fixed header */}
          
        </> : <DisputeHeader dispute={dispute} onBack={onBack} onStatusChange={onStatusChange} />}
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-1 lg:gap-6">
        {/* Main content area */}
        <div className="lg:col-span-2 space-y-1 md:space-y-4">
          <DisputeDetailContent dispute={dispute} />
          
          <Card className={isMobile ? "mt-1 shadow-sm" : ""}>
            <CardHeader className={isMobile ? "pb-1 px-3 pt-3" : "pb-1"}>
              <CardTitle className="text-base md:text-lg">Dispute Management</CardTitle>
            </CardHeader>
            <CardContent className={isMobile ? "px-2 py-1" : ""}>
              <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
                {/* Improved mobile tab navigation with horizontal scrolling */}
                <div className={`${isMobile ? '-mx-2 px-1' : ''}`}>
                  <TabsList className={`${isMobile ? 'flex w-full overflow-x-auto no-scrollbar mb-1' : 'grid grid-cols-5 w-full mb-4'}`}>
                    <TabsTrigger value="overview" className={`whitespace-nowrap ${isMobile ? 'px-3 text-xs flex-shrink-0' : ''}`}>
                      Overview
                    </TabsTrigger>
                    <TabsTrigger value="evidence" className={`whitespace-nowrap ${isMobile ? 'px-3 text-xs flex-shrink-0' : ''}`}>
                      Evidence
                    </TabsTrigger>
                    <TabsTrigger value="communication" className={`whitespace-nowrap ${isMobile ? 'px-3 text-xs flex-shrink-0' : ''}`}>
                      {isMobile ? "Comms" : "Communication"}
                    </TabsTrigger>
                    <TabsTrigger value="provider" className={`whitespace-nowrap ${isMobile ? 'px-3 text-xs flex-shrink-0' : ''}`}>
                      Provider
                    </TabsTrigger>
                    <TabsTrigger value="resolution" className={`whitespace-nowrap ${isMobile ? 'px-3 text-xs flex-shrink-0' : ''}`}>
                      {isMobile ? "Resolve" : "Resolution"}
                    </TabsTrigger>
                  </TabsList>
                </div>
                
                {/* Tab content */}
                <TabsContent value="overview" className="space-y-2 pt-1">
                  {!isMobile && <DisputeStageWorkflow dispute={dispute} onStatusChange={onStatusChange} />}
                  <DisputeOverview dispute={dispute} onStatusChange={onStatusChange} onOpenResolveDialog={() => setShowResolveDialog(true)} />
                </TabsContent>
                
                <TabsContent value="evidence">
                  <DisputeEvidence evidence={dispute.evidence} />
                </TabsContent>
                
                <TabsContent value="communication">
                  <DisputeCustomerCommunication dispute={dispute} entityName={dispute.initiatorName === "Customer" ? dispute.initiatorName : dispute.respondentName} />
                </TabsContent>
                
                <TabsContent value="provider">
                  <DisputeProviderCommunication dispute={dispute} entityName={dispute.initiatorName === "Provider" ? dispute.initiatorName : dispute.respondentName} />
                </TabsContent>

                <TabsContent value="resolution">
                  <DisputeResolutionTemplate dispute={dispute} onStatusChange={onStatusChange} />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
        
        {/* Sidebar - hidden on mobile */}
        {!isMobile && <div className="space-y-4">
            <DisputeStageActions dispute={dispute} onStatusChange={onStatusChange} onOpenResolveDialog={() => setShowResolveDialog(true)} setActiveTab={setActiveTab} />
            
            <DisputeAdminActions dispute={dispute} />
            
            <Card>
              <CardHeader>
                <CardTitle>Admin Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <AdminNotes notes={adminNotes} onAddNote={note => {
              // In a real app, this would make an API call
              toast({
                description: "Admin note added"
              });
            }} entityType="dispute" entityId={dispute.id} />
              </CardContent>
            </Card>
          </div>}
      </div>
    </div>;
};

// Helper function to get next status
const getNextStatus = (status: DisputeStatus): DisputeStatus | null => {
  switch (status) {
    case "submitted":
      return "under_review";
    case "under_review":
      return "mediation";
    case "mediation":
      return "resolved";
    default:
      return null;
  }
};
export default DisputeDetail;