
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";

interface DisputeResolutionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onResolve: (note: string, actions: string[]) => void;
}

export const DisputeResolutionDialog: React.FC<DisputeResolutionDialogProps> = ({
  open,
  onOpenChange,
  onResolve
}) => {
  const { toast } = useToast();
  const [selectedActions, setSelectedActions] = useState<string[]>([]);
  const [resolutionNote, setResolutionNote] = useState("");

  const handleResolveDispute = () => {
    if (resolutionNote.trim() === "") {
      toast({
        title: "Resolution note required",
        description: "Please provide details about the resolution",
        variant: "destructive"
      });
      return;
    }
    
    if (selectedActions.length === 0) {
      toast({
        title: "Action required",
        description: "Please select at least one action to resolve this dispute",
        variant: "destructive"
      });
      return;
    }
    
    onResolve(resolutionNote, selectedActions);
    onOpenChange(false);
  };

  const toggleAction = (actionId: string) => {
    if (selectedActions.includes(actionId)) {
      setSelectedActions(selectedActions.filter(a => a !== actionId));
    } else {
      setSelectedActions([...selectedActions, actionId]);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Resolve Dispute</DialogTitle>
          <DialogDescription>
            Your decision will be final and communicated to both parties.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 my-4">
          <div>
            <Label>Select actions to take</Label>
            <div className="grid gap-2 mt-2">
              <Label className="flex items-center gap-2 p-2 border rounded-md cursor-pointer">
                <input 
                  type="checkbox" 
                  className="w-4 h-4"
                  checked={selectedActions.includes("payment_adjustment")}
                  onChange={() => toggleAction("payment_adjustment")}
                />
                Payment adjustment
              </Label>
              
              <Label className="flex items-center gap-2 p-2 border rounded-md cursor-pointer">
                <input 
                  type="checkbox" 
                  className="w-4 h-4"
                  checked={selectedActions.includes("refund")}
                  onChange={() => toggleAction("refund")}
                />
                Refund to customer
              </Label>
              
              <Label className="flex items-center gap-2 p-2 border rounded-md cursor-pointer">
                <input 
                  type="checkbox" 
                  className="w-4 h-4"
                  checked={selectedActions.includes("service_modification")}
                  onChange={() => toggleAction("service_modification")}
                />
                Require service modification
              </Label>
              
              <Label className="flex items-center gap-2 p-2 border rounded-md cursor-pointer">
                <input 
                  type="checkbox" 
                  className="w-4 h-4"
                  checked={selectedActions.includes("warning")}
                  onChange={() => toggleAction("warning")}
                />
                Issue warning
              </Label>
            </div>
          </div>
          
          <div>
            <Label htmlFor="resolution-note">Resolution details</Label>
            <Textarea 
              id="resolution-note"
              placeholder="Explain your decision and any actions required from either party..."
              className="mt-1" 
              rows={4}
              value={resolutionNote}
              onChange={(e) => setResolutionNote(e.target.value)}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleResolveDispute} className="bg-green-600 hover:bg-green-700">
            Submit Resolution
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
