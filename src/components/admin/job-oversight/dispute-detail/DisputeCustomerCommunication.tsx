
import React, { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Separator } from "@/components/ui/separator";
import { AdminMessageComposer } from "@/components/admin/shared/AdminMessageComposer";
import { AdminMessageThread } from "@/components/admin/shared/AdminMessageThread";
import { JobDispute } from "@/types/jobs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CircleAlert, FileText, Info } from "lucide-react";

interface DisputeCustomerCommunicationProps {
  dispute: JobDispute;
  entityName: string;
}

export const DisputeCustomerCommunication: React.FC<DisputeCustomerCommunicationProps> = ({
  dispute,
  entityName
}) => {
  const { toast } = useToast();
  const [requestingEvidence, setRequestingEvidence] = useState(false);

  // Sample messages for customer communication
  const customerMessages = [
    {
      id: "1",
      adminName: "Admin",
      text: "Hello, I'm reviewing your dispute about the plumbing repair. Could you please provide more details about what specific issues you're experiencing?",
      timestamp: "2023-04-10T10:30:00Z",
      isFromAdmin: true
    },
    {
      id: "2",
      adminName: "Customer",
      adminAvatar: "/placeholder.svg",
      text: "The plumbing repair is leaking again, just 2 days after the service was completed. Water is pooling under the sink and damaging the cabinet.",
      timestamp: "2023-04-10T10:35:00Z",
      isFromAdmin: false
    },
    {
      id: "3",
      adminName: "Admin",
      text: "I understand your concern. Could you please upload a photo of the leak? This will help me understand the issue better.",
      timestamp: "2023-04-10T10:40:00Z",
      isFromAdmin: true
    }
  ];

  const messageTemplates = [
    {
      title: "Request Evidence",
      text: "Could you please provide [specific evidence] to help with this dispute? Photos, receipts, or documentation related to the issue would be helpful."
    },
    {
      title: "Timeline Request",
      text: "To better understand the timeline of events, could you clarify when the service was performed and when you first noticed the issue?"
    },
    {
      title: "Initial Response",
      text: "I'm reviewing your dispute case now. I'll be your point of contact throughout this process and will work to find a fair resolution."
    },
    {
      title: "Solution Proposal",
      text: "Based on our review, we'd like to propose the following solution: [solution details]. Please let me know if this addresses your concerns."
    }
  ];

  return (
    <div className="space-y-4">
      <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm text-blue-700 flex items-center gap-2">
        <Info className="h-4 w-4 shrink-0" />
        <span>This communication is only visible to the admin and the customer</span>
      </div>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Customer Communication</CardTitle>
        </CardHeader>
        <CardContent>
          <AdminMessageThread 
            messages={customerMessages}
            entityName={entityName}
          />
          
          <Separator className="my-4" />
          
          {requestingEvidence ? (
            <div className="bg-muted p-4 rounded-md mb-4">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Request Evidence
              </h4>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">Request specific evidence from the customer to support their claim</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mr-2"
                  onClick={() => {
                    toast({ description: "Evidence request template applied" });
                    setRequestingEvidence(false);
                  }}
                >
                  Photos of Issue
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mr-2"
                  onClick={() => {
                    toast({ description: "Evidence request template applied" });
                    setRequestingEvidence(false);
                  }}
                >
                  Receipt/Invoice
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => {
                    toast({ description: "Evidence request template applied" });
                    setRequestingEvidence(false);
                  }}
                >
                  Service Agreement
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setRequestingEvidence(false)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2 mb-4 overflow-x-auto py-1 no-scrollbar">
              <Button 
                variant="outline" 
                size="sm" 
                className="whitespace-nowrap"
                onClick={() => setRequestingEvidence(true)}
              >
                <FileText className="h-4 w-4 mr-1" />
                Request Evidence
              </Button>
              
              {messageTemplates.map((template, idx) => (
                <Button 
                  key={idx} 
                  variant="outline" 
                  size="sm"
                  className="whitespace-nowrap"
                  onClick={() => {
                    toast({ description: `Template "${template.title}" applied` });
                  }}
                >
                  {template.title}
                </Button>
              ))}
            </div>
          )}
          
          <AdminMessageComposer 
            onSendMessage={(message: string) => {
              toast({
                description: "Message sent to customer"
              });
            }}
            placeholder="Type a message to the customer..."
          />
        </CardContent>
      </Card>
    </div>
  );
};
