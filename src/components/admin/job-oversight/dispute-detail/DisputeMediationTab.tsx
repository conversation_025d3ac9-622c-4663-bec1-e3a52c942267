
import React from "react";
import { useToast } from "@/hooks/use-toast";
import { Separator } from "@/components/ui/separator";
import { AdminMessageComposer } from "@/components/admin/shared/AdminMessageComposer";
import { AdminMessageThread } from "@/components/admin/shared/AdminMessageThread";

interface MockMessage {
  id: string;
  adminName: string;
  adminAvatar?: string;
  text: string;
  timestamp: string;
  isFromAdmin: boolean;
}

interface DisputeMediationTabProps {
  messages: MockMessage[];
  entityName: string;
}

export const DisputeMediationTab: React.FC<DisputeMediationTabProps> = ({
  messages,
  entityName
}) => {
  const { toast } = useToast();

  return (
    <div className="p-4 space-y-4">
      <AdminMessageThread 
        messages={messages}
        entityName={entityName}
      />
      
      <Separator className="my-4" />
      
      <AdminMessageComposer 
        onSendMessage={(message: string) => {
          // In a real app, this would make an API call
          toast({
            description: "Message sent to both parties"
          });
        }}
        placeholder="Type a mediation message..."
      />
    </div>
  );
};
