
import React, { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { JobDispute, DisputeStatus } from "@/types/jobs";
import { Check, Info } from "lucide-react";
import { 
  RadioGroup,
  RadioGroupItem 
} from "@/components/ui/radio-group";

interface DisputeResolutionTemplateProps {
  dispute: JobDispute;
  onStatusChange?: (disputeId: string, newStatus: DisputeStatus) => void;
}

export const DisputeResolutionTemplate: React.FC<DisputeResolutionTemplateProps> = ({
  dispute,
  onStatusChange
}) => {
  const { toast } = useToast();
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [resolutionText, setResolutionText] = useState("");
  const [resolutionType, setResolutionType] = useState("refund_partial");
  const [refundAmount, setRefundAmount] = useState("");
  
  const resolutionTemplates = [
    {
      id: "partial_refund",
      title: "Partial Refund",
      text: "After careful review of all evidence and communications, we've determined that a partial refund is appropriate. The provider will refund [amount] to compensate for [issue], while acknowledging that some value was delivered through the service.",
      type: "refund_partial"
    },
    {
      id: "full_refund",
      title: "Full Refund",
      text: "After thorough review of this case, we've determined that a full refund is warranted due to [reason]. The provider will refund the entire payment amount of [amount] within 5 business days.",
      type: "refund_full"
    },
    {
      id: "service_correction",
      title: "Service Correction",
      text: "We've determined that the provider should correct the identified issues at no additional cost. The provider has agreed to schedule a follow-up appointment within the next [timeframe] to address [specific issues].",
      type: "service_correction"
    },
    {
      id: "split_responsibility",
      title: "Split Responsibility",
      text: "Based on our investigation, we've determined that responsibility is shared between both parties. The provider will [provider action] and the customer will [customer action].",
      type: "split_responsibility"
    }
  ];

  const handleApplyTemplate = (templateId: string) => {
    const template = resolutionTemplates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      setResolutionText(template.text);
      setResolutionType(template.type);
      toast({
        description: `Applied "${template.title}" template`
      });
    }
  };

  const handleResolveDispute = () => {
    if (onStatusChange) {
      onStatusChange(dispute.id, "resolved");
    }
    toast({
      title: "Dispute resolved",
      description: "Resolution has been applied and both parties have been notified"
    });
  };

  return (
    <div className="space-y-4 p-4">
      {dispute.status === "resolved" ? (
        <div className="bg-green-50 border border-green-200 rounded-md p-4 flex flex-col gap-2">
          <div className="flex items-center gap-2 text-green-700 font-medium">
            <Check className="h-5 w-5" />
            <h3>Dispute Successfully Resolved</h3>
          </div>
          <p className="text-sm text-green-700">
            This dispute has been resolved and both parties have been notified of the outcome.
          </p>
          <div className="mt-2 p-3 bg-white rounded border border-green-100">
            <h4 className="text-sm font-medium mb-1">Resolution Summary</h4>
            <p className="text-sm">{dispute.resolution?.description || "No resolution details available."}</p>
            {dispute.resolution?.actionTaken && dispute.resolution.actionTaken.length > 0 && (
              <div className="mt-2">
                <h5 className="text-xs font-medium">Actions Taken:</h5>
                <ul className="text-xs mt-1 space-y-1">
                  {dispute.resolution.actionTaken.map((action, index) => (
                    <li key={index} className="flex items-center gap-1">
                      <span className="h-1.5 w-1.5 bg-green-500 rounded-full"></span>
                      <span>{action.description}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      ) : (
        <>
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm text-blue-700 flex items-center gap-2">
            <Info className="h-4 w-4 shrink-0" />
            <span>Create a resolution for this dispute to notify both parties of your decision</span>
          </div>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Resolution Templates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                {resolutionTemplates.map(template => (
                  <Button
                    key={template.id}
                    variant={selectedTemplate === template.id ? "default" : "outline"}
                    className="justify-start h-auto py-3 text-left"
                    onClick={() => handleApplyTemplate(template.id)}
                  >
                    <div>
                      <div className="font-medium">{template.title}</div>
                      <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
                        {template.text.substring(0, 70)}...
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
              
              <div className="space-y-4 mt-6">
                <div className="space-y-2">
                  <Label>Resolution Type</Label>
                  <RadioGroup defaultValue={resolutionType} value={resolutionType} onValueChange={setResolutionType} className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="refund_partial" id="refund-partial" />
                      <Label htmlFor="refund-partial">Partial Refund</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="refund_full" id="refund-full" />
                      <Label htmlFor="refund-full">Full Refund</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="service_correction" id="service-correction" />
                      <Label htmlFor="service-correction">Service Correction</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="split_responsibility" id="split" />
                      <Label htmlFor="split">Split Responsibility</Label>
                    </div>
                  </RadioGroup>
                </div>
                
                {(resolutionType === "refund_partial" || resolutionType === "refund_full") && (
                  <div className="space-y-2">
                    <Label htmlFor="refund-amount">Refund Amount</Label>
                    <Input 
                      id="refund-amount" 
                      placeholder="e.g. $100.00" 
                      value={refundAmount}
                      onChange={(e) => setRefundAmount(e.target.value)}
                    />
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="resolution-text">Resolution Message</Label>
                  <Textarea 
                    id="resolution-text"
                    placeholder="Describe the resolution details..."
                    className="min-h-[150px]"
                    value={resolutionText}
                    onChange={(e) => setResolutionText(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    This message will be shared with both the customer and provider.
                  </p>
                </div>
                
                <div className="pt-2">
                  <Button 
                    className="w-full" 
                    disabled={!resolutionText.trim()}
                    onClick={handleResolveDispute}
                  >
                    Apply Resolution & Notify Both Parties
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};
