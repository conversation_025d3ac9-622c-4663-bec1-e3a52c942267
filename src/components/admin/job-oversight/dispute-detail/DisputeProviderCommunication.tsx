
import React, { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Separator } from "@/components/ui/separator";
import { AdminMessageComposer } from "@/components/admin/shared/AdminMessageComposer";
import { AdminMessageThread } from "@/components/admin/shared/AdminMessageThread";
import { JobDispute } from "@/types/jobs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CircleAlert, FileText, Info } from "lucide-react";

interface DisputeProviderCommunicationProps {
  dispute: JobDispute;
  entityName: string;
}

export const DisputeProviderCommunication: React.FC<DisputeProviderCommunicationProps> = ({
  dispute,
  entityName
}) => {
  const { toast } = useToast();
  const [requestingEvidence, setRequestingEvidence] = useState(false);

  // Sample messages for provider communication
  const providerMessages = [
    {
      id: "1",
      adminName: "Admin",
      text: "Hello, I'm reviewing a dispute regarding the plumbing repair service you provided. The customer reports a leak occurring 2 days after service. Could you share your perspective?",
      timestamp: "2023-04-10T10:45:00Z",
      isFromAdmin: true
    },
    {
      id: "2",
      adminName: "Provider",
      adminAvatar: "/placeholder.svg",
      text: "I completed the repair as requested and tested it before leaving. There were no issues at that time. It's possible something else has caused this new leak.",
      timestamp: "2023-04-10T11:00:00Z",
      isFromAdmin: false
    },
    {
      id: "3",
      adminName: "Admin",
      text: "Thank you for your response. Does your service include any warranty period for the repairs completed?",
      timestamp: "2023-04-10T11:05:00Z",
      isFromAdmin: true
    }
  ];

  const messageTemplates = [
    {
      title: "Request Evidence",
      text: "Could you please provide documentation related to this service such as work orders, photos taken during service, or your records of the work completed?"
    },
    {
      title: "Service Details",
      text: "Can you explain the specific work performed and any materials used during this service?"
    },
    {
      title: "Warranty Policy",
      text: "Please confirm your warranty policy for this type of service, including duration and what's covered."
    },
    {
      title: "Follow-up Solution",
      text: "Would you be willing to perform a follow-up inspection to determine the cause of the issue the customer is experiencing?"
    }
  ];

  return (
    <div className="space-y-4">
      <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm text-blue-700 flex items-center gap-2">
        <Info className="h-4 w-4 shrink-0" />
        <span>This communication is only visible to the admin and the provider</span>
      </div>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Provider Communication</CardTitle>
        </CardHeader>
        <CardContent>
          <AdminMessageThread 
            messages={providerMessages}
            entityName={entityName}
          />
          
          <Separator className="my-4" />
          
          {requestingEvidence ? (
            <div className="bg-muted p-4 rounded-md mb-4">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Request Evidence
              </h4>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">Request specific evidence from the provider to support their position</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mr-2"
                  onClick={() => {
                    toast({ description: "Evidence request template applied" });
                    setRequestingEvidence(false);
                  }}
                >
                  Service Photos
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mr-2"
                  onClick={() => {
                    toast({ description: "Evidence request template applied" });
                    setRequestingEvidence(false);
                  }}
                >
                  Work Order
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => {
                    toast({ description: "Evidence request template applied" });
                    setRequestingEvidence(false);
                  }}
                >
                  Warranty Terms
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setRequestingEvidence(false)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2 mb-4 overflow-x-auto py-1 no-scrollbar">
              <Button 
                variant="outline" 
                size="sm" 
                className="whitespace-nowrap"
                onClick={() => setRequestingEvidence(true)}
              >
                <FileText className="h-4 w-4 mr-1" />
                Request Evidence
              </Button>
              
              {messageTemplates.map((template, idx) => (
                <Button 
                  key={idx} 
                  variant="outline" 
                  size="sm"
                  className="whitespace-nowrap"
                  onClick={() => {
                    toast({ description: `Template "${template.title}" applied` });
                  }}
                >
                  {template.title}
                </Button>
              ))}
            </div>
          )}
          
          <AdminMessageComposer 
            onSendMessage={(message: string) => {
              toast({
                description: "Message sent to provider"
              });
            }}
            placeholder="Type a message to the provider..."
          />
        </CardContent>
      </Card>
    </div>
  );
};
