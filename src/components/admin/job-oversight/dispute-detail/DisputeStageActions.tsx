
import React from "react";
import { JobDispute, DisputeStatus } from "@/types/jobs";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { AlertCircle, CheckCircle, MessageSquare, ClipboardList, Users, Calendar } from "lucide-react";

interface DisputeStageActionsProps {
  dispute: JobDispute;
  onStatusChange?: (disputeId: string, newStatus: DisputeStatus) => void;
  onOpenResolveDialog: () => void;
  setActiveTab: (tab: string) => void;
}

export const DisputeStageActions: React.FC<DisputeStageActionsProps> = ({
  dispute,
  onStatusChange,
  onOpenResolveDialog,
  setActiveTab
}) => {
  const { toast } = useToast();

  // Actions based on current stage
  const getStageActions = () => {
    switch (dispute.status) {
      case "submitted":
        return (
          <div className="space-y-3">
            <Button 
              className="w-full justify-start text-left"
              onClick={() => {
                if (onStatusChange) {
                  onStatusChange(dispute.id, "under_review");
                }
                toast({
                  description: "Dispute accepted for review"
                });
              }}
            >
              <ClipboardList className="h-4 w-4 mr-2" />
              Accept & Begin Review
            </Button>
            
            <Button 
              variant="outline"
              className="w-full justify-start text-left"
              onClick={() => setActiveTab("evidence")}
            >
              <AlertCircle className="h-4 w-4 mr-2" />
              Review Evidence
            </Button>
          </div>
        );
        
      case "under_review":
        return (
          <div className="space-y-3">
            <Button 
              className="w-full justify-start text-left"
              onClick={() => {
                if (onStatusChange) {
                  onStatusChange(dispute.id, "mediation");
                }
                toast({
                  description: "Dispute moved to mediation phase"
                });
              }}
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Begin Mediation Process
            </Button>
            
            <Button 
              variant="outline"
              className="w-full justify-start text-left"
              onClick={() => setActiveTab("customer")}
            >
              <Users className="h-4 w-4 mr-2" />
              Contact Customer
            </Button>
            
            <Button 
              variant="outline"
              className="w-full justify-start text-left"
              onClick={() => setActiveTab("provider")}
            >
              <Users className="h-4 w-4 mr-2" />
              Contact Provider
            </Button>
          </div>
        );
        
      case "mediation":
        return (
          <div className="space-y-3">
            <Button 
              className="w-full justify-start text-left"
              onClick={onOpenResolveDialog}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Create Resolution
            </Button>
            
            <Button 
              variant="outline"
              className="w-full justify-start text-left"
              onClick={() => setActiveTab("resolution")}
            >
              <ClipboardList className="h-4 w-4 mr-2" />
              View Resolution Templates
            </Button>
            
            <Button 
              variant="outline"
              className="w-full justify-start text-left"
              onClick={() => {
                toast({
                  description: "Meeting scheduled successfully"
                });
              }}
            >
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Mediation Call
            </Button>
            
            <div className="grid grid-cols-2 gap-2">
              <Button 
                variant="outline"
                size="sm"
                onClick={() => setActiveTab("customer")}
              >
                Contact Customer
              </Button>
              
              <Button 
                variant="outline"
                size="sm"
                onClick={() => setActiveTab("provider")}
              >
                Contact Provider
              </Button>
            </div>
          </div>
        );
        
      case "resolved":
      case "closed":
        return (
          <div className="space-y-3">
            <div className="p-3 bg-green-50 border border-green-200 rounded-md text-green-700 flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
              <div>
                <p className="font-medium">Dispute Resolved</p>
                <p className="text-sm">Resolution was applied on {new Date(dispute.updatedAt).toLocaleDateString()}</p>
              </div>
            </div>
            
            <Button 
              variant="outline"
              className="w-full justify-start text-left"
              onClick={() => {
                toast({
                  description: "Follow-up message sent to both parties"
                });
              }}
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Send Follow-up Message
            </Button>
          </div>
        );
        
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base">Stage Actions</CardTitle>
      </CardHeader>
      <CardContent>
        {getStageActions()}
      </CardContent>
    </Card>
  );
};
