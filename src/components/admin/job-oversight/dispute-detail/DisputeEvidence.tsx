
import React from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { File, FileText, Image, MessageSquare, User } from "lucide-react";
import { DisputeEvidence as DisputeEvidenceType } from "@/types/jobs";

interface DisputeEvidenceProps {
  evidence: DisputeEvidenceType[];
}

export const DisputeEvidence: React.FC<DisputeEvidenceProps> = ({ evidence }) => {
  const { toast } = useToast();

  const getEvidenceIcon = (evidence: DisputeEvidenceType) => {
    switch (evidence.type) {
      case "message":
        return <MessageSquare className="h-5 w-5 text-blue-500" />;
      case "image":
        return <Image className="h-5 w-5 text-green-500" />;
      case "document":
        return <File className="h-5 w-5 text-orange-500" />;
      case "contract":
        return <FileText className="h-5 w-5 text-purple-500" />;
      default:
        return <File className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Evidence Submitted</CardTitle>
        <CardDescription>
          Materials provided by both parties to support their claims
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {evidence.map((evidenceItem) => (
            <div
              key={evidenceItem.id}
              className="flex items-start p-3 border rounded-md gap-3"
            >
              <div className="mt-1">{getEvidenceIcon(evidenceItem)}</div>
              <div className="flex-1">
                <div className="flex justify-between">
                  <div className="font-medium">
                    {evidenceItem.description}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(evidenceItem.timestamp).toLocaleDateString()}
                  </div>
                </div>
                <div className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                  <User className="h-3 w-3" />
                  <span>Submitted by {evidenceItem.submitterName} ({evidenceItem.submittedBy})</span>
                </div>
                {evidenceItem.url && evidenceItem.type === "image" && (
                  <div className="mt-2">
                    <img
                      src={evidenceItem.url}
                      alt={evidenceItem.description}
                      className="max-h-40 rounded-md border"
                    />
                  </div>
                )}
                {evidenceItem.url && evidenceItem.type !== "image" && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => window.open(evidenceItem.url, "_blank")}
                  >
                    <File className="h-4 w-4 mr-2" />
                    View Document
                  </Button>
                )}
              </div>
            </div>
          ))}
          
          <div className="text-center">
            <Button
              variant="outline" 
              onClick={() => {
                toast({
                  title: "Request sent",
                  description: "Both parties have been asked to provide additional evidence"
                });
              }}
            >
              Request Additional Evidence
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
