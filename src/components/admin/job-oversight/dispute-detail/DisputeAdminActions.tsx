
import React, { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, Calendar, MessageSquare, User } from "lucide-react";
import { JobDispute } from "@/types/jobs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

interface DisputeAdminActionsProps {
  dispute: JobDispute;
}

export const DisputeAdminActions: React.FC<DisputeAdminActionsProps> = ({ 
  dispute 
}) => {
  const { toast } = useToast();
  const [scheduleMeetingOpen, setScheduleMeetingOpen] = useState(false);
  const [meetingDate, setMeetingDate] = useState("");
  const [meetingTime, setMeetingTime] = useState("");
  const [meetingDuration, setMeetingDuration] = useState("30");

  const handleScheduleMeeting = () => {
    toast({
      description: `Meeting scheduled for ${meetingDate} at ${meetingTime}`
    });
    setScheduleMeetingOpen(false);
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Administrative Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-2">
            <Button 
              variant="outline" 
              className="justify-start text-left"
              onClick={() => {
                toast({
                  description: "Notification sent to both parties"
                });
              }}
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Notify Both Parties
            </Button>
            
            <Button 
              variant="outline" 
              className="justify-start text-left"
              onClick={() => setScheduleMeetingOpen(true)}
            >
              <Calendar className="mr-2 h-4 w-4" />
              Schedule Meeting
            </Button>
            
            <Button 
              variant="outline" 
              className="justify-start text-left"
              onClick={() => {
                toast({
                  description: "Dispute will be prioritized in review queue"
                });
              }}
            >
              <AlertTriangle className="mr-2 h-4 w-4" />
              Flag as Priority
            </Button>
            
            <Button 
              variant="outline" 
              className="justify-start text-left"
              onClick={() => {
                toast({
                  description: "Dispute reassigned to current admin"
                });
              }}
            >
              <User className="mr-2 h-4 w-4" />
              Assign to Me
            </Button>
          </div>
          
          {dispute.status !== "resolved" && dispute.status !== "closed" && (
            <div className="pt-2 border-t">
              <h4 className="text-sm font-medium mb-2">Timeline</h4>
              <div className="text-sm space-y-1 text-muted-foreground">
                <div className="flex justify-between">
                  <span>Created:</span>
                  <span>{new Date(dispute.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Updated:</span>
                  <span>{new Date(dispute.updatedAt).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Resolution by:</span>
                  <span className="text-amber-600 font-medium">
                    {new Date(new Date(dispute.createdAt).getTime() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      <Dialog open={scheduleMeetingOpen} onOpenChange={setScheduleMeetingOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Schedule Meeting</DialogTitle>
            <DialogDescription>
              Schedule a meeting with both parties to discuss the dispute and work towards a resolution.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="meeting-date">Date</Label>
              <Input
                id="meeting-date"
                type="date"
                value={meetingDate}
                onChange={(e) => setMeetingDate(e.target.value)}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="meeting-time">Time</Label>
              <Input
                id="meeting-time"
                type="time"
                value={meetingTime}
                onChange={(e) => setMeetingTime(e.target.value)}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="meeting-duration">Duration (minutes)</Label>
              <select
                id="meeting-duration"
                className="flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                value={meetingDuration}
                onChange={(e) => setMeetingDuration(e.target.value)}
              >
                <option value="15">15 minutes</option>
                <option value="30">30 minutes</option>
                <option value="45">45 minutes</option>
                <option value="60">60 minutes</option>
              </select>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setScheduleMeetingOpen(false)}>Cancel</Button>
            <Button onClick={handleScheduleMeeting}>Schedule Meeting</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
