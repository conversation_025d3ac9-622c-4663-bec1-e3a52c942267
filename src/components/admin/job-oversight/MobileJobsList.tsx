
import React from 'react';
import { JobStatusBadge } from './JobStatusBadge';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, MapPin, DollarSign, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface Job {
  id: string;
  title: string;
  status: string;
  customer: string;
  customerAvatar: string;
  provider: string | null;
  date: string;
  value: string;
  priority?: 'high' | 'medium' | 'low';
  hasIssue?: boolean;
}

interface MobileJobsListProps {
  jobs: Job[];
  onSelectJob?: (jobId: string) => void;
  showPriority?: boolean;
}

export const MobileJobsList = ({ jobs, onSelectJob, showPriority = false }: MobileJobsListProps) => {
  return (
    <div className="space-y-4">
      {jobs.map(job => (
        <Card 
          key={job.id} 
          className="cursor-pointer hover:shadow-md transition-all border-l-4 overflow-hidden relative"
          onClick={() => onSelectJob && onSelectJob(job.id)}
          style={{ 
            borderLeftColor: job.hasIssue ? '#ea384c' : 
                             job.priority === 'high' ? '#F97316' : 
                             job.priority === 'medium' ? '#FACC15' : 
                             job.status === 'disputed' ? '#ea384c' : 
                             '#e5e7eb' 
          }}
        >
          {job.hasIssue && (
            <div className="absolute top-0 right-0 p-1 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-500" />
            </div>
          )}
          
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <p className="text-xs text-gray-500">{job.id}</p>
                <h3 className="font-medium text-base">{job.title}</h3>
              </div>
              <JobStatusBadge status={job.status} />
            </div>
            
            <div className="flex items-center gap-2 mt-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={job.customerAvatar} />
                <AvatarFallback>{job.customer[0]}</AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm">{job.customer}</p>
                <p className="text-xs text-muted-foreground">Customer</p>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-2 mt-3 text-xs">
              <div className="flex items-center gap-1">
                <Calendar className="h-3.5 w-3.5 text-gray-500" />
                <span>{job.date}</span>
              </div>
              <div className="flex items-center gap-1">
                <DollarSign className="h-3.5 w-3.5 text-gray-500" />
                <span>{job.value}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-3.5 w-3.5 text-gray-500" />
                <span>{job.provider || "Unassigned"}</span>
              </div>
            </div>
            
            {showPriority && job.priority && (
              <div className="mt-2 pt-2 border-t border-gray-100">
                <Badge 
                  variant="outline" 
                  className={`text-xs ${
                    job.priority === 'high' ? 'bg-orange-50 text-orange-700 border-orange-200' : 
                    job.priority === 'medium' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' : 
                    'bg-blue-50 text-blue-700 border-blue-200'
                  }`}
                >
                  {job.priority.charAt(0).toUpperCase() + job.priority.slice(1)} Priority
                </Badge>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
