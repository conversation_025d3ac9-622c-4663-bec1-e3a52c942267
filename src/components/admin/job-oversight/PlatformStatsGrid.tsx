
import React from 'react';
import { PlatformMetricsCard } from './PlatformMetricsCard';
import { AttentionRequiredCard } from './AttentionRequiredCard';
import { CompletedJobsCard } from './CompletedJobsCard';

interface PlatformMetrics {
  activeJobs: number;
  completedJobsThisMonth: number;
  customerSatisfaction: number;
}

interface PlatformStatsGridProps {
  metrics: PlatformMetrics;
}

export const PlatformStatsGrid = ({ metrics }: PlatformStatsGridProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <PlatformMetricsCard 
        activeJobs={metrics.activeJobs}
        completedJobsThisMonth={metrics.completedJobsThisMonth}
        customerSatisfaction={metrics.customerSatisfaction}
      />
      <AttentionRequiredCard />
      <CompletedJobsCard completedJobsThisMonth={metrics.completedJobsThisMonth} />
    </div>
  );
};
