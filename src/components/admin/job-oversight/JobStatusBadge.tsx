
import React from 'react';
import { Badge } from "@/components/ui/badge";

interface JobStatusBadgeProps {
  status: string;
}

export const JobStatusBadge = ({ status }: JobStatusBadgeProps) => {
  // Standardize the status (convert to lowercase and handle variations)
  const normalizedStatus = status.toLowerCase().replace(/_/g, ' ');
  
  switch (normalizedStatus) {
    case "completed":
      return <Badge className="bg-green-100 text-green-800 border-green-200">Completed</Badge>;
    case "in progress":
    case "in_progress":
      return <Badge className="bg-blue-100 text-blue-800 border-blue-200">In Progress</Badge>;
    case "pending":
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>;
    case "in review":
    case "in_review":
      return <Badge className="bg-purple-100 text-purple-800 border-purple-200">In Review</Badge>;
    case "disputed":
      return <Badge className="bg-red-100 text-red-800 border-red-200">Disputed</Badge>;
    case "bidding":
      return <Badge className="bg-indigo-100 text-indigo-800 border-indigo-200">Bidding</Badge>;
    case "awarded":
      return <Badge className="bg-amber-100 text-amber-800 border-amber-200">Awarded</Badge>;
    case "open":
      return <Badge className="bg-emerald-100 text-emerald-800 border-emerald-200">Open</Badge>;
    case "cancelled":
      return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Cancelled</Badge>;
    default:
      return <Badge variant="outline" className="capitalize">{status.replace(/_/g, ' ')}</Badge>;
  }
};
