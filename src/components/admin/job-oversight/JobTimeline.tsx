
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useMediaQuery } from "@/hooks/use-media-query";
import { JobDetailView } from "@/components/admin/jobs/JobDetailView";

export const JobTimeline = () => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null);
  
  // Sample job timeline data
  const jobs = [
    {
      id: "JOB-2345",
      title: "Bathroom Remodel",
      customer: "<PERSON>",
      currentStage: "bidding",
      stages: [
        { name: "created", completed: true, date: "2025-05-01" },
        { name: "bidding", completed: true, date: "2025-05-02" },
        { name: "awarded", completed: false },
        { name: "in_progress", completed: false },
        { name: "completed", completed: false },
        { name: "reviewed", completed: false }
      ]
    },
    {
      id: "JOB-2346",
      title: "Kitchen Sink Repair",
      customer: "<PERSON>",
      currentStage: "awarded",
      stages: [
        { name: "created", completed: true, date: "2025-05-01" },
        { name: "bidding", completed: true, date: "2025-05-02" },
        { name: "awarded", completed: true, date: "2025-05-03" },
        { name: "in_progress", completed: false },
        { name: "completed", completed: false },
        { name: "reviewed", completed: false }
      ]
    },
    {
      id: "JOB-2347",
      title: "Water Heater Installation",
      customer: "Emily Davis",
      currentStage: "in_progress",
      stages: [
        { name: "created", completed: true, date: "2025-04-28" },
        { name: "bidding", completed: true, date: "2025-04-29" },
        { name: "awarded", completed: true, date: "2025-04-30" },
        { name: "in_progress", completed: true, date: "2025-05-02" },
        { name: "completed", completed: false },
        { name: "reviewed", completed: false }
      ]
    },
    {
      id: "JOB-2348",
      title: "Electrical Wiring Upgrade",
      customer: "James Miller",
      currentStage: "completed",
      stages: [
        { name: "created", completed: true, date: "2025-04-25" },
        { name: "bidding", completed: true, date: "2025-04-26" },
        { name: "awarded", completed: true, date: "2025-04-27" },
        { name: "in_progress", completed: true, date: "2025-04-29" },
        { name: "completed", completed: true, date: "2025-05-03" },
        { name: "reviewed", completed: false }
      ]
    }
  ];

  // Helper function to format stage names
  const formatStageName = (stage: string) => {
    return stage
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Helper function to get stage color
  const getStageColor = (stageName: string, completed: boolean) => {
    if (!completed) return "bg-gray-200";
    
    switch(stageName) {
      case "created":
        return "bg-blue-500";
      case "bidding":
        return "bg-purple-500";
      case "awarded":
        return "bg-amber-500";
      case "in_progress":
        return "bg-indigo-500";
      case "completed":
        return "bg-green-500";
      case "reviewed":
        return "bg-teal-500";
      default:
        return "bg-gray-500";
    }
  };

  const handleViewDetails = (jobId: string) => {
    setSelectedJobId(jobId);
  };

  const handleCloseDetails = () => {
    setSelectedJobId(null);
  };

  // Find the selected job for the detail view
  const selectedJob = selectedJobId ? 
    {
      id: selectedJobId,
      title: jobs.find(job => job.id === selectedJobId)?.title || "",
      customerName: jobs.find(job => job.id === selectedJobId)?.customer || "",
      customerAvatar: "/placeholder.svg",
      provider: "Assigned Provider", // In a real app, you would fetch this data
      providerAvatar: "/placeholder.svg",
      status: "Open",
      value: "$1,200", // Example value
      description: "Detailed job description would be displayed here. In a real application, this would be fetched from the database.",
      createdDate: jobs.find(job => job.id === selectedJobId)?.stages[0].date || "",
      hasNotes: false
    } : null;

  // Timeline component
  const JobTimelineItem = ({ job }: { job: any }) => (
    <Card 
      className="mb-6 cursor-pointer transition-all hover:shadow-md" 
      onClick={() => handleViewDetails(job.id)}
    >
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <div className="text-xs text-gray-500 mb-1">{job.id}</div>
            <CardTitle className="text-base">{job.title}</CardTitle>
            <div className="text-sm text-muted-foreground mt-1">Customer: {job.customer}</div>
          </div>
          <Badge 
            className={`
              ${job.currentStage === "bidding" ? "bg-purple-100 text-purple-800 border-purple-200" : ""}
              ${job.currentStage === "awarded" ? "bg-amber-100 text-amber-800 border-amber-200" : ""}
              ${job.currentStage === "in_progress" ? "bg-indigo-100 text-indigo-800 border-indigo-200" : ""}
              ${job.currentStage === "completed" ? "bg-green-100 text-green-800 border-green-200" : ""}
              ${job.currentStage === "reviewed" ? "bg-teal-100 text-teal-800 border-teal-200" : ""}
            `}
          >
            {formatStageName(job.currentStage)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className={`relative ${isMobile ? "" : "flex justify-between"}`}>
          {isMobile ? (
            <div className="space-y-3">
              {job.stages.map((stage: any, index: number) => (
                <div key={index} className="flex items-start gap-3">
                  <div className={`h-6 w-6 rounded-full ${getStageColor(stage.name, stage.completed)} flex items-center justify-center text-white text-xs`}>
                    {stage.completed ? "✓" : ""}
                  </div>
                  <div>
                    <div className="font-medium text-sm">{formatStageName(stage.name)}</div>
                    {stage.date && <div className="text-xs text-muted-foreground">{new Date(stage.date).toLocaleDateString()}</div>}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <>
              {/* Line connecting all stages */}
              <div className="absolute top-3 left-0 right-0 h-1 bg-gray-200"></div>
              
              {job.stages.map((stage: any, index: number) => (
                <div key={index} className="relative flex flex-col items-center text-center" style={{ width: `${100 / job.stages.length}%` }}>
                  <div className={`h-6 w-6 rounded-full ${getStageColor(stage.name, stage.completed)} z-10`}></div>
                  <div className="mt-2 text-xs font-medium">{formatStageName(stage.name)}</div>
                  {stage.date && <div className="text-xs text-muted-foreground">{new Date(stage.date).toLocaleDateString()}</div>}
                </div>
              ))}
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-4">
      {jobs.map((job) => (
        <JobTimelineItem key={job.id} job={job} />
      ))}
      
      {/* Job Detail Dialog */}
      {selectedJob && (
        <JobDetailView 
          job={selectedJob} 
          open={!!selectedJobId} 
          onClose={handleCloseDetails} 
        />
      )}
    </div>
  );
};
