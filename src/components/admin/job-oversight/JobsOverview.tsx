
import React from 'react';
import { PlatformStatsGrid } from './PlatformStatsGrid';
import { RecentJobsCard } from './RecentJobsCard';

export const JobsOverview = () => {
  // Simplified mock data for recent jobs
  const recentJobs = [
    {
      id: "JOB-24-1234",
      title: "Kitchen Renovation",
      status: "in_progress",
      customer: "<PERSON>",
      customerAvatar: "/placeholder.svg",
      provider: "Elite Home Builders",
      date: "2025-05-03",
      value: "$12,500",
    },
    {
      id: "JOB-24-1235",
      title: "Bathroom Plumbing Repair",
      status: "completed",
      customer: "<PERSON>",
      customerAvatar: "/placeholder.svg",
      provider: "Quality Plumbing Co.",
      date: "2025-05-02",
      value: "$650",
    },
    {
      id: "JOB-24-1236",
      title: "Electrical Panel Upgrade",
      status: "pending",
      customer: "<PERSON>",
      customerAvatar: "/placeholder.svg",
      provider: null,
      date: "2025-05-04",
      value: "$1,850",
    }
  ];

  // Simplified platform metrics
  const platformMetrics = {
    activeJobs: 156,
    completedJobsThisMonth: 87,
    customerSatisfaction: 92
  };

  return (
    <div className="space-y-6">
      {/* Platform Stats Cards */}
      <PlatformStatsGrid metrics={platformMetrics} />

      {/* Recent Jobs Section */}
      <RecentJobsCard jobs={recentJobs} />
    </div>
  );
};
