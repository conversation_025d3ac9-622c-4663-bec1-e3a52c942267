import React from 'react';
import { Badge } from "@/components/ui/badge";

interface JobBookingStatusBadgeProps {
  status: string;
}

export const JobBookingStatusBadge = ({ status }: JobBookingStatusBadgeProps) => {
  // Standardize the status (convert to lowercase and handle variations)
  const normalizedStatus = status.toLowerCase().replace(/_/g, ' ');
  
  switch (normalizedStatus) {
    case "completed":
      return <Badge className="bg-green-100 text-green-800 border-green-200">Completed</Badge>;
    case "in progress":
    case "in_progress":
      return <Badge className="bg-blue-100 text-blue-800 border-blue-200">In Progress</Badge>;
    case "pending":
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>;
    case "scheduled":
      return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Scheduled</Badge>;
    case "cancelled":
      return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelled</Badge>;
    case "send_bids":
      return <Badge className="bg-indigo-100 text-indigo-800 border-indigo-200">Send Bids</Badge>;
    case "awaiting_payment":
      return <Badge className="bg-amber-100 text-amber-800 border-amber-200">Awaiting Payment</Badge>;
    default:
      return <Badge variant="outline" className="capitalize">{status.replace(/_/g, ' ')}</Badge>;
  }
};
