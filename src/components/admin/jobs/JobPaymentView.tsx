
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  CreditCard, 
  DollarSign, 
  CheckCircle, 
  AlertCircle, 
  Clock
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";

interface JobPaymentViewProps {
  jobId: string;
  jobTitle: string;
  providerName?: string;
  customerName: string;
  amount: string;
  status: "pending" | "in_progress" | "completed" | "cancelled";
}

export const JobPaymentView: React.FC<JobPaymentViewProps> = ({
  jobId,
  jobTitle,
  providerName,
  customerName,
  amount,
  status,
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = React.useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = React.useState(false);
  const [paymentNotes, setPaymentNotes] = React.useState("");
  const [paymentAmount, setPaymentAmount] = React.useState(amount.replace(/[^\d.]/g, ""));
  
  const handleProcessPayment = async () => {
    setLoading(true);
    
    // In a real app, this would be an API call to process the payment
    setTimeout(() => {
      toast({
        title: "Payment processed successfully",
        description: `Payment of $${paymentAmount} has been sent to ${providerName}.`,
      });
      setLoading(false);
      setShowConfirmDialog(false);
    }, 2000);
  };
  
  const getPaymentStatus = () => {
    switch(status) {
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Paid
          </Badge>
        );
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Cancelled
          </Badge>
        );
      case "in_progress":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Clock className="h-3 w-3 mr-1" />
            Processing
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
    }
  };
  
  return (
    <>
      <Card className="h-full">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <DollarSign className="h-5 w-5" />
            Payment Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col gap-1">
              <span className="text-sm text-gray-500">Status</span>
              <div>{getPaymentStatus()}</div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-gray-500">Job</span>
                <p className="font-medium">{jobTitle}</p>
              </div>
              <div>
                <span className="text-sm text-gray-500">Amount</span>
                <p className="font-medium text-lg">{amount}</p>
              </div>
              
              <div>
                <span className="text-sm text-gray-500">Customer</span>
                <p className="font-medium">{customerName}</p>
              </div>
              <div>
                <span className="text-sm text-gray-500">Provider</span>
                <p className="font-medium">{providerName || "Unassigned"}</p>
              </div>
            </div>
            
            <div className="border-t pt-4 mt-4">
              <h4 className="font-medium mb-2">Payment Timeline</h4>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-sm">
                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                  <span>Customer payment received</span>
                  <span className="text-gray-500 ml-auto">2025-04-20</span>
                </li>
                {status === "completed" && (
                  <li className="flex items-center gap-2 text-sm">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Provider payment sent</span>
                    <span className="text-gray-500 ml-auto">2025-04-22</span>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </CardContent>
        <CardFooter className="border-t pt-4 flex">
          {status === "completed" ? (
            <div className="w-full text-center text-green-600 flex items-center justify-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Payment completed
            </div>
          ) : (
            status !== "cancelled" && providerName && (
              <Button 
                onClick={() => setShowConfirmDialog(true)} 
                className="w-full" 
                disabled={status === "in_progress"}
              >
                <CreditCard className="h-5 w-5 mr-2" />
                {status === "in_progress" ? "Payment Processing..." : "Process Provider Payment"}
              </Button>
            )
          )}
        </CardFooter>
      </Card>
      
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Process Payment to Provider</DialogTitle>
            <DialogDescription>
              You are about to send payment to {providerName} for job {jobId}.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Payment Amount</Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input 
                  id="amount"
                  className="pl-9" 
                  value={paymentAmount}
                  onChange={(e) => setPaymentAmount(e.target.value.replace(/[^\d.]/g, ""))}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">Payment Notes (Optional)</Label>
              <Input
                id="notes"
                placeholder="Add any notes about this payment"
                value={paymentNotes}
                onChange={(e) => setPaymentNotes(e.target.value)}
              />
            </div>
            
            <div className="rounded-md bg-amber-50 p-4 text-sm text-amber-800 border border-amber-200">
              <p className="font-medium">Important:</p>
              <p>This action will send payment directly to the provider's account. Once processed, this action cannot be reversed.</p>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowConfirmDialog(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleProcessPayment} 
              disabled={loading || !paymentAmount}
            >
              {loading ? "Processing..." : "Confirm Payment"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
