
import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  MessageSquare,
  Paperclip,
  DollarSign,
  UserCheck,
  AlertCircle,
  CheckCircle2,
  X,
  FileText,
  ChevronLeft,
  ArrowLeft,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { JobAttachmentsView } from "./JobAttachmentsView";
import { JobMessagesView } from "./JobMessagesView";
import { JobPaymentView } from "./JobPaymentView";
import { AdminNotes } from "../shared/AdminNotes";
import { Job, PaymentStatus } from "@/types/jobs";

interface MobileJobDetailCardProps {
  job: Job;
  onClose: () => void;
  onAssignProvider?: (job: Job) => void;
  onCancelJob?: (job: Job) => void;
}

export const MobileJobDetailCard = ({
  job,
  onClose,
  onAssignProvider,
  onCancelJob,
}: MobileJobDetailCardProps) => {
  const [activeTab, setActiveTab] = useState("details");
  const { toast } = useToast();

  // Mock admin notes - in a real app this would come from your backend
  const [adminNotes, setAdminNotes] = useState([
    {
      text: "Customer requested job to be completed by end of month. Provider was notified about special materials requirements.",
      timestamp: "2025-04-16T10:00:00Z",
      admin: "Admin User"
    }
  ]);

  const handleAddNote = (note: string) => {
    const newNote = {
      text: note,
      timestamp: new Date().toISOString(),
      admin: "Admin User" // In a real app, this would be the current admin's name
    };
    setAdminNotes([newNote, ...adminNotes]);
    
    toast({
      title: "Note added",
      description: "The admin note has been added successfully."
    });
  };

  // Helper function to get status badge styling
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "open":
        return <Badge variant="warning">Open</Badge>;
      case "in progress":
        return <Badge variant="business">In Progress</Badge>;
      case "completed":
        return <Badge variant="success">Completed</Badge>;
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleAssignProvider = () => {
    if (onAssignProvider) {
      onAssignProvider(job);
    }
  };

  const handleCancelJob = () => {
    if (onCancelJob) {
      onCancelJob(job);
    }
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header with back button */}
      <div className="sticky top-0 z-10 bg-background border-b p-4 flex items-center">
        <Button variant="ghost" size="icon" onClick={onClose} className="mr-2">
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div className="flex-1">
          <h2 className="text-lg font-semibold truncate">{job.title}</h2>
          <p className="text-xs text-muted-foreground">{job.id}</p>
        </div>
        {getStatusBadge(job.status)}
      </div>

      <div className="flex-1 overflow-y-auto">
        {/* Job Summary */}
        <div className="p-4 pb-0">
          <p className="text-sm text-gray-700 mb-4">{job.description}</p>

          <div className="grid grid-cols-2 gap-y-2 mb-4 text-sm">
            <div className="flex items-center gap-1.5">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>{job.scheduledDate || "Not scheduled"}</span>
            </div>
            <div className="flex items-center gap-1.5">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span>{job.value}</span>
            </div>
            <div className="flex items-center gap-1.5 col-span-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span>{job.location || "123 Main St, San Francisco, CA 94105"}</span>
            </div>
          </div>

          {/* Customer & Provider */}
          <div className="flex flex-col gap-3 mb-4">
            <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
              <Avatar className="h-10 w-10">
                <AvatarImage src={job.customerAvatar} />
                <AvatarFallback>{job.customerName?.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="font-medium">{job.customerName}</p>
                <p className="text-xs text-muted-foreground">Customer</p>
              </div>
              <div className="flex gap-2">
                <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
                  <Phone className="h-4 w-4" />
                </Button>
                <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
                  <Mail className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {job.provider ? (
              <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={job.providerAvatar} />
                  <AvatarFallback>{job.provider?.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="font-medium">{job.provider}</p>
                  <p className="text-xs text-muted-foreground">Provider</p>
                </div>
                <div className="flex gap-2">
                  <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
                    <Phone className="h-4 w-4" />
                  </Button>
                  <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
                    <Mail className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <p className="text-sm text-muted-foreground">No provider assigned</p>
                <Button size="sm" variant="outline" onClick={handleAssignProvider}>
                  <UserCheck className="h-4 w-4 mr-1" /> Assign
                </Button>
              </div>
            )}
          </div>

          {/* Quick actions */}
          <div className="grid grid-cols-2 gap-2 mb-4">
            {job.status !== "Completed" && job.status !== "Cancelled" && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleCancelJob}
                className="w-full"
              >
                <X className="mr-2 h-4 w-4" /> Cancel Job
              </Button>
            )}
            {job.status === "Completed" && (
              <Button
                variant="outline"
                size="sm"
                className="w-full col-span-2 bg-green-50 border-green-200 text-green-700"
                disabled
              >
                <CheckCircle2 className="mr-2 h-4 w-4" /> Job Completed
              </Button>
            )}
            {job.status === "Cancelled" && (
              <Button
                variant="outline"
                size="sm"
                className="w-full col-span-2 bg-red-50 border-red-200 text-red-700"
                disabled
              >
                <AlertCircle className="mr-2 h-4 w-4" /> Job Cancelled
              </Button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-2">
          <div className="border-b">
            <TabsList className="w-full justify-start px-2 bg-transparent gap-2">
              <TabsTrigger value="details" className="data-[state=active]:bg-background rounded-none data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:border-b-2">
                Details
              </TabsTrigger>
              <TabsTrigger value="notes" className="data-[state=active]:bg-background rounded-none data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:border-b-2">
                Notes
              </TabsTrigger>
              <TabsTrigger value="messages" className="data-[state=active]:bg-background rounded-none data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:border-b-2">
                Messages
              </TabsTrigger>
              <TabsTrigger value="files" className="data-[state=active]:bg-background rounded-none data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:border-b-2">
                Files
              </TabsTrigger>
              <TabsTrigger value="payment" className="data-[state=active]:bg-background rounded-none data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:border-b-2">
                Payment
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="details" className="p-4 space-y-4 mt-0">
            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-3">Job Details</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-3 text-muted-foreground" />
                    <div>
                      <div className="text-muted-foreground">Created Date</div>
                      <div>{job.createdDate}</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-3 text-muted-foreground" />
                    <div>
                      <div className="text-muted-foreground">Scheduled Date</div>
                      <div>{job.scheduledDate || "Not scheduled yet"}</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-3 text-muted-foreground" />
                    <div>
                      <div className="text-muted-foreground">Location</div>
                      <div>{job.location || "123 Main St, San Francisco, CA 94105"}</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 mr-3 text-muted-foreground" />
                    <div>
                      <div className="text-muted-foreground">Value</div>
                      <div>{job.value}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-2">Job Description</h3>
                <p className="text-sm">
                  {job.description || 
                    "The customer has reported a leaking sink in their main bathroom. They've mentioned water damage on the cabinet below. This job requires a licensed plumber to diagnose and fix the issue, potentially replacing damaged pipes and seals."}
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notes" className="p-0 mt-0">
            <AdminNotes
              notes={adminNotes}
              onAddNote={handleAddNote}
              entityType="job"
              entityId={job.id}
            />
          </TabsContent>

          <TabsContent value="messages" className="p-0 mt-0">
            <JobMessagesView
              jobId={job.id}
              customerName={job.customerName}
              customerAvatar={job.customerAvatar}
              providerName={job.provider}
              providerAvatar={job.providerAvatar}
            />
          </TabsContent>

          <TabsContent value="files" className="p-0 mt-0">
            <JobAttachmentsView jobId={job.id} />
          </TabsContent>

          <TabsContent value="payment" className="p-0 mt-0">
            <JobPaymentView
              jobId={job.id}
              jobTitle={job.title}
              providerName={job.provider}
              customerName={job.customerName}
              amount={job.value}
              status={job.status === "Completed" ? "completed" : job.status === "Open" ? "pending" : "in_progress"}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
