
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Download, FileText, Image, Paperclip } from "lucide-react";

interface Attachment {
  id: string;
  name: string;
  type: "image" | "document" | "other";
  url: string;
  uploadedBy: string;
  uploadDate: string;
}

interface JobAttachmentsViewProps {
  jobId: string;
}

export const JobAttachmentsView: React.FC<JobAttachmentsViewProps> = ({ jobId }) => {
  const [attachments, setAttachments] = React.useState<Attachment[]>([]);
  const [loading, setLoading] = React.useState(true);

  // Fetch attachments for this job
  React.useEffect(() => {
    // In a real app, this would be an API call
    // For now, we'll use mock data
    setTimeout(() => {
      const mockAttachments: Attachment[] = [
        {
          id: "att-1",
          name: "sink_leak_photo.jpg",
          type: "image",
          url: "https://images.unsplash.com/photo-1565068178556-119dae8c5ab7?q=80&w=2070&auto=format&fit=crop",
          uploadedBy: "Customer",
          uploadDate: "2025-04-15",
        },
        {
          id: "att-2",
          name: "bathroom_overview.jpg",
          type: "image",
          url: "https://images.unsplash.com/photo-1584622650111-993a426fbf0a?q=80&w=2070&auto=format&fit=crop",
          uploadedBy: "Customer",
          uploadDate: "2025-04-15",
        },
        {
          id: "att-3",
          name: "plumbing_diagram.pdf",
          type: "document",
          url: "#", // In a real app, this would be a URL to the document
          uploadedBy: "Provider",
          uploadDate: "2025-04-16",
        },
        {
          id: "att-4",
          name: "parts_list.pdf",
          type: "document",
          url: "#", // In a real app, this would be a URL to the document
          uploadedBy: "Provider",
          uploadDate: "2025-04-16",
        },
      ];
      setAttachments(mockAttachments);
      setLoading(false);
    }, 800);
  }, [jobId]);

  const getIconForType = (type: string) => {
    switch (type) {
      case "image":
        return <Image className="h-5 w-5 text-blue-500" />;
      case "document":
        return <FileText className="h-5 w-5 text-orange-500" />;
      default:
        return <Paperclip className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Paperclip className="h-5 w-5" />
          Job Attachments ({attachments.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <p>Loading attachments...</p>
          </div>
        ) : attachments.length > 0 ? (
          <div className="space-y-3">
            {attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="flex items-center justify-between p-3 border rounded-md hover:bg-gray-50"
              >
                <div className="flex items-center gap-3">
                  {getIconForType(attachment.type)}
                  <div>
                    <p className="font-medium">{attachment.name}</p>
                    <p className="text-xs text-gray-500">
                      Uploaded by {attachment.uploadedBy} on {attachment.uploadDate}
                    </p>
                  </div>
                </div>
                {attachment.type === "image" ? (
                  <Button variant="ghost" size="sm" asChild>
                    <a href={attachment.url} target="_blank" rel="noopener noreferrer">
                      View
                    </a>
                  </Button>
                ) : (
                  <Button variant="ghost" size="sm">
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Paperclip className="h-10 w-10 mx-auto mb-2 text-gray-300" />
            <p>No attachments found for this job</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
