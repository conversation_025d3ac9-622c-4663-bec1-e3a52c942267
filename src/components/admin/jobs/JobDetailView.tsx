import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { JobMessagesView } from "./JobMessagesView";
import { JobAttachmentsView } from "./JobAttachmentsView";
import { JobPaymentView } from "./JobPaymentView";
import { AdminNotes } from "../shared/AdminNotes";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { X, MessageSquare, Paperclip, DollarSign, Calendar, MapPin, Phone, Mail, FileText } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface JobDetailProps {
  job: any;  // This should match your job data structure
  open: boolean;
  onClose: () => void;
}

export const JobDetailView: React.FC<JobDetailProps> = ({ job, open, onClose }) => {
  const [activeTab, setActiveTab] = useState("details");
  const { toast } = useToast();

  // Mock admin notes - in a real app this would come from your backend
  const [adminNotes, setAdminNotes] = useState([
    {
      text: "Customer requested job to be completed by end of month. Provider was notified about special materials requirements.",
      timestamp: "2025-04-16T10:00:00Z",
      admin: "Admin User"
    }
  ]);

  const handleAddNote = (note: string) => {
    const newNote = {
      text: note,
      timestamp: new Date().toISOString(),
      admin: "Admin User" // In a real app, this would be the current admin's name
    };
    setAdminNotes([newNote, ...adminNotes]);
    
    toast({
      title: "Note added",
      description: "The admin note has been added successfully."
    });
  };

  if (!job) return null;

  // Helper function to get status badge styling
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "open":
        return <Badge variant="warning">Open</Badge>;
      case "in progress":
        return <Badge variant="business">In Progress</Badge>;
      case "completed":
        return <Badge variant="success">Completed</Badge>;
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between border-b pb-4">
          <div>
            <h2 className="text-xl font-semibold flex items-center gap-2">
              {job.title} {getStatusBadge(job.status)}
            </h2>
            <p className="text-sm text-muted-foreground">{job.id}</p>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex-1 overflow-auto pt-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex flex-col h-full">
            <TabsList className="grid grid-cols-5 mb-6">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="notes">
                <FileText className="h-4 w-4 mr-2" />
                Admin Notes
              </TabsTrigger>
              <TabsTrigger value="messages">
                <MessageSquare className="h-4 w-4 mr-2" />
                Messages
              </TabsTrigger>
              <TabsTrigger value="attachments">
                <Paperclip className="h-4 w-4 mr-2" />
                Attachments
              </TabsTrigger>
              <TabsTrigger value="payment">
                <DollarSign className="h-4 w-4 mr-2" />
                Payment
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="flex-1 overflow-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Job Overview</h3>
                    <div className="space-y-2">
                      <div className="flex items-start gap-2">
                        <div className="w-5 h-5 mt-0.5 flex items-center justify-center">
                          <Calendar className="h-4 w-4 text-gray-500" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Scheduled Date</p>
                          <p>{job.scheduledDate || "Not scheduled yet"}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-2">
                        <div className="w-5 h-5 mt-0.5 flex items-center justify-center">
                          <MapPin className="h-4 w-4 text-gray-500" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Location</p>
                          <p>123 Main St, San Francisco, CA 94105</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-2">
                        <div className="w-5 h-5 mt-0.5 flex items-center justify-center">
                          <DollarSign className="h-4 w-4 text-gray-500" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Value</p>
                          <p className="font-medium">{job.value}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Job Description</h3>
                    <p className="text-gray-700">
                      {job.description || 
                        "The customer has reported a leaking sink in their main bathroom. They've mentioned water damage on the cabinet below. This job requires a licensed plumber to diagnose and fix the issue, potentially replacing damaged pipes and seals."}
                    </p>
                  </div>
                  
                  {job.hasNotes && (
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Admin Notes</h3>
                      <div className="bg-muted p-3 rounded-md">
                        <p className="text-sm">
                          <span className="font-medium">Admin User (2025-04-16):</span> Customer requested job to be completed by end of month. Provider was notified about special materials requirements.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Customer</h3>
                    <div className="border rounded-md p-4 space-y-3">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={job.customerAvatar} alt={job.customerName} />
                          <AvatarFallback>{job.customerName?.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{job.customerName}</p>
                          <p className="text-sm text-gray-500">Customer</p>
                        </div>
                      </div>
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-500" />
                          <span>(*************</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-gray-500" />
                          <span>{job.customerEmail || `${job.customerName.toLowerCase().replace(/\s/g, ".")}@example.com`}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Provider</h3>
                    {job.provider ? (
                      <div className="border rounded-md p-4 space-y-3">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={job.providerAvatar} alt={job.provider} />
                            <AvatarFallback>{job.provider?.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{job.provider}</p>
                            <p className="text-sm text-gray-500">Service Provider</p>
                          </div>
                        </div>
                        <div className="flex flex-col gap-2">
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-gray-500" />
                            <span>(*************</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-gray-500" />
                            <span>{`${job.provider.toLowerCase().replace(/\s/g, ".")}@example.com`}</span>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="border rounded-md p-4 text-center text-gray-500">
                        <p>No provider assigned yet</p>
                        <Button className="mt-2" variant="outline" size="sm">
                          Assign Provider
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="notes" className="flex-1 overflow-auto">
              <div className="max-w-3xl mx-auto">
                <AdminNotes
                  notes={adminNotes}
                  onAddNote={handleAddNote}
                  entityType="job"
                  entityId={job.id}
                />
              </div>
            </TabsContent>

            <TabsContent value="messages" className="flex-1 overflow-auto">
              <JobMessagesView
                jobId={job.id}
                customerName={job.customerName}
                customerAvatar={job.customerAvatar}
                providerName={job.provider}
                providerAvatar={job.providerAvatar}
              />
            </TabsContent>

            <TabsContent value="attachments" className="flex-1 overflow-auto">
              <JobAttachmentsView jobId={job.id} />
            </TabsContent>

            <TabsContent value="payment" className="flex-1 overflow-auto">
              <JobPaymentView
                jobId={job.id}
                jobTitle={job.title}
                providerName={job.provider}
                customerName={job.customerName}
                amount={job.value}
                status={job.status === "Completed" ? "completed" : job.status === "Open" ? "pending" : "in_progress"}
              />
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
