
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Paperclip, Send } from "lucide-react";
import { AdminRecipient, AdminMessage, addAdminMessage } from "@/utils/messagingUtils";

interface MessageComposerProps {
  recipient: AdminRecipient;
}

export const MessageComposer = ({ recipient }: MessageComposerProps) => {
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    setIsSending(true);
    try {
      // Create a new message
      const newMessage: AdminMessage = {
        id: `msg-${Date.now()}`,
        recipientId: recipient.id,
        recipientType: recipient.type,
        recipientName: recipient.name,
        content: message.trim(),
        isFromAdmin: true,
        timestamp: new Date().toISOString(),
        read: false
      };

      // Add message
      addAdminMessage(newMessage);
      
      // Clear input
      setMessage("");
      
      // Toast notification removed
    } catch (error) {
      console.error("Failed to send message:", error);
      // Error toast notification removed
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="border rounded-md p-3 bg-background">
      <Textarea
        placeholder={`Message ${recipient.name}...`}
        className="min-h-[80px] resize-none border-0 p-2 focus-visible:ring-0 focus-visible:ring-offset-0"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        disabled={isSending}
      />
      <div className="flex justify-between items-center mt-2">
        <Button variant="ghost" size="icon" disabled={isSending}>
          <Paperclip className="h-4 w-4" />
        </Button>
        <Button 
          onClick={handleSendMessage} 
          disabled={!message.trim() || isSending}
          className="px-3"
        >
          <Send className="h-4 w-4 mr-2" /> Send
        </Button>
      </div>
    </div>
  );
};
