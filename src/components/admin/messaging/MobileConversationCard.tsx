
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Flag, MessageSquare, Pin } from "lucide-react";
import { formatDistanceToNow } from 'date-fns';

interface MobileConversationCardProps {
  conversation: any;
  isActive: boolean;
  onClick: () => void;
  getStatusBadge: (status: string) => React.ReactNode;
}

export const MobileConversationCard: React.FC<MobileConversationCardProps> = ({
  conversation,
  isActive,
  onClick,
  getStatusBadge
}) => {
  // Format the timestamp to relative time with error handling
  const getFormattedTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      // Check if date is valid before formatting
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      console.error(`Error formatting date: ${timestamp}`, error);
      return 'Unknown time';
    }
  };
  
  const formattedTime = getFormattedTime(conversation.timestamp);
  
  return (
    <div 
      className={`
        p-4 rounded-xl mb-3 transition-all animate-fade-in border 
        ${isActive 
          ? 'border-l-4 border-l-indigo-500 bg-gradient-to-r from-indigo-50 to-white dark:from-indigo-900/30 dark:to-gray-800/20 shadow-md' 
          : 'bg-white dark:bg-gray-800/50 shadow-sm border-gray-100 dark:border-gray-700 hover:shadow-md'
        }
      `}
      onClick={onClick}
    >
      <div className="flex items-start gap-3">
        <Avatar className="h-12 w-12 rounded-xl border-2 border-gray-100 dark:border-gray-700 shadow-sm">
          <AvatarImage src={conversation.recipientAvatar} alt={conversation.recipientName} />
          <AvatarFallback className={`
            ${conversation.recipientType === 'provider' 
              ? 'bg-gradient-to-br from-green-400 to-teal-500 text-white' 
              : 'bg-gradient-to-br from-purple-400 to-indigo-500 text-white'
            }
          `}>
            {conversation.recipientName?.charAt(0)}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-center">
            <h3 className="font-semibold text-gray-800 dark:text-gray-200 truncate">
              {conversation.recipientName}
            </h3>
            <div className="flex items-center">
              {conversation.unread && (
                <Badge className="mr-2 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 border-0">
                  New
                </Badge>
              )}
              <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
                {formattedTime}
              </span>
            </div>
          </div>
          
          <div className="mt-1 flex items-start">
            <MessageSquare className="h-4 w-4 mr-1 mt-0.5 text-gray-400" />
            <p className="text-sm text-gray-600 dark:text-gray-300 truncate">{conversation.lastMessage}</p>
          </div>
          
          <div className="mt-2 flex items-center justify-between">
            <div className="flex items-center">
              <Badge 
                variant="outline" 
                className={`text-xs px-2 py-0.5 capitalize ${
                  conversation.recipientType === 'provider' 
                    ? 'border-green-200 text-green-800 bg-green-50 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700' 
                    : 'border-purple-200 text-purple-800 bg-purple-50 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-700'
                }`}
              >
                {conversation.recipientType}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              {conversation.pinned && (
                <div className="flex items-center">
                  <Pin size={14} className="text-amber-500 mr-1" />
                  <span className="text-xs text-amber-500">Pinned</span>
                </div>
              )}
              
              {conversation.flagged && (
                <div className="flex items-center">
                  <Flag size={14} className="text-rose-500 mr-1" />
                  <span className="text-xs text-rose-500">Flagged</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
