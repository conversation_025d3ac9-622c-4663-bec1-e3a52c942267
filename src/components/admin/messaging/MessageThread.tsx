
import { useState, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { getAdminMessages, AdminMessage } from "@/utils/messagingUtils";

interface MessageThreadProps {
  recipientId: string;
  recipientType: "provider" | "customer";
  recipientName: string;
  messageEndRef: React.RefObject<HTMLDivElement>;
}

export const MessageThread = ({
  recipientId,
  recipientType,
  recipientName,
  messageEndRef
}: MessageThreadProps) => {
  const [messages, setMessages] = useState<AdminMessage[]>([]);

  useEffect(() => {
    // Load messages for this conversation
    const conversationMessages = getAdminMessages(recipientId, recipientType);
    setMessages(conversationMessages);
  }, [recipientId, recipientType]);

  // Format timestamp for display
  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    
    const isToday = date.toDateString() === now.toDateString();
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isToday) {
      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (isYesterday) {
      return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <ScrollArea className="h-[calc(100%-80px)]">
      {messages.length > 0 ? (
        <div className="space-y-4 p-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${
                message.isFromAdmin ? "justify-end" : "justify-start"
              }`}
            >
              {!message.isFromAdmin && (
                <Avatar className="h-8 w-8 mr-2">
                  <AvatarFallback>{getInitials(recipientName)}</AvatarFallback>
                </Avatar>
              )}
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.isFromAdmin
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted"
                }`}
              >
                <p className="text-sm whitespace-pre-wrap break-words">{message.content}</p>
                <div className="text-xs mt-1 opacity-70 text-right">
                  {formatMessageTime(message.timestamp)}
                  {message.isFromAdmin && !message.read && (
                    <span className="ml-2">✓</span>
                  )}
                  {message.isFromAdmin && message.read && (
                    <span className="ml-2">✓✓</span>
                  )}
                </div>
              </div>
              {message.isFromAdmin && (
                <Avatar className="h-8 w-8 ml-2">
                  <AvatarFallback>AD</AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}
          <div ref={messageEndRef} />
        </div>
      ) : (
        <div className="flex items-center justify-center h-full">
          <p className="text-muted-foreground">No messages yet</p>
        </div>
      )}
    </ScrollArea>
  );
};
