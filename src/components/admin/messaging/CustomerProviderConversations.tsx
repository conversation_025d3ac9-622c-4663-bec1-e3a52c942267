
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Filter, MessageSquare, Eye, ArrowUpDown } from "lucide-react";

// Mock conversations between customers and providers
const mockConversations = [
  {
    id: "conv1",
    customerName: "<PERSON>",
    customerAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop",
    providerName: "Jane Smith",
    providerAvatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=50&h=50&fit=crop",
    lastMessage: "Could you provide a revised quote for the additional work?",
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    messageCount: 12,
    flagged: false,
    serviceType: "Plumbing"
  },
  {
    id: "conv2",
    customerName: "Sarah Wilson",
    customerAvatar: "",
    providerName: "Michael Johnson",
    providerAvatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=50&h=50&fit=crop",
    lastMessage: "I'll be available to complete the job next Monday.",
    timestamp: new Date(Date.now() - 7200000).toISOString(),
    messageCount: 8,
    flagged: true,
    serviceType: "Electrical"
  },
  {
    id: "conv3",
    customerName: "Emily Chen",
    customerAvatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=50&h=50&fit=crop",
    providerName: "David Rodriguez",
    providerAvatar: "",
    lastMessage: "Thank you for your payment. I've attached the receipt.",
    timestamp: new Date(Date.now() - 86400000).toISOString(),
    messageCount: 15,
    flagged: false,
    serviceType: "Cleaning"
  },
  {
    id: "conv4",
    customerName: "Robert Kim",
    customerAvatar: "",
    providerName: "Lisa Taylor",
    providerAvatar: "https://images.unsplash.com/photo-**********-94ddf0286df2?w=50&h=50&fit=crop",
    lastMessage: "I'm concerned about the timeline for this project.",
    timestamp: new Date(Date.now() - 172800000).toISOString(),
    messageCount: 10,
    flagged: true,
    serviceType: "Landscaping"
  },
  {
    id: "conv5",
    customerName: "Jennifer Martinez",
    customerAvatar: "https://images.unsplash.com/photo-**********-14d9def656e4?w=50&h=50&fit=crop",
    providerName: "Thomas Walker",
    providerAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop",
    lastMessage: "The repair is complete. Please let me know if you have any questions.",
    timestamp: new Date(Date.now() - 259200000).toISOString(),
    messageCount: 18,
    flagged: false,
    serviceType: "HVAC"
  }
];

// Mock messages for a conversation
const mockMessages = [
  {
    id: "msg1",
    sender: "customer",
    senderName: "John Doe",
    content: "Hello, I'm interested in your plumbing services. I have a leaky faucet that needs repair.",
    timestamp: new Date(Date.now() - 86400000).toISOString(),
  },
  {
    id: "msg2",
    sender: "provider",
    senderName: "Jane Smith",
    content: "Hi John, I'd be happy to help with your leaky faucet. Could you provide some more details or pictures of the issue?",
    timestamp: new Date(Date.now() - 82800000).toISOString(),
  },
  {
    id: "msg3",
    sender: "customer",
    senderName: "John Doe",
    content: "Sure, here's a picture of the faucet. It's been dripping constantly for about a week.",
    timestamp: new Date(Date.now() - 79200000).toISOString(),
    attachment: "faucet.jpg"
  },
  {
    id: "msg4",
    sender: "provider",
    senderName: "Jane Smith",
    content: "Thanks for the picture. This looks like a simple fix. I can come by tomorrow between 2-4pm. Does that work for you?",
    timestamp: new Date(Date.now() - 75600000).toISOString(),
  },
  {
    id: "msg5",
    sender: "customer",
    senderName: "John Doe",
    content: "That works perfectly. My address is 123 Main St, Apt 4B.",
    timestamp: new Date(Date.now() - 72000000).toISOString(),
  },
  {
    id: "msg6",
    sender: "provider",
    senderName: "Jane Smith",
    content: "Great, I've added you to my schedule. The estimated cost will be $85-120 depending on the parts needed.",
    timestamp: new Date(Date.now() - 68400000).toISOString(),
  },
  {
    id: "msg7",
    sender: "customer",
    senderName: "John Doe",
    content: "That sounds reasonable. Could you provide a revised quote for fixing an additional issue with my shower?",
    timestamp: new Date(Date.now() - 3600000).toISOString(),
  },
];

export const CustomerProviderConversations = () => {
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterOption, setFilterOption] = useState("all");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Format timestamp for display
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} day${diffInDays !== 1 ? 's' : ''} ago`;
    }
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Filter and sort conversations
  const filteredConversations = mockConversations
    .filter(conv => {
      const matchesSearch = searchQuery
        ? conv.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          conv.providerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          conv.serviceType.toLowerCase().includes(searchQuery.toLowerCase())
        : true;
      
      const matchesFilter = 
        filterOption === "all" ||
        (filterOption === "flagged" && conv.flagged);
      
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      const dateA = new Date(a.timestamp).getTime();
      const dateB = new Date(b.timestamp).getTime();
      return sortOrder === "desc" ? dateB - dateA : dateA - dateB;
    });

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search conversations..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Select value={filterOption} onValueChange={setFilterOption}>
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Conversations</SelectItem>
              <SelectItem value="flagged">Flagged Only</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            onClick={() => setSortOrder(sortOrder === "desc" ? "asc" : "desc")}
          >
            <ArrowUpDown className="h-4 w-4 mr-2" />
            {sortOrder === "desc" ? "Newest" : "Oldest"}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="table" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="table">Table View</TabsTrigger>
          <TabsTrigger value="card">Card View</TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer-Provider Conversations</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Provider</TableHead>
                    <TableHead>Service</TableHead>
                    <TableHead>Last Message</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredConversations.length > 0 ? (
                    filteredConversations.map((conv) => (
                      <TableRow key={conv.id}>
                        <TableCell>
                          <div className="flex items-center">
                            <Avatar className="h-8 w-8 mr-2">
                              <AvatarImage src={conv.customerAvatar} />
                              <AvatarFallback>{getInitials(conv.customerName)}</AvatarFallback>
                            </Avatar>
                            <span>{conv.customerName}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Avatar className="h-8 w-8 mr-2">
                              <AvatarImage src={conv.providerAvatar} />
                              <AvatarFallback>{getInitials(conv.providerName)}</AvatarFallback>
                            </Avatar>
                            <span>{conv.providerName}</span>
                          </div>
                        </TableCell>
                        <TableCell>{conv.serviceType}</TableCell>
                        <TableCell className="max-w-[300px] truncate">{conv.lastMessage}</TableCell>
                        <TableCell>{formatTime(conv.timestamp)}</TableCell>
                        <TableCell>
                          {conv.flagged && (
                            <Badge variant="destructive">Flagged</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <Drawer>
                            <DrawerTrigger asChild>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={() => setSelectedConversation(conv.id)}
                              >
                                <Eye className="h-4 w-4 mr-2" /> View
                              </Button>
                            </DrawerTrigger>
                            <DrawerContent>
                              <DrawerHeader>
                                <DrawerTitle>Conversation Between {conv.customerName} and {conv.providerName}</DrawerTitle>
                                <DrawerDescription>
                                  Service Type: {conv.serviceType} | Messages: {conv.messageCount}
                                </DrawerDescription>
                              </DrawerHeader>
                              <div className="p-4">
                                <Card>
                                  <CardContent className="p-0">
                                    <ScrollArea className="h-[500px]">
                                      <div className="p-4 space-y-4">
                                        {mockMessages.map((message) => (
                                          <div
                                            key={message.id}
                                            className={`flex ${
                                              message.sender === "provider" ? "justify-end" : "justify-start"
                                            }`}
                                          >
                                            {message.sender === "customer" && (
                                              <Avatar className="h-8 w-8 mr-2">
                                                <AvatarImage src={conv.customerAvatar} />
                                                <AvatarFallback>{getInitials(conv.customerName)}</AvatarFallback>
                                              </Avatar>
                                            )}
                                            <div
                                              className={`max-w-[70%] rounded-lg p-3 ${
                                                message.sender === "provider"
                                                  ? "bg-blue-100 text-blue-900"
                                                  : "bg-gray-100 text-gray-900"
                                              }`}
                                            >
                                              <div className="font-semibold text-sm mb-1">{message.senderName}</div>
                                              <div className="text-sm">{message.content}</div>
                                              {message.attachment && (
                                                <div className="mt-2 p-2 bg-white rounded border text-sm text-blue-600">
                                                  📎 {message.attachment}
                                                </div>
                                              )}
                                              <div className="text-xs mt-1 opacity-70 text-right">
                                                {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                              </div>
                                            </div>
                                            {message.sender === "provider" && (
                                              <Avatar className="h-8 w-8 ml-2">
                                                <AvatarImage src={conv.providerAvatar} />
                                                <AvatarFallback>{getInitials(conv.providerName)}</AvatarFallback>
                                              </Avatar>
                                            )}
                                          </div>
                                        ))}
                                      </div>
                                    </ScrollArea>
                                  </CardContent>
                                </Card>
                                <div className="flex justify-end gap-2 mt-4">
                                  <Button variant="outline">Flag Conversation</Button>
                                  <Button>Add Admin Note</Button>
                                </div>
                              </div>
                            </DrawerContent>
                          </Drawer>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <MessageSquare className="h-10 w-10 mb-2" />
                          <p>No conversations found</p>
                          <p className="text-sm">Try adjusting your search or filters</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="card" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredConversations.length > 0 ? (
              filteredConversations.map((conv) => (
                <Card key={conv.id} className={conv.flagged ? "border-red-300" : ""}>
                  <CardHeader className="p-4 pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-md font-medium">
                        {conv.serviceType} Conversation
                      </CardTitle>
                      {conv.flagged && (
                        <Badge variant="destructive">Flagged</Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center">
                        <Avatar className="h-8 w-8 mr-2">
                          <AvatarImage src={conv.customerAvatar} />
                          <AvatarFallback>{getInitials(conv.customerName)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{conv.customerName}</div>
                          <div className="text-xs text-muted-foreground">Customer</div>
                        </div>
                      </div>
                      <div className="text-xl">↔</div>
                      <div className="flex items-center">
                        <div className="text-right">
                          <div className="font-medium">{conv.providerName}</div>
                          <div className="text-xs text-muted-foreground">Provider</div>
                        </div>
                        <Avatar className="h-8 w-8 ml-2">
                          <AvatarImage src={conv.providerAvatar} />
                          <AvatarFallback>{getInitials(conv.providerName)}</AvatarFallback>
                        </Avatar>
                      </div>
                    </div>
                    <div className="border-t pt-3">
                      <div className="text-sm text-muted-foreground mb-1">Last message:</div>
                      <p className="text-sm line-clamp-2">{conv.lastMessage}</p>
                      <div className="flex justify-between items-center mt-3">
                        <div className="text-xs text-muted-foreground">{formatTime(conv.timestamp)}</div>
                        <Badge variant="outline">{conv.messageCount} messages</Badge>
                      </div>
                    </div>
                    <Drawer>
                      <DrawerTrigger asChild>
                        <Button 
                          className="w-full mt-3" 
                          variant="outline"
                          onClick={() => setSelectedConversation(conv.id)}
                        >
                          <Eye className="h-4 w-4 mr-2" /> View Conversation
                        </Button>
                      </DrawerTrigger>
                      <DrawerContent>
                        <DrawerHeader>
                          <DrawerTitle>Conversation Details</DrawerTitle>
                          <DrawerDescription>
                            {conv.customerName} and {conv.providerName} | {conv.serviceType}
                          </DrawerDescription>
                        </DrawerHeader>
                        <div className="p-4">
                          <Card>
                            <CardContent className="p-0">
                              <ScrollArea className="h-[500px]">
                                <div className="p-4 space-y-4">
                                  {mockMessages.map((message) => (
                                    <div
                                      key={message.id}
                                      className={`flex ${
                                        message.sender === "provider" ? "justify-end" : "justify-start"
                                      }`}
                                    >
                                      {message.sender === "customer" && (
                                        <Avatar className="h-8 w-8 mr-2">
                                          <AvatarImage src={conv.customerAvatar} />
                                          <AvatarFallback>{getInitials(conv.customerName)}</AvatarFallback>
                                        </Avatar>
                                      )}
                                      <div
                                        className={`max-w-[70%] rounded-lg p-3 ${
                                          message.sender === "provider"
                                            ? "bg-blue-100 text-blue-900"
                                            : "bg-gray-100 text-gray-900"
                                        }`}
                                      >
                                        <div className="font-semibold text-sm mb-1">{message.senderName}</div>
                                        <div className="text-sm">{message.content}</div>
                                        {message.attachment && (
                                          <div className="mt-2 p-2 bg-white rounded border text-sm text-blue-600">
                                            📎 {message.attachment}
                                          </div>
                                        )}
                                        <div className="text-xs mt-1 opacity-70 text-right">
                                          {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                        </div>
                                      </div>
                                      {message.sender === "provider" && (
                                        <Avatar className="h-8 w-8 ml-2">
                                          <AvatarImage src={conv.providerAvatar} />
                                          <AvatarFallback>{getInitials(conv.providerName)}</AvatarFallback>
                                        </Avatar>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </ScrollArea>
                            </CardContent>
                          </Card>
                        </div>
                      </DrawerContent>
                    </Drawer>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="col-span-3 flex flex-col items-center justify-center p-8 bg-gray-50 rounded-lg">
                <MessageSquare className="h-12 w-12 text-gray-400 mb-3" />
                <h3 className="text-lg font-medium mb-1">No conversations found</h3>
                <p className="text-sm text-gray-500">
                  Try adjusting your search or filter settings
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
