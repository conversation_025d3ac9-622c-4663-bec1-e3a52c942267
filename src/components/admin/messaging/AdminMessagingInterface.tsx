
import { useState, useEffect, useRef } from "react";
import { ConversationList } from "./ConversationList";
import { MessageThread } from "./MessageThread";
import { MessageComposer } from "./MessageComposer";
import { ConversationHeader } from "./ConversationHeader";
import { MobileBackButton } from "./MobileBackButton";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { AdminRecipient, AdminConversation, markConversationAsRead, getAdminConversations } from "@/utils/messagingUtils";
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CustomerProviderConversations } from "./CustomerProviderConversations";
import { MobileConversationCard } from "./MobileConversationCard";
import { MobileFilterBar } from "./MobileFilterBar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Message<PERSON>quare, <PERSON>lipboardList, AlertCircle, ArrowLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

export const AdminMessagingInterface = () => {
  const { isMobile } = useUIHelpers();
  const { toast } = useToast();
  const [selectedConversation, setSelectedConversation] = useState<AdminConversation | null>(null);
  const [showMessageThread, setShowMessageThread] = useState(!isMobile);
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const messageEndRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState("direct");
  const [conversations, setConversations] = useState<AdminConversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Fetch admin conversations from a utility function
  useEffect(() => {
    try {
      setIsLoading(true);
      setHasError(false);
      
      // This would typically be an API call in a real app
      const fetchedConversations = getAdminConversations();
      
      // Add validation for conversation data
      const validConversations = fetchedConversations.filter(conv => {
        try {
          // Check if timestamp is valid
          new Date(conv.timestamp).toISOString();
          return true;
        } catch (error) {
          console.error(`Invalid conversation data: ${JSON.stringify(conv)}`, error);
          return false;
        }
      });
      
      setConversations(validConversations);
    } catch (error) {
      console.error("Error fetching conversations:", error);
      setHasError(true);
      toast({
        title: "Error loading messages",
        description: "There was an error loading your messages. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Calculate counts for filter badges
  const unreadCount = conversations.filter(conv => conv.unread).length;
  const flaggedCount = conversations.filter(conv => conv.flagged).length;
  
  // Get filtered conversations based on search and filter
  const getFilteredConversations = () => {
    return conversations.filter(conversation => {
      const matchesSearch = searchQuery 
        ? conversation.recipientName.toLowerCase().includes(searchQuery.toLowerCase())
        : true;
      
      const matchesFilter = 
        filter === "all" ||
        (filter === "unread" && conversation.unread) ||
        (filter === "flagged" && conversation.flagged) ||
        (filter === "pinned" && conversation.pinned);
      
      return matchesSearch && matchesFilter;
    });
  };

  // Scroll to bottom when new messages are added
  useEffect(() => {
    if (messageEndRef.current && selectedConversation) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [selectedConversation]);

  const handleSelectConversation = (conversation: AdminConversation) => {
    try {
      setSelectedConversation(conversation);
      if (isMobile) {
        setShowMessageThread(true);
      }
      
      // Mark conversation as read when selected
      markConversationAsRead(conversation.recipientId, conversation.recipientType);
    } catch (error) {
      console.error("Error selecting conversation:", error);
      toast({
        title: "Error",
        description: "There was an error selecting this conversation. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleBackToList = () => {
    setShowMessageThread(false);
  };

  const getRecipientFromConversation = (conversation: AdminConversation): AdminRecipient => {
    return {
      id: conversation.recipientId,
      type: conversation.recipientType,
      name: conversation.recipientName,
      avatar: conversation.recipientAvatar
    };
  };
  
  // Helper function to get status badge based on recipient type
  const getStatusBadge = (recipientType: string) => {
    return recipientType === 'customer' 
      ? 'bg-purple-100 text-purple-800 border-purple-200'
      : 'bg-green-100 text-green-800 border-green-200';
  };

  const filteredConversations = getFilteredConversations();

  // Handle empty state or error state
  const renderEmptyOrErrorState = () => {
    if (hasError) {
      return (
        <div className="flex flex-col items-center justify-center h-[200px] p-6 text-center">
          <div className="h-12 w-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="h-6 w-6 text-red-500" />
          </div>
          <h3 className="text-lg font-medium mb-2">Something went wrong</h3>
          <p className="text-muted-foreground text-sm mb-4">
            We couldn't load your messages. Please try again.
          </p>
        </div>
      );
    }
    
    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center h-[200px]">
          <div className="h-8 w-8 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-muted-foreground">Loading conversations...</p>
        </div>
      );
    }
    
    if (filteredConversations.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-[200px] p-6 text-center">
          <div className="h-12 w-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
            <MessageSquare className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium mb-2">No messages found</h3>
          <p className="text-muted-foreground text-sm">
            {searchQuery 
              ? "Try adjusting your search or filters" 
              : "Start communicating with customers and providers"}
          </p>
        </div>
      );
    }
    
    return null;
  };

  return (
    <div className="flex flex-col h-[calc(100vh-64px)]">
      <div className="p-4 flex-grow overflow-hidden">
        <div className="mb-2 flex items-center">
          <MessageSquare className="h-5 w-5 mr-2 text-indigo-500" />
          <h2 className="text-2xl font-bold tracking-tight">Admin Messages</h2>
        </div>
        <p className="text-muted-foreground mb-4 animate-fade-in">Manage all communications across the platform</p>
        
        <Tabs
          defaultValue="direct"
          value={activeTab}
          onValueChange={(value) => {
            setActiveTab(value);
            setShowMessageThread(!isMobile); // Reset mobile view when switching tabs
          }}
          className="mb-4"
        >
          <TabsList className="w-full md:w-auto bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-950/40 dark:to-blue-950/40 border border-indigo-100 dark:border-indigo-900 p-1 shadow-sm rounded-lg">
            <TabsTrigger 
              value="direct" 
              className="text-sm md:text-base py-2.5 px-4 data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-blue-600 data-[state=active]:text-white data-[state=active]:shadow rounded-md"
            >
              <MessageSquare className="h-4 w-4 mr-1.5" />
              <span>Direct Messages</span>
            </TabsTrigger>
            <TabsTrigger 
              value="oversight" 
              className="text-sm md:text-base py-2.5 px-4 data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-blue-600 data-[state=active]:text-white data-[state=active]:shadow rounded-md"
            >
              <ClipboardList className="h-4 w-4 mr-1.5" />
              <span>Conversation Oversight</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="direct" className="mt-6 animate-fade-in">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 h-[calc(100vh-200px)]">
              {/* Conversation List - Show on desktop or on mobile when not viewing a thread */}
              {(!isMobile || !showMessageThread) && (
                <Card className="p-4 flex flex-col h-full md:col-span-1 overflow-hidden bg-gradient-to-b from-indigo-50 to-white dark:from-indigo-950/40 dark:to-gray-800 border-0 shadow-md rounded-xl">
                  {isMobile ? (
                    <>
                      <MobileFilterBar 
                        searchQuery={searchQuery}
                        onSearchQueryChange={setSearchQuery}
                        filter={filter}
                        onFilterChange={setFilter}
                        totalConversations={conversations.length}
                        unreadCount={unreadCount}
                        flaggedCount={flaggedCount}
                      />
                      
                      <div className="mt-3 mb-2 flex flex-wrap gap-2">
                        <Badge 
                          onClick={() => setFilter("all")} 
                          className={`cursor-pointer py-1.5 px-3 rounded-full text-xs font-medium ${
                            filter === "all" 
                              ? "bg-gradient-to-r from-indigo-500 to-blue-600 text-white border-0" 
                              : "bg-white text-gray-700 border border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          All
                        </Badge>
                        
                        <Badge 
                          onClick={() => setFilter("unread")} 
                          className={`cursor-pointer py-1.5 px-3 rounded-full text-xs font-medium ${
                            filter === "unread" 
                              ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0" 
                              : "bg-white text-gray-700 border border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          Unread {unreadCount > 0 && `(${unreadCount})`}
                        </Badge>
                        
                        <Badge 
                          onClick={() => setFilter("flagged")} 
                          className={`cursor-pointer py-1.5 px-3 rounded-full text-xs font-medium ${
                            filter === "flagged" 
                              ? "bg-gradient-to-r from-rose-500 to-pink-600 text-white border-0" 
                              : "bg-white text-gray-700 border border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          Flagged {flaggedCount > 0 && `(${flaggedCount})`}
                        </Badge>
                        
                        <Badge 
                          onClick={() => setFilter("pinned")} 
                          className={`cursor-pointer py-1.5 px-3 rounded-full text-xs font-medium ${
                            filter === "pinned" 
                              ? "bg-gradient-to-r from-orange-500 to-amber-500 text-white border-0" 
                              : "bg-white text-gray-700 border border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          Pinned
                        </Badge>
                      </div>
                      
                      <ScrollArea className="flex-grow pr-2 mt-3">
                        {renderEmptyOrErrorState() || (
                          filteredConversations.map((conversation) => (
                            <MobileConversationCard 
                              key={conversation.id}
                              conversation={conversation}
                              isActive={selectedConversation?.id === conversation.id}
                              onClick={() => handleSelectConversation(conversation)}
                              getStatusBadge={getStatusBadge}
                            />
                          ))
                        )}
                      </ScrollArea>
                    </>
                  ) : (
                    <ConversationList 
                      onSelectConversation={handleSelectConversation}
                      activeConversationId={selectedConversation?.id}
                      filter={filter}
                      onFilterChange={setFilter}
                      searchQuery={searchQuery}
                      onSearchQueryChange={setSearchQuery}
                    />
                  )}
                </Card>
              )}
              
              {/* Message Thread - Show on desktop or on mobile when viewing a thread */}
              {(!isMobile || showMessageThread) && (
                <Card className="p-4 flex flex-col h-full md:col-span-2 overflow-hidden bg-gradient-to-b from-blue-50 to-white dark:from-blue-950/40 dark:to-gray-800 border-0 shadow-md rounded-xl">
                  {selectedConversation ? (
                    <>
                      {isMobile && (
                        <div className="mb-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleBackToList}
                            className="flex items-center p-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-lg transition-colors"
                          >
                            <ArrowLeft className="h-4 w-4 mr-1" />
                            <span>Back to messages</span>
                          </Button>
                        </div>
                      )}
                      <ConversationHeader 
                        conversation={selectedConversation} 
                      />
                      
                      <div className="flex-grow overflow-hidden mb-4">
                        <MessageThread 
                          recipientId={selectedConversation.recipientId}
                          recipientType={selectedConversation.recipientType}
                          recipientName={selectedConversation.recipientName}
                          messageEndRef={messageEndRef}
                        />
                      </div>
                      
                      <MessageComposer 
                        recipient={getRecipientFromConversation(selectedConversation)}
                      />
                    </>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center max-w-md">
                        <div className="h-20 w-20 bg-indigo-50 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                          <MessageSquare className="h-10 w-10 text-indigo-500" />
                        </div>
                        <h3 className="text-lg font-medium mb-2">Select a conversation</h3>
                        <p className="text-muted-foreground">
                          Choose a conversation from the list to view messages and respond
                        </p>
                      </div>
                    </div>
                  )}
                </Card>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="oversight" className="mt-6 animate-fade-in">
            <Card className="p-4 bg-gradient-to-b from-blue-50 to-white dark:from-blue-950/40 dark:to-gray-800 border-0 shadow-md rounded-xl">
              <CustomerProviderConversations />
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
