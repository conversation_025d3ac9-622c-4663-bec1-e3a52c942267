
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Bell, Calendar, Check, UserPlus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useMediaQuery } from '@/hooks/use-media-query';
import { Button } from '@/components/ui/button';

export const NotificationCenter = () => {
  const isTablet = useMediaQuery('(min-width: 768px)');
  
  // Sample data for notifications
  const notifications = [
    {
      id: 1,
      title: "New Provider Registration",
      description: "<PERSON> has registered as a new provider",
      time: "5 minutes ago",
      icon: UserPlus,
      color: "text-blue-500 bg-blue-100",
      avatar: "/placeholder.svg",
      initials: "JS",
    },
    {
      id: 2,
      title: "Completed Job",
      description: "Plumbing job #1234 has been completed",
      time: "30 minutes ago",
      icon: Check,
      color: "text-green-500 bg-green-100",
      avatar: "/placeholder.svg",
      initials: "PJ",
    },
    {
      id: 3,
      title: "Upcoming Appointment",
      description: "Provider meeting scheduled at 2:00 PM",
      time: "1 hour ago",
      icon: Calendar,
      color: "text-purple-500 bg-purple-100",
      avatar: "/placeholder.svg",
      initials: "PM",
    },
  ];

  return (
    <Card className="h-full border-none shadow-none bg-transparent">
      <CardContent className="pt-2">
        <div className="space-y-4">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className={cn(
                "flex items-start p-3 rounded-lg transition-colors bg-white dark:bg-gray-800/60 shadow-sm border border-gray-100 dark:border-gray-700",
                isTablet ? "hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer" : ""
              )}
            >
              <div className={cn(
                "p-2 rounded-full mr-3 flex-shrink-0", 
                notification.color
              )}>
                <notification.icon className="h-4 w-4" />
              </div>
              <div className="flex-1 space-y-1">
                <p className="font-medium text-sm">{notification.title}</p>
                <p className="text-xs text-muted-foreground">
                  {notification.description}
                </p>
                <p className="text-xs text-muted-foreground/70">
                  {notification.time}
                </p>
              </div>
              <Avatar className="h-8 w-8 flex-shrink-0">
                <AvatarImage src={notification.avatar} alt="Avatar" />
                <AvatarFallback>{notification.initials}</AvatarFallback>
              </Avatar>
            </div>
          ))}
        </div>
        
        <div className="mt-4">
          <Button variant="outline" className="w-full text-sm bg-white dark:bg-gray-800" size="sm">
            View All Notifications
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
