
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";

type AdminMessage = {
  id: string;
  adminName: string;
  adminAvatar?: string;
  text: string;
  timestamp: string;
  isFromAdmin: boolean;
};

interface AdminMessageThreadProps {
  messages: AdminMessage[];
  entityName: string;
}

export const AdminMessageThread: React.FC<AdminMessageThreadProps> = ({ 
  messages, 
  entityName 
}) => {
  // Get initials from name for avatar fallback
  const getInitials = (name: string) => {
    const parts = name.split(" ");
    return (parts[0]?.charAt(0) || "") + (parts[1]?.charAt(0) || "");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Admin Communication</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="flex flex-col gap-4">
            {messages.length === 0 ? (
              <div className="text-center text-gray-400 py-8">
                No admin messages exchanged with {entityName} yet.
              </div>
            ) : (
              messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.isFromAdmin ? "justify-end" : "justify-start"}`}
                >
                  {!message.isFromAdmin && (
                    <Avatar className="h-8 w-8 mr-2">
                      <AvatarImage src={undefined} alt={entityName} />
                      <AvatarFallback>{getInitials(entityName)}</AvatarFallback>
                    </Avatar>
                  )}
                  
                  <div className={`rounded-lg px-4 py-3 max-w-md break-words
                    ${message.isFromAdmin
                      ? "bg-blue-50 text-blue-900 border border-blue-200"
                      : "bg-gray-50 text-gray-900 border border-gray-200"}
                  `}>
                    <div className="font-semibold mb-1">
                      {message.isFromAdmin ? message.adminName : entityName}
                    </div>
                    <div className="whitespace-pre-wrap">{message.text}</div>
                    <div className="text-xs mt-1 text-gray-500 text-right">
                      {new Date(message.timestamp).toLocaleString()}
                    </div>
                  </div>
                  
                  {message.isFromAdmin && (
                    <Avatar className="h-8 w-8 ml-2">
                      <AvatarImage src={message.adminAvatar} alt={message.adminName} />
                      <AvatarFallback>{getInitials(message.adminName)}</AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};
