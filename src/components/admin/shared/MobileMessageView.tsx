
import React, { useRef, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Flag, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";

interface Message {
  id: string;
  sender: string;
  content: string;
  timestamp: string;
}

interface Conversation {
  id: string;
  customer: {
    name: string;
    avatar?: string;
    initials: string;
  };
  provider: {
    name: string;
    avatar?: string;
    initials: string;
  };
  jobTitle: string;
  jobId: string;
  status: string;
  messages: Message[];
  flagged: boolean;
}

interface MobileMessageViewProps {
  conversation: Conversation;
  onBack: () => void;
  getStatusBadge: (status: string) => React.ReactNode;
}

export const MobileMessageView: React.FC<MobileMessageViewProps> = ({
  conversation,
  onBack,
  getStatusBadge
}) => {
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [conversation]);
  
  const handleFlagConversation = () => {
    toast({
      title: conversation.flagged ? "Conversation unflagged" : "Conversation flagged",
      description: `The conversation between ${conversation.customer.name} and ${conversation.provider.name} has been ${conversation.flagged ? "unflagged" : "flagged"}.`,
    });
  };
  
  // Check if conversation is valid to prevent errors
  if (!conversation) {
    return (
      <Card className="flex flex-col h-full p-4 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-10 w-10 text-red-500 mb-2 mx-auto" />
          <h3 className="font-medium">Error Loading Conversation</h3>
          <p className="text-sm text-muted-foreground mt-2">Unable to load conversation data</p>
          <Button onClick={onBack} className="mt-4">Go Back</Button>
        </div>
      </Card>
    );
  }
  
  return (
    <Card className="flex flex-col h-full p-4">
      {/* Header */}
      <div className="flex items-center justify-between pb-4 border-b">
        <div className="flex items-center gap-3">
          <Button 
            variant="ghost" 
            size="icon" 
            className="mr-1" 
            onClick={onBack}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex gap-1">
            <Avatar>
              <AvatarImage src={conversation.customer.avatar} alt={conversation.customer.name} />
              <AvatarFallback>{conversation.customer.initials}</AvatarFallback>
            </Avatar>
            <Avatar>
              <AvatarImage src={conversation.provider.avatar} alt={conversation.provider.name} />
              <AvatarFallback>{conversation.provider.initials}</AvatarFallback>
            </Avatar>
          </div>
          <div>
            <div className="flex items-center">
              <h4 className="font-medium text-sm">
                {conversation.customer.name} / {conversation.provider.name}
              </h4>
              {conversation.flagged && (
                <Flag size={14} className="text-red-500 ml-2" />
              )}
            </div>
            <div className="flex flex-wrap gap-2 items-center">
              <p className="text-xs text-muted-foreground">{conversation.jobTitle}</p>
              <span className="text-xs text-muted-foreground">{conversation.jobId}</span>
              {getStatusBadge(conversation.status)}
            </div>
          </div>
        </div>
        <div>
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm">Actions</Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[30%]">
              <SheetHeader>
                <SheetTitle>Conversation Actions</SheetTitle>
              </SheetHeader>
              <div className="grid gap-4 py-4">
                <Button 
                  variant={conversation.flagged ? "destructive" : "outline"}
                  size="sm"
                  className="flex justify-start items-center h-12"
                  onClick={handleFlagConversation}
                >
                  <Flag className="h-4 w-4 mr-2" />
                  {conversation.flagged ? 'Unflag Conversation' : 'Flag Conversation'}
                </Button>
                <Button 
                  variant="outline"
                  size="sm"
                  className="flex justify-start items-center h-12"
                >
                  <AlertCircle className="h-4 w-4 mr-2" />
                  Escalate Conversation
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
      
      {/* Messages */}
      <ScrollArea className="flex-grow p-4">
        <div className="space-y-4">
          {conversation.messages && conversation.messages.length > 0 ? (
            conversation.messages.map((message) => (
              <div 
                key={message.id} 
                className={`flex ${message.sender === 'provider' ? 'justify-end' : 'justify-start'}`}
              >
                {message.sender === 'customer' && (
                  <Avatar className="h-8 w-8 mr-2 mt-1">
                    <AvatarImage src={conversation.customer.avatar} alt={conversation.customer.name} />
                    <AvatarFallback>{conversation.customer.initials}</AvatarFallback>
                  </Avatar>
                )}
                <div 
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.sender === 'provider' 
                      ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100' 
                      : 'bg-muted'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <span className="text-xs opacity-70 block text-right mt-1">
                    {message.timestamp}
                  </span>
                </div>
                {message.sender === 'provider' && (
                  <Avatar className="h-8 w-8 ml-2 mt-1">
                    <AvatarImage src={conversation.provider.avatar} alt={conversation.provider.name} />
                    <AvatarFallback>{conversation.provider.initials}</AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))
          ) : (
            <div className="text-center p-4">
              <p className="text-muted-foreground">No messages in this conversation</p>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      
      {/* Admin Notes */}
      <div className="pt-4 border-t mt-auto">
        <h4 className="text-sm font-medium mb-2">Admin Notes</h4>
        <div className="bg-muted p-3 rounded-md">
          <p className="text-sm">
            <span className="font-medium">System:</span> This conversation is being monitored for quality assurance.
          </p>
        </div>
      </div>
    </Card>
  );
};
