
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { MessageSquare } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface AdminMessageComposerProps {
  open?: boolean;
  onClose?: () => void;
  recipientType?: "provider" | "customer";
  recipient?: {
    id: string;
    name: string;
    email: string;
  };
  // Add the missing prop
  onSendMessage?: (message: string) => void;
  placeholder?: string;
}

interface MessageFormData {
  subject: string;
  message: string;
}

export const AdminMessageComposer: React.FC<AdminMessageComposerProps> = ({
  open,
  onClose,
  recipientType,
  recipient,
  onSendMessage,
  placeholder = "Write your message here...",
}) => {
  const { toast } = useToast();
  const [isSending, setIsSending] = useState(false);
  const isDialogMode = open !== undefined && onClose !== undefined;

  const form = useForm<MessageFormData>({
    defaultValues: {
      subject: "",
      message: "",
    },
  });

  const onSubmit = async (data: MessageFormData) => {
    setIsSending(true);
    
    try {
      // Handle different modes - dialog or inline
      if (onSendMessage) {
        // Use the onSendMessage prop for inline mode
        onSendMessage(data.message);
      } else {
        // In a real application, this would be an API call
        // For now, we'll simulate it with a timeout
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        toast({
          title: "Message sent",
          description: `Your message has been sent to ${recipient?.name}.`,
        });
      }
      
      form.reset();
      if (onClose) {
        onClose();
      }
    } catch (error) {
      toast({
        title: "Failed to send message",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  // For inline mode (no dialog)
  if (!isDialogMode) {
    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="message"
            rules={{ required: "Message is required" }}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea 
                    placeholder={placeholder}
                    className="min-h-[80px] resize-none"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="flex justify-end">
            <Button type="submit" disabled={isSending}>
              {isSending ? "Sending..." : "Send Message"}
            </Button>
          </div>
        </form>
      </Form>
    );
  }

  // For dialog mode
  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Message to {recipientType === "provider" ? "Provider" : "Customer"}
          </DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid gap-2">
              <FormLabel>To</FormLabel>
              <div className="p-2 border rounded-md bg-muted/50">
                <div className="font-medium">{recipient?.name}</div>
                <div className="text-sm text-muted-foreground">{recipient?.email}</div>
                <div className="text-xs text-muted-foreground">{recipient?.id}</div>
              </div>
            </div>

            <FormField
              control={form.control}
              name="subject"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Subject (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="Message subject..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="message"
              rules={{ required: "Message is required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Message</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder={placeholder || "Write your message here..."} 
                      className="min-h-[200px] resize-none" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter className="pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSending}>
                {isSending ? "Sending..." : "Send Message"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
