
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";

interface AdminNote {
  text: string;
  timestamp: string;
  admin: string;
}

interface AdminNotesProps {
  notes: AdminNote[];
  onAddNote: (note: string) => void;
  entityType: string;
  entityId: string;
}

export const AdminNotes = ({ notes, onAddNote, entityType, entityId }: AdminNotesProps) => {
  const [newNote, setNewNote] = useState("");
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newNote.trim()) {
      onAddNote(newNote);
      setNewNote("");
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <h3 className="text-sm font-medium">Add Note</h3>
        <form onSubmit={handleSubmit}>
          <Textarea 
            placeholder={`Add internal notes for this ${entityType.toLowerCase()}...`}
            className="min-h-[100px]"
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
          />
          <div className="mt-2 flex justify-end">
            <Button type="submit" disabled={!newNote.trim()}>
              Add Note
            </Button>
          </div>
        </form>
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-2">Previous Notes</h3>
        {notes.length > 0 ? (
          <div className="space-y-3">
            {notes.map((note, index) => (
              <Card key={index}>
                <CardContent className="pt-4">
                  <p className="whitespace-pre-wrap">{note.text}</p>
                  <div className="mt-2 text-xs text-muted-foreground flex justify-between">
                    <span className="font-medium">{note.admin}</span>
                    <time dateTime={note.timestamp}>
                      {new Date(note.timestamp).toLocaleString()}
                    </time>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <p className="text-sm text-muted-foreground">No notes yet. Add the first note above.</p>
        )}
      </div>
    </div>
  );
};
