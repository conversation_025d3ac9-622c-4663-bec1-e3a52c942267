import { z } from "zod";

// Admin Profile Form Schema
export const adminProfileSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  roleId: z.number().positive("Please select a role"),
});

export type AdminProfileFormValues = z.infer<typeof adminProfileSchema>;

// Platform Settings Form Schema
export const platformSettingsSchema = z.object({
  maintenanceMode: z.enum(["disabled", "scheduled", "enabled"], {
    required_error: "Please select a maintenance mode",
  }),
  allowNewRegistrations: z.boolean(),
  autoApproveProviders: z.boolean(),
});

export type PlatformSettingsFormValues = z.infer<typeof platformSettingsSchema>;

// Notification Preferences Form Schema
export const notificationPreferencesSchema = z.object({
  emailNotifications: z.boolean(),
  activityAlerts: z.boolean(),
  notificationFrequency: z.enum(["real-time", "hourly", "daily"], {
    required_error: "Please select a notification frequency",
  }),
});

export type NotificationPreferencesFormValues = z.infer<typeof notificationPreferencesSchema>;

// Team Management Schema (placeholder for future implementation)
export const teamManagementSchema = z.object({
  // This is a placeholder for future implementation
  // Will be expanded when team management features are added
});

export type TeamManagementFormValues = z.infer<typeof teamManagementSchema>;