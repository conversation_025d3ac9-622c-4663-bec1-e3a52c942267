import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, MoreHorizontal, Mail, Phone } from "lucide-react";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { ScrollArea } from "@/components/ui/scroll-area";

type Project = {
  id: string;
  title: string;
  client: {
    name: string;
    avatar: string;
    initials: string;
    email?: string;
    phone?: string;
  };
  provider: {
    name: string;
    avatar: string;
    initials: string;
    email?: string;
    phone?: string;
  };
  status: string;
  date: string;
  amount: string;
  description?: string;
  location?: string;
};

export const ProjectsTable = () => {
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  
  // Sample data for projects
  const projects = [
    {
      id: "PRJ001",
      title: "Bathroom Renovation",
      client: {
        name: "John Smith",
        avatar: "/placeholder.svg",
        initials: "JS",
        email: "<EMAIL>",
        phone: "(*************"
      },
      provider: {
        name: "Mike's Plumbing",
        avatar: "/placeholder.svg",
        initials: "MP",
        email: "<EMAIL>",
        phone: "(*************"
      },
      status: "in_progress",
      date: "Feb 15, 2023",
      amount: "$1,200",
      description: "Complete renovation of the main bathroom including new fixtures, tiling, and plumbing updates.",
      location: "123 Main St, Portland, OR"
    },
    {
      id: "PRJ002",
      title: "Kitchen Remodeling",
      client: {
        name: "Sarah Johnson",
        avatar: "/placeholder.svg",
        initials: "SJ",
        email: "<EMAIL>",
        phone: "(*************"
      },
      provider: {
        name: "Elite Home Services",
        avatar: "/placeholder.svg",
        initials: "EH",
        email: "<EMAIL>",
        phone: "(*************"
      },
      status: "completed",
      date: "Jan 28, 2023",
      amount: "$3,500",
      description: "Full kitchen remodel including new cabinets, countertops, appliances, and flooring.",
      location: "456 Oak Ave, Seattle, WA"
    },
    {
      id: "PRJ003",
      title: "Electrical Repair",
      client: {
        name: "David Wilson",
        avatar: "/placeholder.svg",
        initials: "DW",
        email: "<EMAIL>",
        phone: "(*************"
      },
      provider: {
        name: "PowerFix Electric",
        avatar: "/placeholder.svg",
        initials: "PE",
        email: "<EMAIL>",
        phone: "(*************"
      },
      status: "scheduled",
      date: "Feb 21, 2023",
      amount: "$450",
      description: "Repair of faulty wiring in the living room and installation of new light fixtures.",
      location: "789 Pine St, Chicago, IL"
    },
    {
      id: "PRJ004",
      title: "Garden Landscaping",
      client: {
        name: "Jennifer Brown",
        avatar: "/placeholder.svg",
        initials: "JB",
        email: "<EMAIL>",
        phone: "(*************"
      },
      provider: {
        name: "Green Thumb Gardens",
        avatar: "/placeholder.svg",
        initials: "GT",
        email: "<EMAIL>",
        phone: "(*************"
      },
      status: "awaiting_payment",
      date: "Feb 18, 2023",
      amount: "$800",
      description: "Complete garden redesign including new plants, pathways, and a small water feature.",
      location: "101 Maple Rd, Austin, TX"
    },
  ];

  // Helper to render status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "in_progress":
        return <Badge className="bg-blue-500 hover:bg-blue-600">In Progress</Badge>;
      case "completed":
        return <Badge className="bg-green-500 hover:bg-green-600">Completed</Badge>;
      case "scheduled":
        return <Badge className="bg-amber-500 hover:bg-amber-600">Scheduled</Badge>;
      case "awaiting_payment":
        return <Badge className="bg-purple-500 hover:bg-purple-600">Awaiting Payment</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const handleViewDetails = (project: Project) => {
    setSelectedProject(project);
    setDetailsOpen(true);
  };

  const handleContactEmail = (type: 'client' | 'provider', email: string) => {
    window.location.href = `mailto:${email}`;
    toast({
      title: `Email to ${type}`,
      description: `Opening email to ${email}`,
    });
  };

  const handleContactPhone = (type: 'client' | 'provider', phone: string) => {
    window.location.href = `tel:${phone}`;
    toast({
      title: `Call to ${type}`,
      description: `Calling ${phone}`,
    });
  };

  // Render mobile card view
  if (isMobile) {
    return (
      <>
        <Card>
          <CardHeader>
            <CardTitle>Recent Projects</CardTitle>
          </CardHeader>
          <CardContent className="px-0 py-0">
            <div className="space-y-3">
              {projects.map(project => (
                <div 
                  key={project.id} 
                  className="border-b last:border-0 transition-colors hover:bg-gray-50"
                >
                  <div className="flex flex-col p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium text-base">{project.title}</h4>
                        <p className="text-xs text-muted-foreground mt-0.5">ID: {project.id}</p>
                      </div>
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-purple-700 mr-2">{project.amount}</span>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-[180px]">
                            <DropdownMenuItem onClick={() => handleViewDetails(project)}>
                              <Eye className="h-4 w-4 mr-2" /> View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleContactEmail('client', project.client.email || '')}>
                              <Mail className="h-4 w-4 mr-2" /> Email Client
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleContactPhone('client', project.client.phone || '')}>
                              <Phone className="h-4 w-4 mr-2" /> Call Client
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleContactEmail('provider', project.provider.email || '')}>
                              <Mail className="h-4 w-4 mr-2" /> Email Provider
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleContactPhone('provider', project.provider.phone || '')}>
                              <Phone className="h-4 w-4 mr-2" /> Call Provider
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3 text-sm mb-3">
                      <div className="bg-gray-50 p-2 rounded-md">
                        <p className="text-xs text-gray-500 mb-1">Client</p>
                        <div className="flex items-center">
                          <Avatar className="h-6 w-6 mr-2">
                            <AvatarImage src={project.client.avatar} alt={project.client.name} />
                            <AvatarFallback>{project.client.initials}</AvatarFallback>
                          </Avatar>
                          <span className="text-sm font-medium truncate">{project.client.name}</span>
                        </div>
                      </div>
                      <div className="bg-gray-50 p-2 rounded-md">
                        <p className="text-xs text-gray-500 mb-1">Provider</p>
                        <div className="flex items-center">
                          <Avatar className="h-6 w-6 mr-2">
                            <AvatarImage src={project.provider.avatar} alt={project.provider.name} />
                            <AvatarFallback>{project.provider.initials}</AvatarFallback>
                          </Avatar>
                          <span className="text-sm font-medium truncate">{project.provider.name}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center mt-2">
                      <div>
                        {getStatusBadge(project.status)}
                      </div>
                      <div className="flex items-center">
                        <p className="text-xs text-gray-500 mr-3">{project.date}</p>
                        <Button size="sm" variant="outline" onClick={() => handleViewDetails(project)}>
                          Details
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Project Details Dialog - Mobile View */}
        <MobileProjectDialog 
          project={selectedProject} 
          open={detailsOpen} 
          onClose={() => setDetailsOpen(false)}
          onContactClient={(method) => {
            if (method === 'email' && selectedProject?.client.email) {
              handleContactEmail('client', selectedProject.client.email);
            } else if (selectedProject?.client.phone) {
              handleContactPhone('client', selectedProject.client.phone);
            }
          }}
          onContactProvider={(method) => {
            if (method === 'email' && selectedProject?.provider.email) {
              handleContactEmail('provider', selectedProject.provider.email);
            } else if (selectedProject?.provider.phone) {
              handleContactPhone('provider', selectedProject.provider.phone);
            }
          }}
        />
      </>
    );
  }

  // Desktop table view remains unchanged
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Recent Projects</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Project</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Provider</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {projects.map((project) => (
                <TableRow key={project.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{project.title}</div>
                      <div className="text-sm text-muted-foreground">{project.id}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={project.client.avatar} />
                        <AvatarFallback>{project.client.initials}</AvatarFallback>
                      </Avatar>
                      <div>{project.client.name}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={project.provider.avatar} />
                        <AvatarFallback>{project.provider.initials}</AvatarFallback>
                      </Avatar>
                      <div>{project.provider.name}</div>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(project.status)}</TableCell>
                  <TableCell>{project.date}</TableCell>
                  <TableCell>{project.amount}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewDetails(project)}>
                          <Eye className="h-4 w-4 mr-2" /> View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleContactEmail('client', project.client.email || '')}>
                          <Mail className="h-4 w-4 mr-2" /> Email Client
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleContactPhone('client', project.client.phone || '')}>
                          <Phone className="h-4 w-4 mr-2" /> Call Client
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleContactEmail('provider', project.provider.email || '')}>
                          <Mail className="h-4 w-4 mr-2" /> Email Provider
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleContactPhone('provider', project.provider.phone || '')}>
                          <Phone className="h-4 w-4 mr-2" /> Call Provider
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Project Details Dialog - Desktop View */}
      {selectedProject && (
        <ProjectDetailsDialog 
          project={selectedProject} 
          open={detailsOpen} 
          onClose={() => setDetailsOpen(false)}
          onContactClient={(method) => {
            if (method === 'email') {
              handleContactEmail('client', selectedProject.client.email || '');
            } else {
              handleContactPhone('client', selectedProject.client.phone || '');
            }
          }}
          onContactProvider={(method) => {
            if (method === 'email') {
              handleContactEmail('provider', selectedProject.provider.email || '');
            } else {
              handleContactPhone('provider', selectedProject.provider.phone || '');
            }
          }}
        />
      )}
    </>
  );
};

interface ProjectDetailsDialogProps {
  project: Project;
  open: boolean;
  onClose: () => void;
  onContactClient: (method: 'email' | 'phone') => void;
  onContactProvider: (method: 'email' | 'phone') => void;
}

const ProjectDetailsDialog: React.FC<ProjectDetailsDialogProps> = ({
  project,
  open,
  onClose,
  onContactClient,
  onContactProvider
}) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <div className="p-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-2xl font-bold mb-1">{project.title}</h2>
              <p className="text-sm text-muted-foreground">Project ID: {project.id}</p>
            </div>
            <div>
              {getStatusBadge(project.status)}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-6">
            <div>
              <h3 className="text-lg font-medium mb-3">Project Details</h3>
              <div className="space-y-4">
                <div>
                  <span className="text-muted-foreground text-sm">Date:</span>
                  <p>{project.date}</p>
                </div>
                <div>
                  <span className="text-muted-foreground text-sm">Amount:</span>
                  <p className="font-semibold">{project.amount}</p>
                </div>
                <div>
                  <span className="text-muted-foreground text-sm">Location:</span>
                  <p>{project.location || "Not specified"}</p>
                </div>
                <div>
                  <span className="text-muted-foreground text-sm">Description:</span>
                  <p>{project.description || "No description available."}</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-3">Client</h3>
                <div className="p-4 border rounded-md">
                  <div className="flex items-center gap-3 mb-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={project.client.avatar} />
                      <AvatarFallback>{project.client.initials}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-semibold">{project.client.name}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => onContactClient('email')}
                      className="flex items-center justify-center"
                    >
                      <Mail className="h-4 w-4 mr-2" /> Email
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => onContactClient('phone')}
                      className="flex items-center justify-center"
                    >
                      <Phone className="h-4 w-4 mr-2" /> Call
                    </Button>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-3">Provider</h3>
                <div className="p-4 border rounded-md">
                  <div className="flex items-center gap-3 mb-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={project.provider.avatar} />
                      <AvatarFallback>{project.provider.initials}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-semibold">{project.provider.name}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => onContactProvider('email')}
                      className="flex items-center justify-center"
                    >
                      <Mail className="h-4 w-4 mr-2" /> Email
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => onContactProvider('phone')}
                      className="flex items-center justify-center"
                    >
                      <Phone className="h-4 w-4 mr-2" /> Call
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end">
            <Button variant="outline" onClick={onClose}>Close</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

interface MobileProjectDialogProps {
  project: Project | null;
  open: boolean;
  onClose: () => void;
  onContactClient: (method: 'email' | 'phone') => void;
  onContactProvider: (method: 'email' | 'phone') => void;
}

// New optimized mobile project dialog component
const MobileProjectDialog: React.FC<MobileProjectDialogProps> = ({
  project,
  open,
  onClose,
  onContactClient,
  onContactProvider
}) => {
  if (!project) return null;
  
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="p-0 max-w-md w-full">
        <DialogHeader className="px-4 py-3 border-b sticky top-0 bg-white z-10">
          <DialogTitle className="text-base font-medium">{project.title}</DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[70vh] overflow-y-auto px-4 py-3">
          <div className="space-y-4">
            {/* Status & ID */}
            <div className="flex justify-between items-center">
              <div>
                <p className="text-xs text-gray-500">Project ID</p>
                <p className="font-medium">{project.id}</p>
              </div>
              {getStatusBadge(project.status)}
            </div>
            
            {/* Details */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <h3 className="text-sm font-medium mb-2">Project Details</h3>
              
              <div className="space-y-2">
                <div>
                  <p className="text-xs text-gray-500">Date</p>
                  <p className="text-sm">{project.date}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Amount</p>
                  <p className="text-sm font-semibold">{project.amount}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Location</p>
                  <p className="text-sm">{project.location || "Not specified"}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Description</p>
                  <p className="text-sm">{project.description || "No description available"}</p>
                </div>
              </div>
            </div>
            
            {/* Client */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <h3 className="text-sm font-medium mb-2">Client</h3>
              <div className="flex items-center gap-3 mb-3">
                <Avatar className="h-9 w-9">
                  <AvatarImage src={project.client.avatar} alt={project.client.name} />
                  <AvatarFallback>{project.client.initials}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{project.client.name}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button 
                  size="sm" 
                  variant="outline"
                  className="h-8 text-xs"
                  onClick={() => onContactClient('email')}
                >
                  <Mail className="h-3 w-3 mr-1" /> Email
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  className="h-8 text-xs"
                  onClick={() => onContactClient('phone')}
                >
                  <Phone className="h-3 w-3 mr-1" /> Call
                </Button>
              </div>
            </div>
            
            {/* Provider */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <h3 className="text-sm font-medium mb-2">Provider</h3>
              <div className="flex items-center gap-3 mb-3">
                <Avatar className="h-9 w-9">
                  <AvatarImage src={project.provider.avatar} alt={project.provider.name} />
                  <AvatarFallback>{project.provider.initials}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{project.provider.name}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button 
                  size="sm" 
                  variant="outline"
                  className="h-8 text-xs"
                  onClick={() => onContactProvider('email')}
                >
                  <Mail className="h-3 w-3 mr-1" /> Email
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  className="h-8 text-xs"
                  onClick={() => onContactProvider('phone')}
                >
                  <Phone className="h-3 w-3 mr-1" /> Call
                </Button>
              </div>
            </div>
          </div>
        </ScrollArea>
        <div className="p-3 border-t bg-white sticky bottom-0">
          <Button onClick={onClose} className="w-full">Close</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper function to render status badges
const getStatusBadge = (status: string) => {
  switch (status) {
    case "in_progress":
      return <Badge className="bg-blue-500 hover:bg-blue-600">In Progress</Badge>;
    case "completed":
      return <Badge className="bg-green-500 hover:bg-green-600">Completed</Badge>;
    case "scheduled":
      return <Badge className="bg-amber-500 hover:bg-amber-600">Scheduled</Badge>;
    case "awaiting_payment":
      return <Badge className="bg-purple-500 hover:bg-purple-600">Awaiting Payment</Badge>;
    default:
      return <Badge>{status}</Badge>;
  }
};
