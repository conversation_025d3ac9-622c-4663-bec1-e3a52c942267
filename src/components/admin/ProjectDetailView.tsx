
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/hooks/use-toast";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import {
  Check,
  X,
  MessageSquare,
  Ban,
  User,
  Calendar,
  MapPin,
  Clock,
  Image as ImageIcon,
  FileText,
  Mail,
  Phone,
  DollarSign,
  Flag,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  ThumbsUp,
  ThumbsDown
} from "lucide-react";

interface ProjectDetailViewProps {
  project: any;
  open: boolean;
  onClose: () => void;
}

export const ProjectDetailView = ({ project, open, onClose }: ProjectDetailViewProps) => {
  const [activeTab, setActiveTab] = useState("details");
  const [confirmSuspend, setConfirmSuspend] = useState(false);
  const [mediaPreview, setMediaPreview] = useState<string | null>(null);
  
  const handleSuspendProject = () => {
    toast({
      title: "Project suspended",
      description: "The project has been suspended and all parties have been notified.",
    });
    setConfirmSuspend(false);
  };
  
  const handleApproveMedia = (media: string) => {
    toast({
      title: "Media approved",
      description: "The media has been approved and is now visible to all parties.",
    });
  };
  
  const handleRejectMedia = (media: string) => {
    toast({
      title: "Media rejected",
      description: "The media has been rejected and is no longer visible.",
    });
  };
  
  const handleContactCustomer = () => {
    toast({
      title: "Contacting customer",
      description: "Opening messaging interface to contact the customer.",
    });
  };
  
  const handleContactProvider = () => {
    toast({
      title: "Contacting provider",
      description: "Opening messaging interface to contact the provider.",
    });
  };
  
  if (!project) return null;
  
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case "in progress":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <RefreshCw className="h-3 w-3 mr-1" />
            In Progress
          </Badge>
        );
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <X className="h-3 w-3 mr-1" />
            Cancelled
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">{status}</Badge>
        );
    }
  };
  
  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl">
              Project Details {getStatusBadge(project.status)}
            </DialogTitle>
            <DialogDescription>
              {project.id} - {project.serviceType} service in {project.location}
            </DialogDescription>
          </DialogHeader>
          
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="details">
                <FileText className="h-4 w-4 mr-2" />
                Project Details
              </TabsTrigger>
              <TabsTrigger value="media">
                <ImageIcon className="h-4 w-4 mr-2" />
                Media ({project.images?.length || 0})
              </TabsTrigger>
              <TabsTrigger value="bids">
                <DollarSign className="h-4 w-4 mr-2" />
                Provider Bids
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="details">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="md:col-span-2">
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
                    
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Description</h4>
                        <p className="mt-1">{project.description}</p>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Service Type</h4>
                          <p className="mt-1">{project.serviceType}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Status</h4>
                          <p className="mt-1">{project.status}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Date</h4>
                          <div className="flex items-center mt-1">
                            <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                            {project.date}
                          </div>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Location</h4>
                          <div className="flex items-center mt-1">
                            <MapPin className="h-4 w-4 mr-1 text-gray-500" />
                            {project.location}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-semibold mb-4">Customer</h3>
                    <div className="flex items-center gap-3 mb-4">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback>{project.customer.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{project.customer}</p>
                        <p className="text-sm text-gray-500">Customer</p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full" onClick={handleContactCustomer}>
                        <Mail className="h-4 w-4 mr-2" />
                        Message Customer
                      </Button>
                      <Button variant="outline" className="w-full">
                        <Phone className="h-4 w-4 mr-2" />
                        Call Customer
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div className="flex justify-end mt-6 gap-2">
                <Button
                  variant="outline"
                  onClick={() => setConfirmSuspend(true)}
                  className="bg-red-50 text-red-700 hover:bg-red-100 hover:text-red-800 border-red-200"
                >
                  <Ban className="h-4 w-4 mr-2" />
                  Suspend Project
                </Button>
                <Button>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Message Parties
                </Button>
                <Button variant="outline">
                  <Flag className="h-4 w-4 mr-2" />
                  Flag Project
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="media">
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-lg font-semibold mb-4">Project Images</h3>
                  
                  {project.images && project.images.length > 0 ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {project.images.map((image: string, index: number) => (
                        <div key={index} className="relative group">
                          <img
                            src={image}
                            alt={`Project image ${index + 1}`}
                            className="rounded-lg w-full h-48 object-cover cursor-pointer"
                            onClick={() => setMediaPreview(image)}
                          />
                          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col items-center justify-center gap-2 rounded-lg">
                            <div className="flex gap-2">
                              <Button 
                                size="sm" 
                                variant="default" 
                                className="bg-green-600 hover:bg-green-700"
                                onClick={() => handleApproveMedia(image)}
                              >
                                <ThumbsUp className="h-4 w-4" />
                                <span className="sr-only">Approve</span>
                              </Button>
                              <Button 
                                size="sm" 
                                variant="destructive"
                                onClick={() => handleRejectMedia(image)}
                              >
                                <ThumbsDown className="h-4 w-4" />
                                <span className="sr-only">Reject</span>
                              </Button>
                            </div>
                            <Button 
                              size="sm" 
                              variant="secondary"
                              onClick={() => setMediaPreview(image)}
                            >
                              View
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <ImageIcon className="h-12 w-12 mx-auto text-gray-400" />
                      <h3 className="mt-2 text-lg font-medium text-gray-900">No images uploaded</h3>
                      <p className="mt-1 text-gray-500">There are no images attached to this project</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="bids">
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-lg font-semibold mb-4">Provider Bids</h3>
                  
                  <div className="space-y-4">
                    {[
                      {
                        provider: "Mike Smith",
                        amount: "$120",
                        timeframe: "Same day service",
                        status: "Accepted"
                      },
                      {
                        provider: "Sarah Johnson",
                        amount: "$150",
                        timeframe: "Next day service",
                        status: "Pending"
                      },
                      {
                        provider: "David Wilson",
                        amount: "$135",
                        timeframe: "Within 2 days",
                        status: "Rejected"
                      }
                    ].map((bid, index) => (
                      <Card key={index} className="border border-gray-200">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-10 w-10">
                                <AvatarFallback>{bid.provider.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium">{bid.provider}</p>
                                <p className="text-sm text-gray-500">Provider</p>
                              </div>
                            </div>
                            <div>
                              {bid.status === "Accepted" && (
                                <Badge className="bg-green-100 text-green-800 border-green-200">
                                  <Check className="h-3 w-3 mr-1" />
                                  Accepted
                                </Badge>
                              )}
                              {bid.status === "Pending" && (
                                <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                                  <Clock className="h-3 w-3 mr-1" />
                                  Pending
                                </Badge>
                              )}
                              {bid.status === "Rejected" && (
                                <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
                                  <X className="h-3 w-3 mr-1" />
                                  Rejected
                                </Badge>
                              )}
                            </div>
                          </div>
                          
                          <div className="mt-4 grid grid-cols-2 gap-2">
                            <div>
                              <p className="text-sm text-gray-500">Bid Amount</p>
                              <p className="text-lg font-semibold">{bid.amount}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Timeframe</p>
                              <p>{bid.timeframe}</p>
                            </div>
                          </div>
                          
                          <div className="mt-4 flex justify-end gap-2">
                            <Button variant="outline" size="sm" onClick={handleContactProvider}>
                              <MessageSquare className="h-4 w-4 mr-2" />
                              Contact
                            </Button>
                            {bid.status === "Pending" && (
                              <>
                                <Button size="sm" variant="default" className="bg-green-600 hover:bg-green-700">
                                  <Check className="h-4 w-4 mr-1" />
                                  Approve
                                </Button>
                                <Button size="sm" variant="destructive">
                                  <X className="h-4 w-4 mr-1" />
                                  Reject
                                </Button>
                              </>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
      
      <Dialog open={mediaPreview !== null} onOpenChange={() => setMediaPreview(null)}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Media Preview</DialogTitle>
          </DialogHeader>
          
          <div className="flex items-center justify-center mt-4">
            <img 
              src={mediaPreview || ''} 
              alt="Media preview" 
              className="max-h-[60vh] max-w-full object-contain rounded-lg"
            />
          </div>
          
          <DialogFooter className="flex justify-between">
            <div className="flex gap-2">
              <Button 
                variant="default" 
                className="bg-green-600 hover:bg-green-700"
                onClick={() => {
                  handleApproveMedia(mediaPreview as string);
                  setMediaPreview(null);
                }}
              >
                <ThumbsUp className="h-4 w-4 mr-2" />
                Approve
              </Button>
              <Button 
                variant="destructive"
                onClick={() => {
                  handleRejectMedia(mediaPreview as string);
                  setMediaPreview(null);
                }}
              >
                <ThumbsDown className="h-4 w-4 mr-2" />
                Reject
              </Button>
            </div>
            <Button variant="outline" onClick={() => setMediaPreview(null)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      <Dialog open={confirmSuspend} onOpenChange={setConfirmSuspend}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Suspend Project
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to suspend this project? This will halt all activities and notify all parties involved.
            </DialogDescription>
          </DialogHeader>
          
          <div className="bg-red-50 border border-red-100 rounded-md p-4 my-4">
            <h4 className="font-medium flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <span className="text-red-800">Warning</span>
            </h4>
            <p className="text-sm text-red-700 mt-1">
              Project suspension should only be used for serious policy violations or disputes. Suspensions can be reversed later if needed.
            </p>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmSuspend(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleSuspendProject}>
              <Ban className="h-4 w-4 mr-2" />
              Suspend Project
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
