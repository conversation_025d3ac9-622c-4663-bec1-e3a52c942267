
import { <PERSON><PERSON> } from "@/components/ui/button";
import { X, RefreshCw, Calendar, User, Phone, Mail, CreditCard, DollarSign, ArrowLeft, MapPin } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

interface MobilePaymentDetailCardProps {
  payment: any;
  onClose: () => void;
  onRefund: (payment: any) => void;
}

export function MobilePaymentDetailCard({ payment, onClose, onRefund }: MobilePaymentDetailCardProps) {
  // Helper function for status badge styling
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "Paid":
        return <Badge variant="outline" className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-green-300 font-medium">Paid</Badge>;
      case "Pending":
        return <Badge variant="outline" className="bg-gradient-to-r from-amber-400 to-yellow-500 text-white border-yellow-300 font-medium">Pending</Badge>;
      case "Refunded":
        return <Badge variant="outline" className="bg-gradient-to-r from-red-400 to-rose-500 text-white border-red-300 font-medium">Refunded</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getServiceGradient = (serviceType: string) => {
    if (!serviceType) return "from-gray-100 to-gray-200";
    
    const service = (serviceType || '').toLowerCase();
    if (service.includes('plumb')) {
      return "from-blue-100 to-blue-200";
    } else if (service.includes('electric')) {
      return "from-yellow-100 to-yellow-200";
    } else if (service.includes('clean')) {
      return "from-green-100 to-green-200";
    } else if (service.includes('renov')) {
      return "from-orange-100 to-orange-200";
    } else if (service.includes('landscap')) {
      return "from-emerald-100 to-emerald-200";
    } else {
      return "from-purple-100 to-purple-200";
    }
  };

  // Get initial for avatar
  const getInitials = (name: string) => {
    if (!name) return "?";
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <div className="flex flex-col h-full">
      <div className={`p-4 sticky top-0 z-10 bg-gradient-to-r ${getServiceGradient(payment.serviceType)} shadow-sm flex justify-between items-center`}>
        <div className="flex items-center">
          <Button 
            size="icon" 
            variant="ghost" 
            onClick={onClose} 
            className="mr-2 rounded-full h-8 w-8 hover:bg-white/20"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h2 className="text-xl font-bold">Payment Details</h2>
            <p className="text-sm text-gray-600">{payment.id}</p>
          </div>
        </div>
        <div>
          {getStatusBadge(payment.status)}
        </div>
      </div>
      
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-5">
          <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
            <div className="flex items-center mb-4">
              <div className="bg-blue-50 p-2 rounded-lg mr-3">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-sm uppercase text-gray-500 font-medium">Amount</h3>
                <p className="text-2xl font-bold text-green-600">${payment.amount.toFixed(2)}</p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-xs text-gray-500 mb-1">Service Type</p>
                <p className="font-medium">{payment.serviceType || "General Service"}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Date</p>
                <div className="flex items-center">
                  <Calendar className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
                  <p>{new Date(payment.date).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100 animate-fade-in" style={{ animationDelay: "100ms" }}>
            <h3 className="text-sm uppercase text-gray-500 font-medium mb-3">Customer</h3>
            <div className="flex items-center gap-3 mb-3">
              <Avatar className="h-12 w-12">
                <AvatarFallback className="bg-gradient-to-br from-blue-400 to-blue-600 text-white">{getInitials(payment.customerName)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-semibold text-lg">{payment.customerName}</p>
                <p className="text-sm text-gray-500">Customer ID: {payment.customerId || "CUS-" + Math.floor(1000 + Math.random() * 9000)}</p>
              </div>
            </div>
            <div className="space-y-2 mt-4">
              <div className="flex items-center text-sm">
                <Mail className="h-4 w-4 mr-2 text-blue-500" />
                <p>{payment.customerEmail || `${payment.customerName.toLowerCase().replace(/\s/g, '.')}@example.com`}</p>
              </div>
              <div className="flex items-center text-sm">
                <Phone className="h-4 w-4 mr-2 text-green-500" />
                <p>{payment.customerPhone || "(*************"}</p>
              </div>
              {payment.customerAddress && (
                <div className="flex items-center text-sm">
                  <MapPin className="h-4 w-4 mr-2 text-red-500" />
                  <p>{payment.customerAddress}</p>
                </div>
              )}
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100 animate-fade-in" style={{ animationDelay: "200ms" }}>
            <h3 className="text-sm uppercase text-gray-500 font-medium mb-3">Provider</h3>
            <div className="flex items-center gap-3 mb-3">
              <Avatar className="h-12 w-12">
                <AvatarFallback className="bg-gradient-to-br from-green-400 to-green-600 text-white">{getInitials(payment.providerName)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-semibold text-lg">{payment.providerName}</p>
                <p className="text-sm text-gray-500">Provider ID: {payment.providerId || "PRO-" + Math.floor(1000 + Math.random() * 9000)}</p>
              </div>
            </div>
            <div className="space-y-2 mt-4">
              <div className="flex items-center text-sm">
                <Mail className="h-4 w-4 mr-2 text-blue-500" />
                <p>{payment.providerEmail || `contact@${payment.providerName.toLowerCase().replace(/\s/g, '')}.com`}</p>
              </div>
              <div className="flex items-center text-sm">
                <Phone className="h-4 w-4 mr-2 text-green-500" />
                <p>{payment.providerPhone || "(*************"}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100 animate-fade-in" style={{ animationDelay: "300ms" }}>
            <h3 className="text-sm uppercase text-gray-500 font-medium mb-3">Payment Details</h3>
            <div className="flex items-center mb-4">
              <div className="p-2 bg-gray-100 rounded-lg mr-3">
                <CreditCard className="h-5 w-5 text-gray-700" />
              </div>
              <div>
                <p className="text-sm font-medium">Credit Card</p>
                <p className="text-xs text-gray-500">•••• {payment.cardLast4}</p>
              </div>
            </div>
            
            <div className="space-y-2 border-t pt-3 mt-2">
              <h4 className="text-xs uppercase text-gray-500 mb-2">Transaction Timeline</h4>
              <div className="flex items-center gap-2 text-sm p-2 rounded-lg bg-green-50">
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                <span>Payment received</span>
                <span className="text-gray-500 ml-auto text-xs">{new Date(payment.date).toLocaleDateString()}</span>
              </div>
              {payment.status === "Refunded" && (
                <div className="flex items-center gap-2 text-sm p-2 rounded-lg bg-red-50">
                  <div className="h-2 w-2 rounded-full bg-red-500"></div>
                  <span>Payment refunded</span>
                  <span className="text-gray-500 ml-auto text-xs">{new Date().toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </ScrollArea>
      
      <div className="p-4 border-t bg-gray-50 sticky bottom-0 shadow-inner">
        <div className="flex gap-3">
          {payment.status === "Paid" && (
            <Button 
              className="flex-1 border-red-200 bg-gradient-to-r from-red-400 to-rose-500 text-white hover:from-red-500 hover:to-rose-600"
              onClick={() => onRefund(payment)}
            >
              <RefreshCw className="h-4 w-4 mr-2" /> Issue Refund
            </Button>
          )}
          <Button 
            onClick={onClose} 
            className="flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
