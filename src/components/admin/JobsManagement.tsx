
import { useState } from "react";
import { 
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow 
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Search, MoreVertical, Eye, RefreshCw, Filter, X, Calendar, UserRound, Phone, Mail } from "lucide-react";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { MobilePaymentDetailCard } from "./payments/MobilePaymentDetailCard";
import { useMediaQuery } from "@/hooks/use-media-query";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// Changed export to match the import in AdminDashboard.tsx
export const JobsManagement = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPayment, setSelectedPayment] = useState<any | null>(null);
  const [showPaymentDetails, setShowPaymentDetails] = useState(false);
  const [showMobileDetails, setShowMobileDetails] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Mock data - in a real app, this would come from an API
  const payments = [
    {
      id: "TRX-1001",
      customerName: "Sarah Johnson",
      providerName: "Mike's Home Improvement",
      amount: 450.00,
      status: "Paid",
      date: "2025-04-15",
      cardLast4: "4242",
      serviceType: "Plumbing",
      initials: "SJ"
    },
    {
      id: "TRX-1002",
      customerName: "David Wilson",
      providerName: "Electro Solutions",
      amount: 285.75,
      status: "Pending",
      date: "2025-04-16",
      cardLast4: "8735",
      serviceType: "Electrical",
      initials: "DW"
    },
    {
      id: "TRX-1003",
      customerName: "Emily Davis",
      providerName: "Green Thumb Landscaping",
      amount: 125.00,
      status: "Paid",
      date: "2025-04-10",
      cardLast4: "9012",
      serviceType: "Landscaping",
      initials: "ED"
    },
    {
      id: "TRX-1004",
      customerName: "James Miller",
      providerName: "Mike's Home Improvement",
      amount: 750.00,
      status: "Refunded",
      date: "2025-04-08",
      cardLast4: "6789",
      serviceType: "Renovation",
      initials: "JM"
    },
    {
      id: "TRX-1005",
      customerName: "Lisa Parker",
      providerName: "Clean Pro Services",
      amount: 195.00,
      status: "Pending",
      date: "2025-04-18",
      cardLast4: "3456",
      serviceType: "Cleaning",
      initials: "LP"
    }
  ];

  // Filter payments based on search query and status filter
  const filteredPayments = payments.filter(payment => {
    const matchesSearch = 
      payment.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.providerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.id.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || payment.status.toLowerCase() === statusFilter.toLowerCase();
    
    return matchesSearch && matchesStatus;
  });

  // Helper function for status badge styling
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "Paid":
        return <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 font-medium">Paid</Badge>;
      case "Pending":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200 font-medium">Pending</Badge>;
      case "Refunded":
        return <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200 font-medium">Refunded</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleViewDetails = (payment: any) => {
    setSelectedPayment(payment);
    if (isMobile) {
      setShowMobileDetails(true);
    } else {
      setShowPaymentDetails(true);
    }
  };

  const handleRefund = (payment: any) => {
    // In a real app, this would be an API call
    toast({
      title: "Payment refunded",
      description: `Payment ${payment.id} has been refunded successfully.`,
    });
    
    // Update the payment status in the UI
    if (selectedPayment && selectedPayment.id === payment.id) {
      setSelectedPayment({
        ...selectedPayment,
        status: "Refunded"
      });
    }
  };

  // Get card gradient based on service type
  const getServiceGradient = (serviceType: string) => {
    if (!serviceType) return "from-gray-100 to-gray-200";
    
    const service = serviceType.toLowerCase();
    if (service.includes('plumb')) {
      return "from-blue-100 to-blue-200 border-l-4 border-blue-500";
    } else if (service.includes('electric')) {
      return "from-yellow-100 to-yellow-200 border-l-4 border-yellow-500";
    } else if (service.includes('clean')) {
      return "from-green-100 to-green-200 border-l-4 border-green-500";
    } else if (service.includes('renov')) {
      return "from-orange-100 to-orange-200 border-l-4 border-orange-500";
    } else if (service.includes('landscap')) {
      return "from-emerald-100 to-emerald-200 border-l-4 border-emerald-500";
    } else {
      return "from-purple-100 to-purple-200 border-l-4 border-purple-500";
    }
  };

  // Mobile payment card component - enhanced with better design
  const MobilePaymentCard = ({ payment }: { payment: any }) => (
    <div 
      className={`border rounded-lg shadow-sm mb-3 bg-gradient-to-r ${getServiceGradient(payment.serviceType)} overflow-hidden`}
      onClick={() => handleViewDetails(payment)}
    >
      <div className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div>
            <h3 className="font-medium text-gray-800">{payment.id}</h3>
            <div className="flex items-center text-xs text-gray-500 mt-0.5">
              <Calendar className="h-3 w-3 mr-1" />
              <span>{new Date(payment.date).toLocaleDateString()}</span>
            </div>
          </div>
          {getStatusBadge(payment.status)}
        </div>
        
        <div className="bg-white/50 rounded-md p-2 mb-3">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8 border border-gray-200">
              <AvatarFallback>{payment.initials}</AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-sm truncate">{payment.customerName}</p>
              <p className="text-xs text-gray-500 truncate">{payment.providerName}</p>
            </div>
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-lg font-semibold text-gray-900">${payment.amount.toFixed(2)}</span>
          <Button
            size="sm"
            variant="outline"
            className="bg-white hover:bg-gray-50"
            onClick={(e) => {
              e.stopPropagation();
              handleViewDetails(payment);
            }}
          >
            <Eye className="h-3.5 w-3.5 mr-1" />
            Details
          </Button>
        </div>
      </div>
    </div>
  );

  // Render desktop or mobile view based on screen size
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Manage Jobs</h1>
        <div className="flex items-center gap-2">
          {isMobile ? (
            <>
              <Sheet open={filterOpen} onOpenChange={setFilterOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="bottom" className="h-[50vh]">
                  <SheetHeader className="mb-4">
                    <SheetTitle>Filter Jobs</SheetTitle>
                  </SheetHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-1 block">Search</label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search jobs..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-9"
                        />
                        {searchQuery && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                            onClick={() => setSearchQuery("")}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium mb-1 block">Job Status</label>
                      <Select 
                        value={statusFilter} 
                        onValueChange={setStatusFilter}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="paid">Paid</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="refunded">Refunded</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="pt-4">
                      <Button 
                        className="w-full bg-indigo-600 hover:bg-indigo-700" 
                        onClick={() => setFilterOpen(false)}
                      >
                        Apply Filters
                      </Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
              
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search jobs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9 w-full rounded-full"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                    onClick={() => setSearchQuery("")}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </>
          ) : (
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search jobs..."
                className="pl-8 w-[250px] md:w-[300px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          )}
        </div>
      </div>

      {!isMobile ? (
        <Table>
          <TableCaption>A list of all jobs on the platform.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Job ID</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Provider</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPayments.map((payment) => (
              <TableRow key={payment.id}>
                <TableCell className="font-medium">{payment.id}</TableCell>
                <TableCell>{payment.customerName}</TableCell>
                <TableCell>{payment.providerName}</TableCell>
                <TableCell>${payment.amount.toFixed(2)}</TableCell>
                <TableCell>{getStatusBadge(payment.status)}</TableCell>
                <TableCell>{new Date(payment.date).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewDetails(payment)}>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>View Details</span>
                      </DropdownMenuItem>
                      {payment.status === "Paid" && (
                        <DropdownMenuItem onClick={() => handleRefund(payment)}>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          <span>Issue Refund</span>
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="space-y-2">
          {filteredPayments.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <p className="text-muted-foreground">No jobs found matching your filters.</p>
              {searchQuery && (
                <Button 
                  variant="link" 
                  onClick={() => setSearchQuery('')}
                  className="mt-2"
                >
                  Clear search
                </Button>
              )}
            </div>
          ) : (
            filteredPayments.map(payment => (
              <MobilePaymentCard key={payment.id} payment={payment} />
            ))
          )}
        </div>
      )}

      {/* Payment Details Dialog (Desktop) */}
      <Dialog open={showPaymentDetails} onOpenChange={setShowPaymentDetails}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Job Details</DialogTitle>
            <DialogDescription>
              View detailed information about this job.
            </DialogDescription>
          </DialogHeader>
          {selectedPayment && (
            <div className="py-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Job ID</h3>
                  <p className="font-medium">{selectedPayment.id}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                  <p>{getStatusBadge(selectedPayment.status)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Customer</h3>
                  <p className="font-medium">{selectedPayment.customerName}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Provider</h3>
                  <p className="font-medium">{selectedPayment.providerName}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Amount</h3>
                  <p className="font-medium">${selectedPayment.amount.toFixed(2)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
                  <p className="font-medium">{new Date(selectedPayment.date).toLocaleDateString()}</p>
                </div>
              </div>

              {/* Additional job details would be displayed here */}
              <div className="pt-4 border-t">
                <h3 className="text-sm font-medium mb-2">Payment Method</h3>
                <div className="flex items-center gap-2">
                  <div className="p-1 rounded bg-gray-100">
                    <img src="https://placehold.co/40x25?text=Card" alt="Card" className="h-6" />
                  </div>
                  <span>•••• {selectedPayment.cardLast4 || "4242"}</span>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="flex gap-2 sm:gap-0">
            {selectedPayment && selectedPayment.status === "Paid" && (
              <Button 
                variant="outline"
                className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                onClick={() => handleRefund(selectedPayment)}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Issue Refund
              </Button>
            )}
            <Button onClick={() => setShowPaymentDetails(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Mobile Payment Detail View */}
      {isMobile && selectedPayment && (
        <div className={`fixed inset-0 bg-background z-50 ${showMobileDetails ? 'block' : 'hidden'}`}>
          <MobilePaymentDetailCard
            payment={selectedPayment}
            onClose={() => setShowMobileDetails(false)}
            onRefund={handleRefund}
          />
        </div>
      )}
    </div>
  );
};

// Make sure it's also exported as default
export default JobsManagement;
