
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { MessageSquare, Search } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";

interface Message {
  id: number;
  sender: string;
  recipient: string;
  content: string;
  timestamp: string;
}

interface ProjectMessagesProps {
  messages: Message[];
}

export const ProjectMessages = ({ messages }: ProjectMessagesProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const providersPerPage = 5;

  // Group messages by provider
  const messagesByProvider = messages.reduce((acc, message) => {
    const provider = message.sender === "John Doe" ? message.recipient : message.sender;
    if (!acc[provider]) {
      acc[provider] = [];
    }
    acc[provider].push(message);
    return acc;
  }, {} as Record<string, Message[]>);

  // Filter providers by search term
  const filteredProviders = Object.keys(messagesByProvider).filter(provider =>
    provider.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate pagination
  const indexOfLastProvider = currentPage * providersPerPage;
  const indexOfFirstProvider = indexOfLastProvider - providersPerPage;
  const currentProviders = filteredProviders.slice(indexOfFirstProvider, indexOfLastProvider);
  const totalPages = Math.ceil(filteredProviders.length / providersPerPage);

  // Generate page numbers for pagination
  const pageNumbers = [];
  for (let i = 1; i <= totalPages; i++) {
    pageNumbers.push(i);
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex items-center gap-2 mb-4">
          <MessageSquare className="w-5 h-5" />
          <h3 className="font-semibold">Messages</h3>
          {messages.length > 0 && (
            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
              {messages.length} total
            </span>
          )}
          <span className="text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full ml-2">
            {Object.keys(messagesByProvider).length} providers
          </span>
        </div>

        {filteredProviders.length > 0 ? (
          <>
            <div className="mb-4 relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search providers..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1); // Reset to first page on search
                }}
                className="pl-8"
              />
            </div>

            <Tabs defaultValue={currentProviders[0]} className="w-full">
              <div className="border rounded-md p-1 mb-4 bg-gray-50">
                <ScrollArea className="h-12">
                  <TabsList className="bg-transparent justify-start w-max">
                    {currentProviders.map((provider) => (
                      <TabsTrigger key={provider} value={provider} className="data-[state=active]:bg-white">
                        {provider}
                        <span className="ml-1.5 text-xs bg-gray-100 px-1.5 py-0.5 rounded-full">
                          {messagesByProvider[provider].length}
                        </span>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </ScrollArea>
              </div>

              {currentProviders.map((provider) => (
                <TabsContent key={provider} value={provider}>
                  <ScrollArea className="h-[400px] pr-4">
                    <div className="space-y-4">
                      {messagesByProvider[provider].map((message) => (
                        <div
                          key={message.id}
                          className={`flex ${
                            message.sender === "John Doe"
                              ? "justify-end"
                              : "justify-start"
                          }`}
                        >
                          <div
                            className={`max-w-[70%] p-3 rounded-lg ${
                              message.sender === "John Doe"
                                ? "bg-blue-50 border-blue-100"
                                : "bg-gray-50 border-gray-100"
                            } border`}
                          >
                            <p className="text-sm font-medium mb-1">{message.sender}</p>
                            <p className="text-sm">{message.content}</p>
                            <p className="text-xs text-gray-500 mt-1">
                              {new Date(message.timestamp).toLocaleString()}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>
              ))}
            </Tabs>

            {totalPages > 1 && (
              <div className="mt-4 flex justify-center">
                <Pagination
                  totalItems={filteredProviders.length}
                  itemsPerPage={providersPerPage}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              </div>
            )}
          </>
        ) : (
          <p className="text-gray-500 text-sm">No messages yet</p>
        )}
      </CardContent>
    </Card>
  );
};
