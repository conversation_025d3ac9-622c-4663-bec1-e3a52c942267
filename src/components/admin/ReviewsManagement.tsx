
import { useState } from "react";
import { 
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow 
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Search, MoreVertical, Eye, Trash2, Filter, X, Star, ChevronRight } from "lucide-react";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

export const ReviewsManagement = () => {
  const { toast } = useToast();
  const { isMobile } = useUIHelpers();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedReview, setSelectedReview] = useState<any | null>(null);
  const [showReviewDetails, setShowReviewDetails] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const [ratingFilter, setRatingFilter] = useState<string>("all");
  const [showMobileDetails, setShowMobileDetails] = useState(false);

  // Mock data - in a real app, this would come from an API
  const reviews = [
    {
      id: "REV-1001",
      customerName: "Sarah Johnson",
      providerName: "Mike's Home Improvement",
      rating: 5,
      reviewText: "Excellent service! Mike was professional, punctual, and did an amazing job renovating our kitchen. Would highly recommend.",
      date: "2025-04-10"
    },
    {
      id: "REV-1002",
      customerName: "David Wilson",
      providerName: "Electro Solutions",
      rating: 4,
      reviewText: "Good work overall. They fixed our electrical issues quickly, though there was a slight delay in arrival.",
      date: "2025-04-12"
    },
    {
      id: "REV-1003",
      customerName: "Emily Davis",
      providerName: "Green Thumb Landscaping",
      rating: 5,
      reviewText: "Transformed our backyard into an absolute paradise! The Green Thumb team was creative and attentive to our needs.",
      date: "2025-04-08"
    },
    {
      id: "REV-1004",
      customerName: "James Miller",
      providerName: "Clean Pro Services",
      rating: 2,
      reviewText: "Disappointed with the cleaning service. Several areas were missed, and they didn't use the eco-friendly products as requested.",
      date: "2025-04-15"
    },
    {
      id: "REV-1005",
      customerName: "Lisa Parker",
      providerName: "Electro Solutions",
      rating: 3,
      reviewText: "Average service. Fixed the problem but left a mess behind.",
      date: "2025-04-16"
    }
  ];

  // Filter reviews based on search query and rating filter
  const filteredReviews = reviews.filter(review => {
    const matchesSearch = 
      review.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      review.providerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      review.reviewText.toLowerCase().includes(searchQuery.toLowerCase()) ||
      review.id.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesRating = ratingFilter === "all" || review.rating === parseInt(ratingFilter);
    
    return matchesSearch && matchesRating;
  });

  const handleViewDetails = (review: any) => {
    setSelectedReview(review);
    if (isMobile) {
      setShowMobileDetails(true);
    } else {
      setShowReviewDetails(true);
    }
  };

  const handleDeleteClick = (review: any) => {
    setSelectedReview(review);
    setShowDeleteConfirmation(true);
  };

  const handleDeleteReview = () => {
    // In a real app, this would be an API call
    toast({
      title: "Review deleted",
      description: `Review ${selectedReview?.id} has been deleted successfully.`,
    });
    setShowDeleteConfirmation(false);
    setShowMobileDetails(false);
  };

  // Helper function to render stars
  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, index) => (
      <Star 
        key={index}
        className={cn(
          "h-4 w-4", 
          index < rating 
            ? "text-yellow-400 fill-yellow-400" 
            : "text-gray-300"
        )} 
      />
    ));
  };

  // Get color based on rating
  const getRatingColor = (rating: number) => {
    if (rating >= 5) return "from-green-400 to-emerald-500";
    if (rating >= 4) return "from-teal-400 to-cyan-500";
    if (rating >= 3) return "from-blue-400 to-indigo-500";
    if (rating >= 2) return "from-amber-400 to-orange-500";
    return "from-red-400 to-rose-500";
  };

  // Enhanced Mobile review card component with better UI/UX and colors
  const MobileReviewCard = ({ review }: { review: any }) => (
    <div 
      className="rounded-xl overflow-hidden shadow-md mb-4 animate-fade-in"
      onClick={() => handleViewDetails(review)}
    >
      <div className={`bg-gradient-to-r ${getRatingColor(review.rating)} p-3 flex justify-between items-center`}>
        <div className="text-white font-semibold">
          {review.id}
        </div>
        <div className="flex bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full">
          {renderStars(review.rating)}
        </div>
      </div>
      
      <div className="p-4 bg-white dark:bg-gray-800 border-x border-b border-gray-100 dark:border-gray-700 rounded-b-xl">
        <div className="flex justify-between items-start mb-3">
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100">{review.customerName}</h3>
            <p className="text-sm text-blue-600 dark:text-blue-400">{review.providerName}</p>
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
            {new Date(review.date).toLocaleDateString()}
          </div>
        </div>
        
        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-3">{review.reviewText}</p>
        
        <div className="flex justify-end">
          <div className="flex items-center text-blue-600 dark:text-blue-400 font-medium text-sm">
            <span className="mr-1">View Details</span>
            <ChevronRight className="h-4 w-4" />
          </div>
        </div>
      </div>
    </div>
  );

  // Enhanced Mobile review detail component
  const MobileReviewDetailCard = ({ review, onClose }: { review: any, onClose: () => void }) => (
    <div className="flex flex-col h-full bg-background">
      <div className={`sticky top-0 z-10 bg-gradient-to-r ${getRatingColor(review.rating)} p-4 flex items-center justify-between text-white`}>
        <h2 className="font-semibold text-lg">{review.id}</h2>
        <Button variant="ghost" size="icon" onClick={onClose} className="text-white hover:bg-white/20">
          <X className="h-5 w-5" />
        </Button>
      </div>
      
      <div className="flex-1 overflow-auto p-5">
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div className="space-y-1">
              <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
              <p className="font-medium">{new Date(review.date).toLocaleDateString()}</p>
            </div>
            
            <div className="bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 p-3 rounded-lg">
              <h3 className="text-sm font-medium text-center mb-1">Rating</h3>
              <div className="flex text-yellow-400">
                {renderStars(review.rating)}
              </div>
            </div>
          </div>
          
          <div className="space-y-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/10 dark:to-indigo-900/10 p-4 rounded-lg border-l-4 border-blue-500">
            <h3 className="text-sm font-medium text-blue-700 dark:text-blue-400">Customer</h3>
            <p className="font-medium">{review.customerName}</p>
          </div>
          
          <div className="space-y-2 bg-gradient-to-r from-teal-50 to-green-50 dark:from-teal-900/10 dark:to-green-900/10 p-4 rounded-lg border-l-4 border-teal-500">
            <h3 className="text-sm font-medium text-teal-700 dark:text-teal-400">Provider</h3>
            <p className="font-medium">{review.providerName}</p>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">Review</h3>
            <p className="mt-1 bg-gray-50 dark:bg-gray-800/60 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-inner">{review.reviewText}</p>
          </div>
        </div>
      </div>
      
      <div className="sticky bottom-0 z-10 bg-background border-t p-4 flex gap-3">
        <Button 
          variant="outline" 
          className="flex-1" 
          onClick={onClose}
        >
          Close
        </Button>
        <Button 
          variant="destructive" 
          className="flex-1"
          onClick={() => handleDeleteClick(review)}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Delete Review
        </Button>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Reviews</h1>
          <p className="text-muted-foreground">
            Manage and respond to your customer reviews
          </p>
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          {isMobile ? (
            <>
              <Sheet open={filterOpen} onOpenChange={setFilterOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon" className="relative bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                    <Filter className="h-4 w-4" />
                    {ratingFilter !== "all" && (
                      <span className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full" />
                    )}
                  </Button>
                </SheetTrigger>
                <SheetContent side="bottom" className="h-[50vh] rounded-t-xl">
                  <SheetHeader className="mb-4">
                    <SheetTitle>Filter Reviews</SheetTitle>
                  </SheetHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-1 block">Search</label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search reviews..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-9 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                        />
                        {searchQuery && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                            onClick={() => setSearchQuery("")}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium mb-1 block">Rating</label>
                      <Select 
                        value={ratingFilter} 
                        onValueChange={setRatingFilter}
                      >
                        <SelectTrigger className="bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                          <SelectValue placeholder="Filter by rating" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Ratings</SelectItem>
                          <SelectItem value="5">5 Stars</SelectItem>
                          <SelectItem value="4">4 Stars</SelectItem>
                          <SelectItem value="3">3 Stars</SelectItem>
                          <SelectItem value="2">2 Stars</SelectItem>
                          <SelectItem value="1">1 Star</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="pt-4">
                      <Button 
                        className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700" 
                        onClick={() => setFilterOpen(false)}
                      >
                        Apply Filters
                      </Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
              
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search reviews..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9 w-full bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus-visible:ring-blue-500"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                    onClick={() => setSearchQuery("")}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </>
          ) : (
            <>
              <Select 
                value={ratingFilter} 
                onValueChange={setRatingFilter}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="All Ratings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Ratings</SelectItem>
                  <SelectItem value="5">5 Stars</SelectItem>
                  <SelectItem value="4">4 Stars</SelectItem>
                  <SelectItem value="3">3 Stars</SelectItem>
                  <SelectItem value="2">2 Stars</SelectItem>
                  <SelectItem value="1">1 Star</SelectItem>
                </SelectContent>
              </Select>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search reviews..."
                  className="pl-8 w-[250px] md:w-[300px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </>
          )}
        </div>
      </div>

      {!isMobile ? (
        <Table>
          <TableCaption>A list of all customer reviews on the platform.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Review ID</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Provider</TableHead>
              <TableHead>Rating</TableHead>
              <TableHead>Review Preview</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredReviews.map((review) => (
              <TableRow key={review.id}>
                <TableCell className="font-medium">{review.id}</TableCell>
                <TableCell>{review.customerName}</TableCell>
                <TableCell>{review.providerName}</TableCell>
                <TableCell>
                  <span className="text-yellow-500">{renderStars(review.rating)}</span>
                </TableCell>
                <TableCell className="max-w-[200px] truncate">{review.reviewText}</TableCell>
                <TableCell>{new Date(review.date).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewDetails(review)}>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>View Full Review</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDeleteClick(review)} className="text-red-500">
                        <Trash2 className="mr-2 h-4 w-4" />
                        <span>Delete Review</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="space-y-2">
          {ratingFilter !== "all" && (
            <div className="bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 px-4 py-3 rounded-lg mb-3 flex items-center justify-between shadow-sm">
              <div className="flex items-center">
                <Filter className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                <span className="text-blue-800 dark:text-blue-300">Showing {ratingFilter}-star reviews</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setRatingFilter("all")}
                className="h-7 text-blue-600 dark:text-blue-400 hover:text-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30"
              >
                Clear
              </Button>
            </div>
          )}
          
          {filteredReviews.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-100 dark:border-gray-700 shadow-inner">
              <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 mb-3">
                <Star className="h-6 w-6 text-gray-400 dark:text-gray-500" />
              </div>
              <p className="text-lg font-medium mb-1 text-gray-900 dark:text-gray-100">No reviews found</p>
              <p className="text-gray-500 dark:text-gray-400 max-w-xs mx-auto">
                No reviews match your current filters. Try adjusting your search criteria.
              </p>
            </div>
          ) : (
            <div className="px-1 py-2">
              {filteredReviews.map(review => (
                <MobileReviewCard key={review.id} review={review} />
              ))}
            </div>
          )}
        </div>
      )}

      {/* Review Details Dialog (Desktop) */}
      <Dialog open={showReviewDetails} onOpenChange={setShowReviewDetails}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Review Details</DialogTitle>
            <DialogDescription>
              View the full review content.
            </DialogDescription>
          </DialogHeader>
          {selectedReview && (
            <div className="py-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Review ID</h3>
                  <p className="font-medium">{selectedReview.id}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
                  <p className="font-medium">{new Date(selectedReview.date).toLocaleDateString()}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Customer</h3>
                  <p className="font-medium">{selectedReview.customerName}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Provider</h3>
                  <p className="font-medium">{selectedReview.providerName}</p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Rating</h3>
                <p className="text-yellow-500 text-xl">{renderStars(selectedReview.rating)}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Review</h3>
                <p className="mt-1">{selectedReview.reviewText}</p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReviewDetails(false)}>Close</Button>
            <Button 
              variant="destructive" 
              onClick={() => {
                setShowReviewDetails(false);
                handleDeleteClick(selectedReview);
              }}
            >
              Delete Review
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Mobile Review Detail View */}
      {isMobile && selectedReview && (
        <div className={`fixed inset-0 bg-background z-50 ${showMobileDetails ? 'block animate-slide-in' : 'hidden'}`}>
          <MobileReviewDetailCard
            review={selectedReview}
            onClose={() => setShowMobileDetails(false)}
          />
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Delete Review</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this review? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert variant="destructive">
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                Deleting this review will permanently remove it from the platform.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteConfirmation(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteReview}>Delete Review</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
