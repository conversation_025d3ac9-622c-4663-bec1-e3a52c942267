import { useState, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AdminMessageComposer } from "../shared/AdminMessageComposer";
import { AdminMessageThread } from "../shared/AdminMessageThread";
import {
  Mail,
  Phone,
  MapPin,
  Star,
  Calendar,
  Briefcase,
  MessageSquare,
  UserX,
  UserCheck,
  CreditCard,
} from "lucide-react";
import { JobBookingsList } from "../job-bookings/JobBookingsList";
import { JobBooking } from "@/services/jobBookingService";
import { apiService } from "@/services/api";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { useToast } from "@/components/ui/use-toast";

interface Provider {
  id: string | number;
  avatar: string;
  name: string;
  status: "Active" | "Suspended" | "Pending" | string; // string as a fallback for other statuses
  plan: "Elite" | "Pro" | "Starter" | string; // string as a fallback for other plans
  email: string;
  specialty: string;
  location: string;
  rating: number | string;
}

interface MobileProviderDetailCardProps {
  provider: Provider;
  onClose: () => void;
  onSuspend: (provider: Provider) => void;
  onOpenMessageComposer?: (provider: Provider) => void;
}

export const MobileProviderDetailCard = ({
  provider,
  onClose,
  onSuspend,
  onOpenMessageComposer,
}: MobileProviderDetailCardProps) => {
  const [activeTab, setActiveTab] = useState("info");
  const [isMessageComposerOpen, setIsMessageComposerOpen] = useState(false);
  const { token } = useAuth();
  const { toast } = useToast();
  const [bookings, setBookings] = useState<JobBooking[]>([]);
  const [isLoadingBookings, setIsLoadingBookings] = useState(false);

  // Fetch job bookings when the tab is selected
  useEffect(() => {
    if (activeTab === "bookings" && provider?.id) {
      fetchProviderBookings();
    }
  }, [activeTab, provider?.id]);

  const fetchProviderBookings = async () => {
    if (!token) {
      toast({
        title: "Authentication Error",
        description: "Not authenticated. Please log in.",
        variant: "destructive",
      });
      setIsLoadingBookings(false);
      return;
    }
    setIsLoadingBookings(true);
    try {
      // First, get all job bookings associated with this provider
      // Assuming 'token' is confirmed string by an earlier 'if (!token) return;'
      const apiHeaders: Record<string, string> = {
        'Authorization': token, 
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };
      const endpoint = `/api/job-bookings?user_id=${provider.id}&page=1&per_page=10`;
      const response = await apiService<{ data: JobBooking[] }>(endpoint, {
        method: 'GET',
        headers: apiHeaders, // Use the new headers object
        requiresAuth: true,
        includeCredentials: true
      });

      if (response.isSuccess && response.data) {
        const successData = response.data; // Assign to intermediate variable
        setBookings(successData.data);
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to fetch job bookings",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching job bookings:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoadingBookings(false);
    }
  };

  // Simulated data (would come from API in a real app)
  const joinDate = "2024-12-15";
  const completedJobs = 12;
  const responseRate = "95%";
  const responseTime = "2.5 hours";

  const getStatusBadge = (status: string) => {
    switch(status) {
      case "Active":
        return <Badge variant="success">{status}</Badge>;
      case "Suspended":
        return <Badge variant="destructive">{status}</Badge>;
      case "Pending":
        return <Badge variant="warning">{status}</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPlanBadge = (plan: string) => {
    switch(plan) {
      case "Elite":
        return <Badge className="bg-purple-500">{plan}</Badge>;
      case "Pro":
        return <Badge variant="business">{plan}</Badge>;
      case "Starter":
        return <Badge variant="secondary">{plan}</Badge>;
      default:
        return <Badge variant="outline">{plan}</Badge>;
    }
  };

  // Mock admin messages
  const mockAdminMessages = [
    {
      id: "msg-admin-p1",
      adminName: "Admin Team",
      adminAvatar: "",
      text: "Hello, we'd like to verify your business license and insurance documents. Can you please upload them in your profile?",
      timestamp: "2025-03-10T09:30:00Z",
      isFromAdmin: true,
    },
    {
      id: "msg-provider-p1",
      adminName: "",
      adminAvatar: "",
      text: "I've just uploaded my business license and insurance documents. Let me know if you need anything else!",
      timestamp: "2025-03-10T11:20:00Z",
      isFromAdmin: false,
    },
    {
      id: "msg-admin-p2",
      adminName: "Verification Team",
      adminAvatar: "",
      text: "Thank you! We've reviewed your documents and everything looks good. Your account has been verified.",
      timestamp: "2025-03-11T14:15:00Z",
      isFromAdmin: true,
    }
  ];

  const handleOpenMessageComposer = () => {
    setIsMessageComposerOpen(true);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Provider Header */}
      <div className="mb-4 p-4 bg-card rounded-lg border shadow-sm">
        <div className="flex items-center gap-3">
          <Avatar className="h-16 w-16">
            <AvatarImage src={provider.avatar} alt={provider.name} />
            <AvatarFallback className="text-xl">{provider.name.charAt(0)}{provider.name.split(' ')[1]?.charAt(0)}</AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <h2 className="text-xl font-bold truncate">{provider.name}</h2>
            <div className="flex items-center gap-1 mt-1">
              {getStatusBadge(provider.status)}
              <span className="mx-1">•</span>
              {getPlanBadge(provider.plan)}
            </div>
            <div className="flex items-center text-sm text-muted-foreground mt-1">
              <Mail className="h-3.5 w-3.5 mr-1" />
              <span className="truncate">{provider.email}</span>
            </div>
          </div>
        </div>

        {/* Quick actions */}
        <div className="grid grid-cols-2 gap-2 mt-4">
          {provider.status === "Suspended" ? (
            <Button
              variant="success"
              size="sm"
              onClick={() => onSuspend(provider)}
              className="w-full"
            >
              <UserCheck className="mr-2 h-4 w-4" /> Reactivate
            </Button>
          ) : (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => onSuspend(provider)}
              className="w-full"
            >
              <UserX className="mr-2 h-4 w-4" /> Suspend
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenMessageComposer}
            className="w-full"
          >
            <MessageSquare className="mr-2 h-4 w-4" /> Message
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
        <TabsList className="grid w-full grid-cols-5 mb-4">
          <TabsTrigger value="info">Info</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="performance">Stats</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          <TabsTrigger value="messages">Messages</TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="space-y-4">
          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-3">Business Details</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <Briefcase className="h-4 w-4 mr-3 text-muted-foreground" />
                  <div>
                    <div className="text-muted-foreground">Specialty</div>
                    <div>{provider.specialty}</div>
                  </div>
                </div>

                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-3 text-muted-foreground" />
                  <div>
                    <div className="text-muted-foreground">Location</div>
                    <div>{provider.location}</div>
                  </div>
                </div>

                <div className="flex items-center">
                  <Star className="h-4 w-4 mr-3 text-muted-foreground" />
                  <div>
                    <div className="text-muted-foreground">Rating</div>
                    <div className="flex items-center">
                      {provider.rating}
                      <Star className="h-3 w-3 fill-amber-500 text-amber-500 ml-1" />
                    </div>
                  </div>
                </div>

                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-3 text-muted-foreground" />
                  <div>
                    <div className="text-muted-foreground">Member Since</div>
                    <div>{joinDate}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-3">Subscription</h3>
              <div className="flex justify-between items-center mb-3">
                <div>
                  <div className="text-sm text-muted-foreground">Current Plan</div>
                  <div className="flex items-center mt-1">
                    {getPlanBadge(provider.plan)}
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <CreditCard className="h-4 w-4 mr-2" /> Update
                </Button>
              </div>
              <Separator className="my-3" />
              <div className="text-sm">
                <div className="text-muted-foreground mb-1">Next Billing Date</div>
                <div>May 15, 2025</div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bookings" className="space-y-4">
          <JobBookingsList bookings={bookings} isLoading={isLoadingBookings} />
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-4">Key Metrics</h3>
              <div className="grid grid-cols-2 gap-y-4">
                <div className="text-center">
                  <div className="text-xs text-muted-foreground">Jobs Completed</div>
                  <div className="text-xl font-semibold mt-1">{completedJobs}</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-muted-foreground">Response Rate</div>
                  <div className="text-xl font-semibold mt-1">{responseRate}</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-muted-foreground">Avg Response Time</div>
                  <div className="text-xl font-semibold mt-1">{responseTime}</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-muted-foreground">Customer Satisfaction</div>
                  <div className="text-xl font-semibold mt-1">92%</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-medium">Recent Activity</h3>
              </div>

              <div className="space-y-3 text-sm">
                <div className="pb-2 border-b">
                  <div className="flex justify-between">
                    <div className="font-medium">Completed Job #1082</div>
                    <div className="text-xs text-muted-foreground">2d ago</div>
                  </div>
                  <div className="text-muted-foreground mt-1">Plumbing repair - $120</div>
                </div>

                <div className="pb-2 border-b">
                  <div className="flex justify-between">
                    <div className="font-medium">Accepted New Job #1095</div>
                    <div className="text-xs text-muted-foreground">1w ago</div>
                  </div>
                  <div className="text-muted-foreground mt-1">Water heater installation</div>
                </div>

                <div>
                  <div className="flex justify-between">
                    <div className="font-medium">Subscription Renewed</div>
                    <div className="text-xs text-muted-foreground">1m ago</div>
                  </div>
                  <div className="text-muted-foreground mt-1">Pro Plan - $49.99/month</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reviews" className="space-y-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Customer Reviews</h3>
                <div className="flex items-center">
                  <Star className="h-4 w-4 fill-amber-500 text-amber-500" />
                  <span className="ml-1 font-semibold">{provider.rating}</span>
                </div>
              </div>

              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="pb-3 border-b last:border-0 last:pb-0">
                    <div className="flex justify-between">
                      <div className="font-medium">Customer {i}</div>
                      <div className="flex">
                        {Array.from({ length: 5 }).map((_, idx) => (
                          <Star
                            key={idx}
                            className={`h-3.5 w-3.5 ${idx < 5 - (i % 2) ? "text-amber-500 fill-amber-500" : "text-gray-300"}`}
                          />
                        ))}
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">April {10 + i}, 2025</div>
                    <div className="mt-2 text-sm">
                      Great service! {i === 1 ? "Very professional and completed the job quickly." : i === 2 ? "Showed up on time and fixed our issue at a fair price." : "Would definitely recommend to others."}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="messages" className="space-y-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Admin Messages</h3>
                <Button variant="outline" size="sm" onClick={handleOpenMessageComposer}>
                  <MessageSquare className="h-4 w-4 mr-1.5" /> New
                </Button>
              </div>

              <div className="rounded-lg border overflow-hidden">
                <AdminMessageThread
                  messages={mockAdminMessages}
                  entityName={provider.name}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Bottom action */}
      <div className="mt-4">
        <Button variant="outline" className="w-full" onClick={onClose}>
          Close
        </Button>
      </div>

      {/* Message Composer */}
      <AdminMessageComposer
        open={isMessageComposerOpen}
        onClose={() => setIsMessageComposerOpen(false)}
        recipientType="provider"
        recipient={{
          id: String(provider.id),
          name: provider.name,
          email: provider.email
        }}
      />
    </div>
  );
};
