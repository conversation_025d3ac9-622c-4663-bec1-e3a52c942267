import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DrawerClose } from "@/components/ui/drawer";
import { X, MessageSquare } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

type Message = {
  id: string;
  sender: "customer" | "provider";
  text: string;
  timestamp: string;
};

type Provider = {
  id: string;
  name: string;
  avatar?: string;
};

type Customer = {
  id: string;
  name: string;
  avatar?: string;
};

type Job = {
  id?: string;
  title?: string;
};

interface DrawerProps {
  open: boolean;
  onClose: () => void;
  customer: Customer;
  provider: Provider;
  job?: Job;
  messages: Message[];
}

export const CustomerProviderConversationDrawer: React.FC<DrawerProps> = ({
  open,
  onClose,
  customer,
  provider,
  job,
  messages,
}) => {
  return (
    <Drawer open={open} onOpenChange={(open) => !open && onClose()}>
      <DrawerContent className="fixed right-0 top-0 bottom-0 max-w-full w-full sm:w-[480px] bg-white dark:bg-gray-950 z-[200] shadow-2xl outline-none flex flex-col">
        <DrawerHeader className="flex flex-row items-center justify-between px-6 py-4 border-b bg-white dark:bg-gray-950">
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-3">
              <Avatar className="h-9 w-9">
                <AvatarImage src={provider.avatar} alt={provider.name} />
                <AvatarFallback>
                  {provider.name
                    .split(" ")
                    .slice(0, 2)
                    .map((word) => word.charAt(0))
                    .join("")
                  }
                </AvatarFallback>
              </Avatar>
              <span className="font-semibold">{provider.name} <span className="text-xs text-gray-500">— {provider.id}</span></span>
            </div>
            <div className="flex items-center gap-3 mt-1">
              <Avatar className="h-8 w-8">
                <AvatarImage src={customer.avatar} alt={customer.name} />
                <AvatarFallback>
                  {customer.name
                    .split(" ")
                    .slice(0, 2)
                    .map((word) => word.charAt(0))
                    .join("")
                  }
                </AvatarFallback>
              </Avatar>
              <span className="text-sm">{customer.name} <span className="text-xs text-gray-500">— {customer.id}</span></span>
            </div>
            {job?.title && (
              <div className="mt-2">
                <span className="text-xs text-gray-500 font-medium">
                  {job.title}
                </span>
                {job.id && (
                  <Badge className="ml-2 bg-gray-200 text-gray-700 font-normal">{job.id}</Badge>
                )}
              </div>
            )}
          </div>
          <DrawerClose asChild>
            <button aria-label="Close" className="p-2 text-gray-600 hover:bg-gray-100 rounded-full">
              <X className="h-6 w-6" />
            </button>
          </DrawerClose>
        </DrawerHeader>
        <div className="flex-1 overflow-y-auto px-6 py-6">
          <ScrollArea className="h-full">
            <div className="flex flex-col gap-4">
              {messages.length === 0 ? (
                <div className="text-center text-gray-400">No conversation history yet.</div>
              ) : (
                messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={`flex ${msg.sender === "provider" ? "justify-end" : "justify-start"}`}
                  >
                    <div className={`rounded-lg px-4 py-3 max-w-xs text-sm break-words
                      ${msg.sender === "provider"
                        ? "bg-blue-50 text-blue-900 border border-blue-200"
                        : "bg-gray-50 text-gray-900 border border-gray-200"}
                    `}>
                      <div className="font-semibold mb-1">
                        {msg.sender === "provider"
                          ? provider.name
                          : customer.name}
                      </div>
                      <div>{msg.text}</div>
                      <div className="text-xs mt-1 text-gray-500 text-right">
                        {new Date(msg.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </div>
      </DrawerContent>
    </Drawer>
  );
};
