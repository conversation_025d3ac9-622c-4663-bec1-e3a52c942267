
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Upload, Mail, Search, Check, Edit, Filter, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { CardIcon } from "@/components/ui/card-icon";
import { EditInvitationForm } from "@/components/admin/provider-invitations/EditInvitationForm";
import { useUIHelpers } from "@/hooks/use-ui-helpers";

// Types for provider invitation data
interface ProviderInvitation {
  id: string;
  businessName: string;
  email: string;
  phone?: string;
  serviceType: string;
  location?: string;
  invitationCode: string;
  status: "draft" | "sent" | "opened" | "signed_up" | "logged_in";
  sentDate?: string;
  openedDate?: string;
  signupDate?: string;
  loginDate?: string;
}

// Mock data for demo purposes
const mockInvitations: ProviderInvitation[] = [
  {
    id: "inv-001",
    businessName: "Mike's Plumbing",
    email: "<EMAIL>",
    phone: "************",
    serviceType: "Plumbing",
    location: "Austin, TX",
    invitationCode: "JOIN1234",
    status: "logged_in",
    sentDate: "2025-04-21T10:30:00Z",
    openedDate: "2025-04-21T14:45:00Z",
    signupDate: "2025-04-22T09:15:00Z",
    loginDate: "2025-04-22T09:20:00Z"
  },
  {
    id: "inv-002",
    businessName: "Bright Electric Co",
    email: "<EMAIL>",
    phone: "************",
    serviceType: "Electrical",
    location: "Denver, CO",
    invitationCode: "JOIN5678",
    status: "signed_up",
    sentDate: "2025-04-21T10:30:00Z",
    openedDate: "2025-04-21T16:20:00Z",
    signupDate: "2025-04-23T11:05:00Z"
  },
  {
    id: "inv-003",
    businessName: "Cool Air HVAC",
    email: "<EMAIL>",
    phone: "************",
    serviceType: "HVAC",
    location: "Phoenix, AZ",
    invitationCode: "JOIN9012",
    status: "opened",
    sentDate: "2025-04-21T10:30:00Z",
    openedDate: "2025-04-22T08:15:00Z"
  },
  {
    id: "inv-004",
    businessName: "Elite Cleaning Services",
    email: "<EMAIL>",
    phone: "************",
    serviceType: "Cleaning",
    location: "Seattle, WA",
    invitationCode: "JOIN3456",
    status: "sent",
    sentDate: "2025-04-21T10:30:00Z"
  },
  {
    id: "inv-005",
    businessName: "GreenScape Landscaping",
    email: "<EMAIL>",
    serviceType: "Landscaping",
    invitationCode: "JOIN7890",
    status: "draft"
  }
];

export const ProviderInvitations = () => {
  const { toast } = useToast();
  const { isMobile } = useUIHelpers();
  const [activeTab, setActiveTab] = useState("upload");
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [invitations, setInvitations] = useState<ProviderInvitation[]>(mockInvitations);
  const [selectedProviders, setSelectedProviders] = useState<Set<string>>(new Set());
  const [selectedAll, setSelectedAll] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedInvitation, setSelectedInvitation] = useState<ProviderInvitation | null>(null);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // Filter invitations based on search and status filter
  const filteredInvitations = invitations.filter(invitation => {
    const matchesSearch = 
      invitation.businessName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invitation.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invitation.serviceType.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || invitation.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Handle CSV file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setCsvFile(e.target.files[0]);
    }
  };

  const handleUploadCSV = () => {
    setIsLoading(true);
    
    // In a real implementation, we would process the CSV file here
    // For now, we'll simulate a successful upload
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "CSV Successfully Uploaded",
        description: `5 provider records imported. Ready to send invitations.`,
      });
      setActiveTab("manage");
    }, 1500);
  };

  // Handle sending invitations
  const handleSendInvitations = () => {
    setIsLoading(true);
    
    // In a real implementation, we would send emails to the selected providers
    // For now, we'll simulate sending invitations
    setTimeout(() => {
      setIsLoading(false);
      
      // Update statuses for selected providers
      const updatedInvitations = invitations.map(invitation => {
        if (selectedProviders.has(invitation.id) && invitation.status === "draft") {
          return {
            ...invitation,
            status: "sent" as "draft" | "sent" | "opened" | "signed_up" | "logged_in",
            sentDate: new Date().toISOString()
          };
        }
        return invitation;
      });
      
      setInvitations(updatedInvitations);
      setSelectedProviders(new Set());
      setSelectedAll(false);
      
      toast({
        title: "Invitations Sent",
        description: `${selectedProviders.size} invitations have been sent successfully.`,
      });
    }, 1500);
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "—";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectedAll) {
      setSelectedProviders(new Set());
    } else {
      const newSelected = new Set<string>();
      filteredInvitations.forEach(invitation => {
        if (invitation.status === "draft") {
          newSelected.add(invitation.id);
        }
      });
      setSelectedProviders(newSelected);
    }
    setSelectedAll(!selectedAll);
  };

  // Handle individual checkbox selection
  const handleSelect = (id: string) => {
    const newSelected = new Set(selectedProviders);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedProviders(newSelected);
  };

  // Get stats for summary cards
  const getInvitationStats = () => {
    const total = invitations.length;
    const sent = invitations.filter(i => i.status === "sent").length;
    const opened = invitations.filter(i => i.status === "opened").length;
    const signedUp = invitations.filter(i => ["signed_up", "logged_in"].includes(i.status)).length;
    const loggedIn = invitations.filter(i => i.status === "logged_in").length;
    
    return { total, sent, opened, signedUp, loggedIn };
  };

  const stats = getInvitationStats();

  // Render status badge based on invitation status
  const renderStatusBadge = (status: string) => {
    switch(status) {
      case "draft":
        return <Badge variant="outline" className="bg-gray-50 text-gray-700">Draft</Badge>;
      case "sent":
        return <Badge variant="secondary" className="bg-purple-100 text-purple-700">Sent</Badge>;
      case "opened":
        return <Badge variant="warning" className="bg-amber-100 text-amber-700">Opened</Badge>;
      case "signed_up":
        return <Badge variant="business" className="bg-blue-100 text-blue-700">Signed Up</Badge>;
      case "logged_in":
        return <Badge variant="success" className="bg-green-100 text-green-700">Logged In</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Check if provider has given status
  const hasStatus = (invitation: ProviderInvitation, status: string) => {
    const statusMap: Record<string, boolean> = {
      "sent": ["sent", "opened", "signed_up", "logged_in"].includes(invitation.status),
      "opened": ["opened", "signed_up", "logged_in"].includes(invitation.status),
      "signed_up": ["signed_up", "logged_in"].includes(invitation.status),
      "logged_in": invitation.status === "logged_in"
    };
    
    return statusMap[status] || false;
  };

  const handleEditClick = (invitation: ProviderInvitation) => {
    setSelectedInvitation(invitation);
    setIsEditFormOpen(true);
  };

  const handleEditFormClose = () => {
    setSelectedInvitation(null);
    setIsEditFormOpen(false);
  };

  const handleUpdateInvitation = (updatedInvitation: ProviderInvitation) => {
    setInvitations(current =>
      current.map(inv =>
        inv.id === updatedInvitation.id ? updatedInvitation : inv
      )
    );
  };

  // Mobile invitation card component
  const MobileInvitationCard = ({ invitation }: { invitation: ProviderInvitation }) => {
    return (
      <Card className="mb-3 overflow-hidden border-l-4 animate-fade-in hover:shadow-md transition-all" 
        style={{ 
          borderLeftColor: invitation.status === 'draft' ? '#e5e7eb' : 
                            invitation.status === 'sent' ? '#d8b4fe' : 
                            invitation.status === 'opened' ? '#fcd34d' : 
                            invitation.status === 'signed_up' ? '#93c5fd' : 
                            '#86efac'
        }}>
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-2">
            <div>
              <h3 className="font-medium text-base">{invitation.businessName}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">{invitation.email}</p>
            </div>
            <div className="flex items-center gap-2">
              {invitation.status === "draft" && (
                <input 
                  type="checkbox" 
                  checked={selectedProviders.has(invitation.id)}
                  onChange={() => handleSelect(invitation.id)}
                  className="h-4 w-4"
                />
              )}
              {renderStatusBadge(invitation.status)}
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-2 mb-3">
            <div>
              <p className="text-xs text-gray-500">Service</p>
              <p className="text-sm">{invitation.serviceType}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Code</p>
              <code className="text-xs bg-gray-100 dark:bg-gray-800 p-1 rounded">{invitation.invitationCode}</code>
            </div>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-1.5">
              <span className={`w-6 h-6 rounded-full flex items-center justify-center ${
                hasStatus(invitation, "sent") 
                  ? "bg-green-100 text-green-600" 
                  : "bg-gray-100 text-gray-400"
              }`}>
                {hasStatus(invitation, "sent") ? <Check className="h-3 w-3" /> : "1"}
              </span>
              <span className={`w-6 h-6 rounded-full flex items-center justify-center ${
                hasStatus(invitation, "opened") 
                  ? "bg-green-100 text-green-600" 
                  : "bg-gray-100 text-gray-400"
              }`}>
                {hasStatus(invitation, "opened") ? <Check className="h-3 w-3" /> : "2"}
              </span>
              <span className={`w-6 h-6 rounded-full flex items-center justify-center ${
                hasStatus(invitation, "signed_up") 
                  ? "bg-green-100 text-green-600" 
                  : "bg-gray-100 text-gray-400"
              }`}>
                {hasStatus(invitation, "signed_up") ? <Check className="h-3 w-3" /> : "3"}
              </span>
              <span className={`w-6 h-6 rounded-full flex items-center justify-center ${
                hasStatus(invitation, "logged_in") 
                  ? "bg-green-100 text-green-600" 
                  : "bg-gray-100 text-gray-400"
              }`}>
                {hasStatus(invitation, "logged_in") ? <Check className="h-3 w-3" /> : "4"}
              </span>
            </div>
            
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-8 w-8 p-0"
              onClick={() => handleEditClick(invitation)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Provider Invitations</h1>
        <p className="text-muted-foreground">Upload, send and track provider invitations</p>
      </div>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-100 dark:border-blue-800">
          <CardContent className="flex flex-row items-center justify-between p-4 md:p-6">
            <div className="space-y-0.5">
              <p className="text-xs md:text-sm text-blue-700 dark:text-blue-300">Total</p>
              <p className="text-xl md:text-2xl font-bold text-blue-900 dark:text-blue-100">{stats.total}</p>
            </div>
            <CardIcon color="bg-blue-100 dark:bg-blue-800">
              <Mail className="h-4 w-4 md:h-5 md:w-5 text-blue-700 dark:text-blue-300" />
            </CardIcon>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-purple-50 to-fuchsia-50 dark:from-purple-900/20 dark:to-fuchsia-900/20 border-purple-100 dark:border-purple-800">
          <CardContent className="flex flex-row items-center justify-between p-4 md:p-6">
            <div className="space-y-0.5">
              <p className="text-xs md:text-sm text-purple-700 dark:text-purple-300">Sent</p>
              <p className="text-xl md:text-2xl font-bold text-purple-900 dark:text-purple-100">{stats.sent}</p>
            </div>
            <CardIcon color="bg-purple-100 dark:bg-purple-800">
              <Mail className="h-4 w-4 md:h-5 md:w-5 text-purple-700 dark:text-purple-300" />
            </CardIcon>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 border-amber-100 dark:border-amber-800">
          <CardContent className="flex flex-row items-center justify-between p-4 md:p-6">
            <div className="space-y-0.5">
              <p className="text-xs md:text-sm text-amber-700 dark:text-amber-300">Opened</p>
              <p className="text-xl md:text-2xl font-bold text-amber-900 dark:text-amber-100">{stats.opened}</p>
            </div>
            <CardIcon color="bg-amber-100 dark:bg-amber-800">
              <Mail className="h-4 w-4 md:h-5 md:w-5 text-amber-700 dark:text-amber-300" />
            </CardIcon>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border-blue-100 dark:border-blue-800">
          <CardContent className="flex flex-row items-center justify-between p-4 md:p-6">
            <div className="space-y-0.5">
              <p className="text-xs md:text-sm text-blue-700 dark:text-blue-300">Signed Up</p>
              <p className="text-xl md:text-2xl font-bold text-blue-900 dark:text-blue-100">{stats.signedUp}</p>
            </div>
            <CardIcon color="bg-blue-100 dark:bg-blue-800">
              <Check className="h-4 w-4 md:h-5 md:w-5 text-blue-700 dark:text-blue-300" />
            </CardIcon>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-100 dark:border-green-800">
          <CardContent className="flex flex-row items-center justify-between p-4 md:p-6">
            <div className="space-y-0.5">
              <p className="text-xs md:text-sm text-green-700 dark:text-green-300">Logged In</p>
              <p className="text-xl md:text-2xl font-bold text-green-900 dark:text-green-100">{stats.loggedIn}</p>
            </div>
            <CardIcon color="bg-green-100 dark:bg-green-800">
              <Check className="h-4 w-4 md:h-5 md:w-5 text-green-700 dark:text-green-300" />
            </CardIcon>
          </CardContent>
        </Card>
      </div>
      
      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4 p-1 bg-gray-100 dark:bg-gray-800 overflow-hidden rounded-lg">
          <TabsTrigger 
            value="upload" 
            className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-primary dark:data-[state=active]:text-primary-foreground transition-all"
          >
            <Upload className="h-4 w-4 mr-2" />
            Upload CSV
          </TabsTrigger>
          <TabsTrigger 
            value="manage" 
            className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-primary dark:data-[state=active]:text-primary-foreground transition-all"
          >
            <Mail className="h-4 w-4 mr-2" />
            Manage Invitations
          </TabsTrigger>
        </TabsList>
        
        {/* Upload CSV Tab */}
        <TabsContent value="upload" className="animate-fade-in">
          <Card>
            <CardHeader>
              <CardTitle>Upload Provider List</CardTitle>
              <CardDescription>
                Upload a CSV file containing provider details to send invitation emails
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-6 md:p-10 text-center hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="p-3 rounded-full bg-primary/10">
                    <Upload className="h-6 w-6 text-primary" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Upload CSV File</h3>
                    <p className="text-sm text-muted-foreground">
                      CSV should include: Business Name, Email, Phone (optional),<br />
                      Service Type, City/State (optional)
                    </p>
                  </div>
                  <Input 
                    id="file-upload" 
                    type="file" 
                    accept=".csv" 
                    className="w-full max-w-xs cursor-pointer"
                    onChange={handleFileChange} 
                  />
                </div>
              </div>
              
              {csvFile && (
                <div className="flex items-center justify-between p-4 border rounded-lg bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 animate-scale-in">
                  <div>
                    <p className="font-medium">{csvFile.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {(csvFile.size / 1024).toFixed(2)} KB
                    </p>
                  </div>
                  <Button 
                    onClick={handleUploadCSV} 
                    disabled={isLoading}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    {isLoading ? "Uploading..." : "Process CSV"}
                  </Button>
                </div>
              )}
              
              <div className="bg-blue-50 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200 p-4 rounded-lg border border-blue-100 dark:border-blue-800">
                <h4 className="font-medium mb-2">CSV Format</h4>
                <p className="text-sm mb-2">Your CSV file should include the following columns:</p>
                <code className="text-xs bg-blue-100 dark:bg-blue-800 p-2 rounded block overflow-x-auto">
                  business_name,email,phone,service_type,location
                </code>
                <p className="text-xs mt-2">Example: "Mike's Plumbing,<EMAIL>,************,Plumbing,Austin TX"</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Manage Invitations Tab */}
        <TabsContent value="manage" className="animate-fade-in">
          <Card>
            <CardHeader className="flex flex-col space-y-2">
              <div className="flex flex-col md:flex-row justify-between md:items-center">
                <div>
                  <CardTitle>Manage Provider Invitations</CardTitle>
                  <CardDescription>
                    Send and track invitations to providers
                  </CardDescription>
                </div>
                {!isMobile && (
                  <div className="flex items-center gap-2 mt-4 md:mt-0">
                    <div className="relative flex-1">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="search"
                        placeholder="Search providers..."
                        className="pl-8 w-full md:w-auto"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                    <select
                      className="bg-background text-foreground px-3 py-2 border rounded"
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                    >
                      <option value="all">All Statuses</option>
                      <option value="draft">Draft</option>
                      <option value="sent">Sent</option>
                      <option value="opened">Opened</option>
                      <option value="signed_up">Signed Up</option>
                      <option value="logged_in">Logged In</option>
                    </select>
                  </div>
                )}
              </div>
              
              {isMobile && (
                <div className="space-y-3">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search providers..."
                      className="pl-8 w-full"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <div className="flex justify-between items-center">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex items-center"
                      onClick={() => setShowFilters(!showFilters)}
                    >
                      <Filter className="h-4 w-4 mr-1" />
                      Filters
                      {statusFilter !== "all" && <span className="ml-1 bg-blue-100 text-blue-800 px-1 rounded text-xs">1</span>}
                    </Button>
                    
                    <Button 
                      size="sm" 
                      disabled={selectedProviders.size === 0 || isLoading}
                      onClick={handleSendInvitations}
                      className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
                    >
                      <Mail className="mr-1 h-4 w-4" />
                      {isLoading ? "Sending..." : `Send ${selectedProviders.size > 0 ? `(${selectedProviders.size})` : ''}`}
                    </Button>
                  </div>
                  
                  {showFilters && (
                    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg animate-fade-in">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-sm">Filters</h3>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => setShowFilters(false)}>
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="space-y-2">
                        <label className="text-xs font-medium block">Status</label>
                        <select
                          className="bg-background text-foreground px-3 py-2 border rounded w-full"
                          value={statusFilter}
                          onChange={(e) => setStatusFilter(e.target.value)}
                        >
                          <option value="all">All Statuses</option>
                          <option value="draft">Draft</option>
                          <option value="sent">Sent</option>
                          <option value="opened">Opened</option>
                          <option value="signed_up">Signed Up</option>
                          <option value="logged_in">Logged In</option>
                        </select>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardHeader>
            <CardContent>
              {!isMobile && (
                <div className="mb-4 flex justify-between items-center">
                  <div>
                    <span className="text-muted-foreground text-sm">
                      {selectedProviders.size} providers selected
                    </span>
                  </div>
                  <div>
                    <Button 
                      disabled={selectedProviders.size === 0 || isLoading}
                      onClick={handleSendInvitations}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      <Mail className="mr-2 h-4 w-4" />
                      {isLoading ? "Sending..." : "Send Invitations"}
                    </Button>
                  </div>
                </div>
              )}
              
              {/* Desktop table view */}
              {!isMobile && (
                <div className="border rounded-md overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <input 
                            type="checkbox" 
                            checked={selectedAll}
                            onChange={handleSelectAll}
                            className="h-4 w-4"
                          />
                        </TableHead>
                        <TableHead>Business Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead className="hidden md:table-cell">Service Type</TableHead>
                        <TableHead className="hidden lg:table-cell">Invitation Code</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="hidden md:table-cell">Sent</TableHead>
                        <TableHead className="text-center">Tracking</TableHead>
                        <TableHead className="w-12">Edit</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredInvitations.length > 0 ? (
                        filteredInvitations.map((invitation) => (
                          <TableRow key={invitation.id} className="animate-fade-in">
                            <TableCell>
                              {invitation.status === "draft" && (
                                <input 
                                  type="checkbox" 
                                  checked={selectedProviders.has(invitation.id)}
                                  onChange={() => handleSelect(invitation.id)}
                                  className="h-4 w-4"
                                />
                              )}
                            </TableCell>
                            <TableCell className="font-medium">{invitation.businessName}</TableCell>
                            <TableCell>{invitation.email}</TableCell>
                            <TableCell className="hidden md:table-cell">{invitation.serviceType}</TableCell>
                            <TableCell className="hidden lg:table-cell">
                              <code className="text-xs bg-muted p-1 rounded">{invitation.invitationCode}</code>
                            </TableCell>
                            <TableCell>{renderStatusBadge(invitation.status)}</TableCell>
                            <TableCell className="hidden md:table-cell">
                              {formatDate(invitation.sentDate)}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center justify-center gap-2">
                                <span className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                  hasStatus(invitation, "sent") 
                                    ? "bg-green-100 text-green-600" 
                                    : "bg-gray-100 text-gray-400"
                                }`}>
                                  {hasStatus(invitation, "sent") ? <Check className="h-4 w-4" /> : "1"}
                                </span>
                                <span className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                  hasStatus(invitation, "opened") 
                                    ? "bg-green-100 text-green-600" 
                                    : "bg-gray-100 text-gray-400"
                                }`}>
                                  {hasStatus(invitation, "opened") ? <Check className="h-4 w-4" /> : "2"}
                                </span>
                                <span className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                  hasStatus(invitation, "signed_up") 
                                    ? "bg-green-100 text-green-600" 
                                    : "bg-gray-100 text-gray-400"
                                }`}>
                                  {hasStatus(invitation, "signed_up") ? <Check className="h-4 w-4" /> : "3"}
                                </span>
                                <span className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                  hasStatus(invitation, "logged_in") 
                                    ? "bg-green-100 text-green-600" 
                                    : "bg-gray-100 text-gray-400"
                                }`}>
                                  {hasStatus(invitation, "logged_in") ? <Check className="h-4 w-4" /> : "4"}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8"
                                onClick={() => handleEditClick(invitation)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={9} className="text-center py-8">
                            No invitations match your filters.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
              
              {/* Mobile card view */}
              {isMobile && (
                <div className="mt-1">
                  {filteredInvitations.length > 0 ? (
                    filteredInvitations.map((invitation) => (
                      <MobileInvitationCard key={invitation.id} invitation={invitation} />
                    ))
                  ) : (
                    <div className="flex flex-col items-center justify-center text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <Mail className="h-12 w-12 text-gray-400 mb-3" />
                      <p className="text-gray-500 dark:text-gray-400">No invitations match your filters.</p>
                    </div>
                  )}
                </div>
              )}
              
              <div className="mt-4">
                <div className="text-xs text-muted-foreground flex flex-wrap items-center gap-2">
                  <div className="flex items-center">
                    <span className="w-5 h-5 rounded-full flex items-center justify-center bg-gray-100 text-gray-400 mr-1">1</span>
                    <span>Sent</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-5 h-5 rounded-full flex items-center justify-center bg-gray-100 text-gray-400 mr-1">2</span>
                    <span>Opened</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-5 h-5 rounded-full flex items-center justify-center bg-gray-100 text-gray-400 mr-1">3</span>
                    <span>Signed Up</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-5 h-5 rounded-full flex items-center justify-center bg-gray-100 text-gray-400 mr-1">4</span>
                    <span>Logged In</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <EditInvitationForm
        invitation={selectedInvitation}
        isOpen={isEditFormOpen}
        onClose={handleEditFormClose}
        onSave={handleUpdateInvitation}
      />
    </div>
  );
};

