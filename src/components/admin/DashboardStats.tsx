import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  ArrowUpRight,
  ArrowDownRight,
  Users,
  Briefcase,
  DollarSign,
  Star,
  Info,
  User
} from "lucide-react";
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { userStatsService } from '@/services/userStatsService';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { toast } from 'sonner';

type StatDetailType = {
  title: string;
  value: string;
  change: string;
  isPositive: boolean;
  icon: React.ElementType;
  color: string;
  details: {
    description: string;
    breakdown: Array<{
      label: string;
      value: string | number;
    }>;
    chart?: React.ReactNode;
    trends?: Array<{
      period: string;
      value: string | number;
      change: string;
      isPositive: boolean;
    }>;
  }
};

export const DashboardStats = () => {
  const isMobile = useIsMobile();
  const { token } = useAuth();
  const [selectedStat, setSelectedStat] = useState<StatDetailType | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [activeUsers, setActiveUsers] = useState({ activeProviders: 0, activeCustomers: 0 });
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  // Fetch active users count
  useEffect(() => {
    const fetchActiveUsers = async () => {
      setIsLoadingStats(true);
      try {
        // Fetch active providers (role_id=3)
        const providersResponse = await userStatsService.getActiveUsersCount(token || '', 3);
        // Fetch active customers (role_id=2)
        const customersResponse = await userStatsService.getActiveUsersCount(token || '', 2);

        if (providersResponse.isSuccess && customersResponse.isSuccess) {
          setActiveUsers({
            activeProviders: providersResponse.data?.activeProviders || 0,
            activeCustomers: customersResponse.data?.activeCustomers || 0
          });
        } else {
          console.error('Failed to fetch active users count:',
            providersResponse.error || customersResponse.error);
          toast.error('Failed to load user statistics');
        }
      } catch (error) {
        console.error('Error fetching active users count:', error);
        toast.error('Failed to load user statistics');
      } finally {
        setIsLoadingStats(false);
      }
    };

    fetchActiveUsers();
  }, [token]);

  // Stats data with real active users count
  const stats: StatDetailType[] = [
    {
      title: "Active Providers",
      value: isLoadingStats ? "..." : activeUsers.activeProviders.toString(),
      change: "+12%",
      isPositive: true,
      icon: Users,
      color: "bg-blue-500",
      details: {
        description: "Track all active service providers registered on the platform.",
        breakdown: [
          { label: "Active Providers", value: isLoadingStats ? "..." : activeUsers.activeProviders },
          { label: "Pending Verification", value: 18 },
          { label: "Inactive Accounts", value: 15 },
        ],
        trends: [
          { period: "Last Week", value: "138", change: "+5%", isPositive: true },
          { period: "Last Month", value: "130", change: "+11.5%", isPositive: true },
          { period: "Last Quarter", value: "120", change: "+20.8%", isPositive: true },
        ]
      }
    },
    {
      title: "Active Customers",
      value: isLoadingStats ? "..." : activeUsers.activeCustomers.toString(),
      change: "+8%",
      isPositive: true,
      icon: User,
      color: "bg-green-500",
      details: {
        description: "Track all active customers registered on the platform.",
        breakdown: [
          { label: "Active Customers", value: isLoadingStats ? "..." : activeUsers.activeCustomers },
          { label: "New This Month", value: 3 },
          { label: "Inactive Accounts", value: 2 },
        ],
        trends: [
          { period: "Last Week", value: "6", change: "+20%", isPositive: true },
          { period: "Last Month", value: "5", change: "+40%", isPositive: true },
          { period: "Last Quarter", value: "4", change: "+75%", isPositive: true },
        ]
      }
    },
    {
      title: "Active Jobs",
      value: "89",
      change: "+5%",
      isPositive: true,
      icon: Briefcase,
      color: "bg-green-500",
      details: {
        description: "Overview of all active projects and service requests.",
        breakdown: [
          { label: "In Progress", value: 42 },
          { label: "Scheduled", value: 27 },
          { label: "Awaiting Payment", value: 20 },
        ],
        trends: [
          { period: "Last Week", value: "84", change: "+6%", isPositive: true },
          { period: "Last Month", value: "76", change: "+17.1%", isPositive: true },
          { period: "Last Quarter", value: "65", change: "+36.9%", isPositive: true },
        ]
      }
    },
    {
      title: "Revenue",
      value: "$24,320",
      change: "-3%",
      isPositive: false,
      icon: DollarSign,
      color: "bg-purple-500",
      details: {
        description: "Platform revenue from completed jobs and subscription fees.",
        breakdown: [
          { label: "Job Commissions", value: "$14,850" },
          { label: "Subscription Fees", value: "$8,470" },
          { label: "Premium Listings", value: "$1,000" },
        ],
        trends: [
          { period: "Last Week", value: "$25,100", change: "-3.1%", isPositive: false },
          { period: "Last Month", value: "$23,500", change: "+3.5%", isPositive: true },
          { period: "Last Quarter", value: "$72,000", change: "+1.8%", isPositive: true },
        ]
      }
    },
    {
      title: "Avg. Rating",
      value: "4.8",
      change: "+0.2",
      isPositive: true,
      icon: Star,
      color: "bg-amber-500",
      details: {
        description: "Average customer satisfaction rating across all providers.",
        breakdown: [
          { label: "5 Star Reviews", value: "68%" },
          { label: "4 Star Reviews", value: "22%" },
          { label: "3 Star Reviews", value: "8%" },
          { label: "1-2 Star Reviews", value: "2%" },
        ],
        trends: [
          { period: "Last Week", value: "4.7", change: "+0.1", isPositive: true },
          { period: "Last Month", value: "4.6", change: "+0.2", isPositive: true },
          { period: "Last Quarter", value: "4.5", change: "+0.3", isPositive: true },
        ]
      }
    },
  ];

  const handleShowDetails = (stat: StatDetailType) => {
    setSelectedStat(stat);
    setDetailsOpen(true);
  };

  return (
    <>
      <div className={cn(
        "grid gap-4",
        isMobile ? "grid-cols-1" : "grid-cols-2 lg:grid-cols-4"
      )}>
        {stats.map((stat, index) => (
          <Card
            key={index}
            className="overflow-hidden transition-all hover:shadow-md cursor-pointer"
            onClick={() => handleShowDetails(stat)}
          >
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={cn(
                "p-2 rounded-full",
                `${stat.color}/10`
              )}>
                <stat.icon className={cn("h-4 w-4", stat.color.replace('bg-', 'text-'))} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center justify-between pt-1">
                <div className="flex items-center">
                  {stat.isPositive ? (
                    <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <p className={cn(
                    "text-xs",
                    stat.isPositive ? "text-green-500" : "text-red-500"
                  )}>
                    {stat.change} from last month
                  </p>
                </div>
                <div className="text-xs text-blue-500 hover:text-blue-700 flex items-center gap-1">
                  <Info className="h-3 w-3" />
                  <span>View details</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Stat Details Dialog */}
      <StatDetailsDialog
        stat={selectedStat}
        open={detailsOpen}
        onOpenChange={setDetailsOpen}
      />
    </>
  );
};

interface StatDetailsDialogProps {
  stat: StatDetailType | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const StatDetailsDialog: React.FC<StatDetailsDialogProps> = ({ stat, open, onOpenChange }) => {
  const [activeTab, setActiveTab] = useState("overview");

  if (!stat) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className={cn(
              "p-2 rounded-full",
              `${stat.color}/10`
            )}>
              <stat.icon className={cn("h-5 w-5", stat.color.replace('bg-', 'text-'))} />
            </div>
            <DialogTitle>{stat.title} Statistics</DialogTitle>
          </div>
        </DialogHeader>

        <div className="py-4">
          <div className="flex justify-between items-baseline mb-6">
            <h3 className="text-3xl font-bold">{stat.value}</h3>
            <div className="flex items-center">
              {stat.isPositive ? (
                <ArrowUpRight className="h-5 w-5 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="h-5 w-5 text-red-500 mr-1" />
              )}
              <p className={cn(
                "text-sm font-medium",
                stat.isPositive ? "text-green-500" : "text-red-500"
              )}>
                {stat.change} from last month
              </p>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3 mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="breakdown">Breakdown</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-3">
              <p className="text-muted-foreground">{stat.details.description}</p>
              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Value</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-semibold">{stat.value}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Change</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center">
                      {stat.isPositive ? (
                        <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                      ) : (
                        <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
                      )}
                      <p className={cn(
                        "text-lg font-semibold",
                        stat.isPositive ? "text-green-500" : "text-red-500"
                      )}>
                        {stat.change}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="breakdown" className="space-y-4">
              <div className="grid gap-3">
                {stat.details.breakdown.map((item, index) => (
                  <div key={index} className="flex justify-between items-center p-3 border rounded-md hover:bg-gray-50 transition-colors">
                    <span className="font-medium">{item.label}</span>
                    <span className={cn(
                      "px-3 py-1 rounded-full text-sm font-medium",
                      stat.color.replace('bg', 'bg') + "/10",
                      stat.color.replace('bg', 'text')
                    )}>
                      {item.value}
                    </span>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="trends" className="space-y-4">
              <div className="grid gap-3">
                {stat.details.trends && stat.details.trends.map((trend, index) => (
                  <Card key={index} className="overflow-hidden">
                    <CardHeader className="pb-1">
                      <CardTitle className="text-sm">{trend.period}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-between items-center">
                        <p className="text-lg font-semibold">{trend.value}</p>
                        <div className="flex items-center">
                          {trend.isPositive ? (
                            <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                          ) : (
                            <ArrowDownRight className="h-3 w-3 text-red-500 mr-1" />
                          )}
                          <p className={cn(
                            "text-xs font-medium",
                            trend.isPositive ? "text-green-500" : "text-red-500"
                          )}>
                            {trend.change}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
