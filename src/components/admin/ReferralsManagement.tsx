
import { useState } from "react";
import { 
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow 
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { Search, MoreVertical, Eye, CheckCircle, Filter, Download, Edit, Trash, X, ChevronRight } from "lucide-react";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON>Header,
  She<PERSON><PERSON>itle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AdminNotes } from "@/components/admin/shared/AdminNotes";

export const ReferralsManagement = () => {
  const { toast } = useToast();
  const { isMobile } = useUIHelpers();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedReferral, setSelectedReferral] = useState<any | null>(null);
  const [showReferralDetails, setShowReferralDetails] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedReferrals, setSelectedReferrals] = useState<string[]>([]);
  const [showNotesDialog, setShowNotesDialog] = useState(false);
  const [activeTab, setActiveTab] = useState("details");
  const [filterOpen, setFilterOpen] = useState(false);
  const [showMobileDetails, setShowMobileDetails] = useState(false);

  // Mock data - in a real app, this would come from an API
  const referrals = [
    {
      id: "REF-1001",
      referrerName: "Sarah Johnson",
      referredName: "Michael Brown",
      status: "Completed",
      creditEarned: 50.00,
      date: "2025-04-10",
      email: "<EMAIL>",
      notes: [
        { text: "Referred through Facebook share", timestamp: "2025-04-09T14:30:00Z", admin: "AdminUser" },
      ]
    },
    {
      id: "REF-1002",
      referrerName: "David Wilson",
      referredName: "Jessica Smith",
      status: "Pending",
      creditEarned: 0.00,
      date: "2025-04-14",
      email: "<EMAIL>",
      notes: []
    },
    {
      id: "REF-1003",
      referrerName: "Emily Davis",
      referredName: "Robert Taylor",
      status: "Completed",
      creditEarned: 50.00,
      date: "2025-04-08",
      email: "<EMAIL>",
      notes: []
    },
    {
      id: "REF-1004",
      referrerName: "Emily Davis",
      referredName: "Amanda Lewis",
      status: "Pending",
      creditEarned: 0.00,
      date: "2025-04-15",
      email: "<EMAIL>",
      notes: []
    },
    {
      id: "REF-1005",
      referrerName: "James Miller",
      referredName: "Daniel Clark",
      status: "Completed",
      creditEarned: 50.00,
      date: "2025-04-09",
      email: "<EMAIL>",
      notes: []
    }
  ];

  // Filter referrals based on search query and status filter
  const filteredReferrals = referrals.filter(referral => {
    const matchesSearch = 
      referral.referrerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      referral.referredName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      referral.id.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatusFilter = 
      statusFilter === "all" || 
      referral.status.toLowerCase() === statusFilter.toLowerCase();
    
    return matchesSearch && matchesStatusFilter;
  });

  const handleViewDetails = (referral: any) => {
    setSelectedReferral(referral);
    if (isMobile) {
      setShowMobileDetails(true);
    } else {
      setShowReferralDetails(true);
    }
  };

  const handleMarkComplete = (referral: any) => {
    // In a real app, this would be an API call
    toast({
      title: "Referral marked complete",
      description: `Referral ${referral.id} has been marked as completed and a $50 credit has been awarded.`,
    });
  };

  const handleCheckboxChange = (id: string) => {
    setSelectedReferrals(prev => 
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedReferrals(filteredReferrals.map(referral => referral.id));
    } else {
      setSelectedReferrals([]);
    }
  };

  const handleBulkMarkComplete = () => {
    // In a real app, this would be an API call
    toast({
      title: "Bulk action completed",
      description: `${selectedReferrals.length} referrals have been marked as completed.`,
    });
    setSelectedReferrals([]);
  };

  const handleExportCSV = () => {
    toast({
      title: "Export initiated",
      description: "Referrals data is being exported to CSV.",
    });
  };

  const handleNotesOpen = (referral: any) => {
    setSelectedReferral(referral);
    setShowNotesDialog(true);
  };

  const handleAddNote = (note: string) => {
    if (selectedReferral) {
      // In a real app, this would be an API call
      toast({
        title: "Note added",
        description: "Your note has been added to this referral.",
      });
      setShowNotesDialog(false);
    }
  };

  // Helper function for status badge styling
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "Completed":
        return <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">Completed</Badge>;
      case "Pending":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Mobile referral card component
  const MobileReferralCard = ({ referral }: { referral: any }) => (
    <Card 
      className="mb-3" 
      onClick={() => handleViewDetails(referral)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div>
            <h3 className="font-medium">{referral.id}</h3>
            <p className="text-sm text-muted-foreground">{new Date(referral.date).toLocaleDateString()}</p>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(referral.status)}
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>
        
        <div className="mt-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-muted-foreground">Referrer:</span>
            <span className="text-sm">{referral.referrerName}</span>
          </div>
          <div className="flex justify-between items-center mt-1">
            <span className="text-sm font-medium text-muted-foreground">Referred:</span>
            <span className="text-sm">{referral.referredName}</span>
          </div>
          {referral.status === "Completed" && (
            <div className="flex justify-between items-center mt-1">
              <span className="text-sm font-medium text-muted-foreground">Credit:</span>
              <span className="text-sm font-semibold">${referral.creditEarned.toFixed(2)}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
  
  // Mobile referral detail component
  const MobileReferralDetailView = ({ referral, onClose }: { referral: any, onClose: () => void }) => (
    <div className="flex flex-col h-full bg-background">
      <div className="sticky top-0 z-10 bg-background border-b p-4 flex items-center justify-between">
        <h2 className="font-semibold text-lg">Referral Details</h2>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-5 w-5" />
        </Button>
      </div>
      
      <div className="flex-1 overflow-auto p-4">
        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="notes">Admin Notes</TabsTrigger>
          </TabsList>
          
          <TabsContent value="details" className="pt-4 space-y-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Referral ID</h3>
              <p className="font-medium">{referral.id}</p>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
              <p className="font-medium">{new Date(referral.date).toLocaleDateString()}</p>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
              <div>{getStatusBadge(referral.status)}</div>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Credit Earned</h3>
              <p className="font-medium">${referral.creditEarned.toFixed(2)}</p>
            </div>
            
            <div className="space-y-2 pt-2 border-t">
              <h3 className="text-sm font-medium text-muted-foreground">Referrer</h3>
              <p className="font-medium">{referral.referrerName}</p>
            </div>

            <div className="space-y-2 pt-2 border-t">
              <h3 className="text-sm font-medium text-muted-foreground">Referred Friend</h3>
              <p className="font-medium">{referral.referredName}</p>
              <p className="text-sm text-muted-foreground">{referral.email}</p>
            </div>
          </TabsContent>
          
          <TabsContent value="notes" className="pt-4">
            <AdminNotes 
              notes={referral.notes || []} 
              onAddNote={handleAddNote}
              entityType="Referral"
              entityId={referral.id}
            />
          </TabsContent>
        </Tabs>
      </div>
      
      <div className="sticky bottom-0 z-10 bg-background border-t p-4">
        {referral.status === "Pending" ? (
          <Button 
            className="w-full"
            onClick={() => {
              handleMarkComplete(referral);
              onClose();
            }}
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Mark as Complete
          </Button>
        ) : (
          <Button 
            variant="outline" 
            className="w-full"
            onClick={onClose}
          >
            Close
          </Button>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold">Manage Referrals</h1>
        
        {isMobile ? (
          <div className="flex items-center gap-2">
            <Sheet open={filterOpen} onOpenChange={setFilterOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="bottom" className="h-[50vh]">
                <SheetHeader className="mb-4">
                  <SheetTitle>Filter Referrals</SheetTitle>
                </SheetHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-1 block">Search</label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search referrals..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-9"
                      />
                      {searchQuery && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                          onClick={() => setSearchQuery("")}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-1 block">Referral Status</label>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="pt-4">
                    <Button 
                      className="w-full" 
                      onClick={() => setFilterOpen(false)}
                    >
                      Apply Filters
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
            
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search referrals..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 w-full"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-wrap items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search referrals..."
                className="pl-8 w-[250px] md:w-[300px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  <SelectValue placeholder="Filter by status" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            
            <Button 
              variant="outline" 
              className="flex items-center gap-2"
              onClick={handleExportCSV}
            >
              <Download className="h-4 w-4" />
              Export CSV
            </Button>
          </div>
        )}
      </div>

      {selectedReferrals.length > 0 && (
        <div className="bg-muted/50 p-2 rounded-md flex items-center justify-between">
          <p className="text-sm">{selectedReferrals.length} referrals selected</p>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setSelectedReferrals([])}
            >
              Deselect All
            </Button>
            <Button 
              size="sm"
              onClick={handleBulkMarkComplete}
              disabled={!selectedReferrals.some(id => {
                const referral = referrals.find(r => r.id === id);
                return referral && referral.status === "Pending";
              })}
            >
              Mark Selected as Complete
            </Button>
          </div>
        </div>
      )}

      {isMobile ? (
        <div className="space-y-2">
          {filteredReferrals.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No referrals found matching your filters.</p>
            </div>
          ) : (
            filteredReferrals.map(referral => (
              <MobileReferralCard key={referral.id} referral={referral} />
            ))
          )}
        </div>
      ) : (
        <Table>
          <TableCaption>A list of all referrals on the platform.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox 
                  checked={selectedReferrals.length > 0 && selectedReferrals.length === filteredReferrals.length}
                  onCheckedChange={handleSelectAll}
                  aria-label="Select all"
                />
              </TableHead>
              <TableHead>Referral ID</TableHead>
              <TableHead>Referrer</TableHead>
              <TableHead>Friend Referred</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Credit Earned</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredReferrals.map((referral) => (
              <TableRow key={referral.id} className={selectedReferrals.includes(referral.id) ? "bg-muted/50" : ""}>
                <TableCell>
                  <Checkbox 
                    checked={selectedReferrals.includes(referral.id)}
                    onCheckedChange={() => handleCheckboxChange(referral.id)}
                    aria-label={`Select referral ${referral.id}`}
                  />
                </TableCell>
                <TableCell className="font-medium">{referral.id}</TableCell>
                <TableCell>{referral.referrerName}</TableCell>
                <TableCell>{referral.referredName}</TableCell>
                <TableCell>{getStatusBadge(referral.status)}</TableCell>
                <TableCell>${referral.creditEarned.toFixed(2)}</TableCell>
                <TableCell>{new Date(referral.date).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewDetails(referral)}>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>View Details</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleNotesOpen(referral)}>
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Add Notes</span>
                      </DropdownMenuItem>
                      {referral.status === "Pending" && (
                        <DropdownMenuItem onClick={() => handleMarkComplete(referral)}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          <span>Mark as Complete</span>
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {/* Referral Details Dialog (Desktop) */}
      <Dialog open={showReferralDetails} onOpenChange={setShowReferralDetails}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Referral Details</DialogTitle>
            <DialogDescription>
              View detailed information about this referral.
            </DialogDescription>
          </DialogHeader>
          
          {selectedReferral && (
            <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="notes">Admin Notes</TabsTrigger>
              </TabsList>
              
              <TabsContent value="details" className="pt-4 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Referral ID</h3>
                    <p className="font-medium">{selectedReferral.id}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
                    <p className="font-medium">{new Date(selectedReferral.date).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                    <p>{getStatusBadge(selectedReferral.status)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Credit Earned</h3>
                    <p className="font-medium">${selectedReferral.creditEarned.toFixed(2)}</p>
                  </div>
                </div>

                <div className="space-y-2 pt-2 border-t">
                  <h3 className="text-sm font-medium text-muted-foreground">Referrer</h3>
                  <p className="font-medium">{selectedReferral.referrerName}</p>
                </div>

                <div className="space-y-2 pt-2 border-t">
                  <h3 className="text-sm font-medium text-muted-foreground">Referred Friend</h3>
                  <p className="font-medium">{selectedReferral.referredName}</p>
                  <p className="text-sm text-muted-foreground">{selectedReferral.email}</p>
                </div>
              </TabsContent>
              
              <TabsContent value="notes" className="pt-4">
                <AdminNotes 
                  notes={selectedReferral.notes || []} 
                  onAddNote={handleAddNote}
                  entityType="Referral"
                  entityId={selectedReferral.id}
                />
              </TabsContent>
            </Tabs>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReferralDetails(false)}>Close</Button>
            {selectedReferral && selectedReferral.status === "Pending" && (
              <Button 
                onClick={() => {
                  handleMarkComplete(selectedReferral);
                  setShowReferralDetails(false);
                }}
              >
                Mark as Complete
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Mobile Referral Detail View */}
      {isMobile && selectedReferral && (
        <div className={`fixed inset-0 bg-background z-50 ${showMobileDetails ? 'block' : 'hidden'}`}>
          <MobileReferralDetailView
            referral={selectedReferral}
            onClose={() => setShowMobileDetails(false)}
          />
        </div>
      )}

      {/* Admin Notes Dialog */}
      <Dialog open={showNotesDialog} onOpenChange={setShowNotesDialog}>
        <DialogContent className={isMobile ? "w-[95%] max-w-[500px] p-4" : "sm:max-w-[500px]"}>
          <DialogHeader>
            <DialogTitle>Admin Notes</DialogTitle>
            <DialogDescription>
              Add internal notes for this referral. These notes are only visible to admin users.
            </DialogDescription>
          </DialogHeader>
          
          {selectedReferral && (
            <AdminNotes 
              notes={selectedReferral.notes || []} 
              onAddNote={handleAddNote}
              entityType="Referral"
              entityId={selectedReferral.id}
            />
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowNotesDialog(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
