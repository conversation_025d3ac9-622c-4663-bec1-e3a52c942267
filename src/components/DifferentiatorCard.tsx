
import React from 'react';

interface DifferentiatorCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
}

const DifferentiatorCard: React.FC<DifferentiatorCardProps> = ({
  title,
  description,
  icon
}) => {
  return (
    <div className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
      <div className="bg-blue-50 dark:bg-blue-900/20 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
        {icon}
      </div>
      <h3 className="text-lg font-semibold text-center text-gray-900 dark:text-white mb-3">
        {title}
      </h3>
      <p className="text-center text-gray-600 dark:text-gray-300">
        {description}
      </p>
    </div>
  );
};

export default DifferentiatorCard;
