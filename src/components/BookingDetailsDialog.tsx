import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { DataProviderType } from "@/pages/Bookings.tsx";
import { Checkbox } from "@/components/ui/checkbox";
import {apiService} from "@/services/api.ts";
import { useToast } from '@/hooks/use-toast';
import useAuthHeader from "react-auth-kit/hooks/useAuthHeader";
import {Loader} from "lucide-react";

interface DataBooking {
  id: string;
  title: string[];
  client: { fullName: string; email: string; phone: string };
  provider: { name: string; avatar: string; initials: string };
  status: string;
  date: string;
  amount: string;
  service: { category: string; tasks: string[]; customTask: null };
  location: { address: string; city: string; state: string; zipCode: string };
  description: string | undefined;
}

interface BookingDetailsDialogProps {
  booking: DataBooking | null;
  isOpen: boolean;
  onClose: () => void;
  provider : DataProviderType[];
  isLoadingProviders: boolean;
}


const BookingDetailsDialog: React.FC<BookingDetailsDialogProps> = ({
  booking,
  isOpen,
  onClose,
  provider,
  isLoadingProviders
}) => {
  // State to track selected providers
  const [selectedProviders, setSelectedProviders] = useState<DataProviderType[]>([]);
  const {toast} = useToast();
  const authHeader = useAuthHeader()
  const _id = booking?.id || '';
  const [isSending, setIsSending] = useState(false);

  if (!booking) return null;

  // Handler for when a provider is selected/deselected
  function handleProviderSelection(provider: DataProviderType) {
    setSelectedProviders(prevSelected => {
      // Check if the provider is already selected
      const isAlreadySelected = prevSelected.some(p => p.businessId === provider.businessId);

      if (isAlreadySelected) {
        // If already selected, remove it
        return prevSelected.filter(p => p.businessId !== provider.businessId);
      } else {
        // If not selected, add it
        return [...prevSelected, provider];
      }
    });
  }

  async function handleSubmit() {
    const payload = selectedProviders.map(item => item.businessId);
    setIsSending(true);
    try {
      const { data, error, isSuccess } = await apiService(`/api/job-bookings/${_id}/send`, {
        method: 'POST',
        headers: {
          'Authorization': authHeader || '',
        },
        body: {provider_uuids : payload},
      });

      if (!isSuccess) {
        throw new Error(error || 'HTTP error occurred');
      }

      // Handle success
      toast({
        title: "Success",
        description: "Sent to providers successfully!",
        variant: "default",
        className: "bg-green-400 text-white",
      });
      setIsSending(false);
      onClose();
    }
    catch (error) {
      toast({
        title: "Failed",
        description: "Send to providers failed!",
        variant: "default",
        className: "bg-red-400 text-white",
      });
      setIsSending(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={()=>{
      onClose();
      setSelectedProviders([]);
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Send Project to Providers</DialogTitle>
          <DialogDescription>
            Select providers that should receive this project request
          </DialogDescription>
        </DialogHeader>

        <div className="">
          <div className="font-medium mb-1">Project Details:</div>
          <div className=" flex flex-col gap-2 bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4">
            <p>Id: <span className="text-sm text-gray-500">{booking.id}</span> </p>
            <p>Service: <span  className="text-sm text-gray-500">{booking.service.category}</span></p>
            <p>Location: <span  className="text-sm text-gray-500">{booking.location.address}</span></p>
            <p>Date: <span  className="text-sm text-gray-500">{booking.date}</span></p>
          </div>
        </div>

        <div>
          <h3>
            Available Providers: <span className="text-sm text-gray-500">({selectedProviders.length} selected)</span>
          </h3>
          <div className="min-h-[100px] mt-2 max-h-[250px] flex flex-col justify-center gap-2 overflow-y-auto border border-gray-200 rounded-lg">

            {
              isLoadingProviders ? <div className="text-center">Loading...</div> :
              provider.length === 0 ?
                        <div className="text-center text-gray-500 py-4">
                          No providers available
                        </div> :
              provider.map((item, index) => {
              const isSelected = selectedProviders.some(p => p.businessId === item.businessId);

              return (
                <div 
                  key={index} 
                  className="flex items-center gap-2 px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded cursor-pointer"
                  onClick={() => handleProviderSelection(item)}
                >
                  <Checkbox 
                    id={`provider-${item.businessId}`} 
                    checked={isSelected}
                    onCheckedChange={() => handleProviderSelection(item)}
                  />
                  <div className="flex flex-col">
                    <span className="font-medium">{item.name}</span>
                    <span className="text-xs text-gray-500">{item.category} • {item.location}</span>
                  </div>
                </div>
              );
            })
            }
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={()=>{
            onClose()
            setSelectedProviders([]);
          }}>Cancel</Button>
          <Button 
            onClick={handleSubmit}
            disabled={selectedProviders.length === 0}
          >
            {isSending ? (
                <>
                  <Loader className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
            ) : (
                <>Send to Providers ({selectedProviders.length})</>
            )}

          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BookingDetailsDialog;
