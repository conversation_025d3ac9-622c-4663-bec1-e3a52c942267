
import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface NavigationMenuItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  onClick?: () => void;
  isActive?: boolean;
  className?: string;
  isNested?: boolean;
  badge?: string | number;
}

export const NavigationMenuItem: React.FC<NavigationMenuItemProps> = ({ 
  to, 
  icon, 
  label, 
  onClick, 
  isActive = false,
  className,
  isNested = false,
  badge
}) => {
  return (
    <Link 
      to={to} 
      className={cn(
        "flex items-center gap-3 px-4 py-2.5 rounded-md transition-all",
        "hover:bg-accent/80 dark:hover:bg-gray-800/80 text-foreground",
        isActive ? "bg-primary/10 dark:bg-primary/20 font-medium" : "",
        isNested ? "pl-8 text-sm" : "",
        className
      )}
      onClick={onClick}
    >
      <div className={cn(
        "menu-item-icon flex items-center justify-center w-6",
        isNested ? "opacity-80" : ""
      )}>
        {icon}
      </div>
      <span className={cn(
        "font-medium flex-1",
        isNested ? "text-gray-600 dark:text-gray-300" : ""
      )}>
        {label}
      </span>
      {badge && (
        <span className="bg-primary/20 text-primary text-xs font-medium px-2 py-0.5 rounded-full">
          {badge}
        </span>
      )}
    </Link>
  );
};
