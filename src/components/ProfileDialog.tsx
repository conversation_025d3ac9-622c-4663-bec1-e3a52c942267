import React, { useState, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
} from "@/components/ui/dialog";
import { Shield, MapPin, Clock, Briefcase, MessageSquare, Phone, Info, Settings, Image, Star } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { StarRating } from './StarRating';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSwipeGesture } from '@/hooks/use-swipe-gesture';
import { DataType } from '@/types/common';
import { RequestBidDialog } from './RequestBidDialog';

interface ProfileDialogProps {
  isOpen: boolean;
  onClose: () => void;
  provider: DataType | null;
}

export function ProfileDialog({ isOpen, onClose, provider }: ProfileDialogProps) {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [isRequestBidOpen, setIsRequestBidOpen] = useState(false);

  // Create a memoized callback for the swipe right gesture
  const handleSwipeRight = useCallback(() => {
    if (isMobile && isOpen) {
      onClose();
    }
  }, [isMobile, isOpen, onClose]);

  // Use swipe gesture to close dialog on mobile
  useSwipeGesture({
    onSwipeRight: handleSwipeRight
  });

  if (!provider) return null;

  const handleMessageClick = (e: React.MouseEvent) => {
    e.preventDefault();
    onClose();
    navigate('/messages', {
      state: {
        providerId: provider.businessId || provider.businessId,
        provider: {
          name: provider.name,
          specialty: provider.specialty || provider.category,
          avatar: ""
        }
      }
    });
  };

  // Helper function to format hours
  const parseHourString = (hourString: string) => {
    // Match the day name at the beginning of the string
    const dayMatch = hourString.match(/^([A-Za-z]+)/);
    const day = dayMatch ? dayMatch[0] : '';

    // Extract everything after the day name
    const remainingText = hourString.substring(day.length);

    // Find time pattern in the remaining text
    let time = 'Closed';

    if (remainingText.includes('Closed')) {
      time = 'Closed';
    } else {
      // Look for time pattern like "8:30AM–5:30PM"
      const timeMatch = remainingText.match(/([0-9:]+[A-Za-z]+[–-][0-9:]+[A-Za-z]+)/);
      if (timeMatch) {
        time = timeMatch[0];
      }
    }

    return { day, time };
  };

  return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogTitle className="sr-only">Provider Profile</DialogTitle>

          <div className="flex flex-col mt-10">
            {/* Header section with profile info */}
            <div className="flex flex-col sm:flex-row gap-4 items-center sm:items-start pb-6 border-b">
              <div className="relative">
                <div className="h-24 w-24 sm:h-28 sm:w-28 rounded-full bg-gray-100 flex items-center justify-center text-3xl font-semibold text-gray-500">
                  {provider.name?.charAt(0)}
                </div>
                {provider.badges?.includes('verified') && (
                    <div className="absolute top-0 right-0 bg-green-600 rounded-full p-1">
                      <Shield className="h-4 w-4 text-white" />
                    </div>
                )}
              </div>

              <div className="flex-1 text-center sm:text-left">
                <h2 className="text-2xl font-bold mb-1">{provider.name}</h2>
                <p className="text-gray-600 dark:text-gray-300 mb-2">{provider.specialty || provider.category}</p>
                <p className="text-gray-600 dark:text-gray-300 my-4">
                    {provider.address}
                  </p>
                <div className="flex items-center justify-center sm:justify-start mb-3">
                  <StarRating rating={provider.rating || 0} />
                  <span className="ml-2 text-gray-600 dark:text-gray-300">
                  {provider.rating || 0} ({provider.reviewCount || 0} reviews)
                </span>
                </div>

                <div className="flex flex-wrap gap-2">
                  {provider.badges?.map((badge) => {
                    if (badge === 'verified') return null;
                    return (
                        <Badge
                            key={badge}
                            variant="outline"
                            className="bg-gray-50 dark:bg-gray-800"
                        >
                          {badge === 'topRated' ? 'Top Rated' :
                              badge === 'fastResponse' ? 'Fast Response' :
                                  badge === 'mostHired' ? 'Most Hired' : badge}
                        </Badge>
                    );
                  })}
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                <Button
                    className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                    onClick={handleMessageClick}
                >
                  <MessageSquare className="h-4 w-4" />
                  Message
                </Button>
                {/*<Button*/}
                {/*    variant="outline"*/}
                {/*    className="flex items-center gap-2"*/}
                {/*>*/}
                {/*  <Phone className="h-4 w-4" />*/}
                {/*  Call*/}
                {/*</Button>*/}
              </div>
            </div>

            {/* Tabs section */}
            <Tabs defaultValue="about" className="mt-6">
              <TabsList className={`grid grid-cols-4 mb-6 ${isMobile ? 'p-1 gap-1 h-20' : ''}`}>
                <TabsTrigger
                    value="about"
                    className={isMobile ? "flex flex-col items-center justify-center gap-1 py-2" : ""}
                >
                  {isMobile && <Info className="h-5 w-5" />}
                  About
                </TabsTrigger>
                <TabsTrigger
                    value="services"
                    className={isMobile ? "flex flex-col items-center justify-center gap-1 py-2" : ""}
                >
                  {isMobile && <Settings className="h-5 w-5" />}
                  Services
                </TabsTrigger>
                <TabsTrigger
                    value="portfolio"
                    className={isMobile ? "flex flex-col items-center justify-center gap-1 py-2" : ""}
                >
                  {isMobile && <Image className="h-5 w-5" />}
                  Portfolio
                </TabsTrigger>
                <TabsTrigger
                    value="reviews"
                    className={isMobile ? "flex flex-col items-center justify-center gap-1 py-2" : ""}
                >
                  {isMobile && <Star className="h-5 w-5" />}
                  Reviews
                </TabsTrigger>
              </TabsList>

              <TabsContent value="about" className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-3">About {provider.name}</h3>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    {provider.name} is a licensed {(provider.specialty || provider.category || 'service').toLowerCase()} expert with over {((provider.businessId || provider.businessId || '').length % 5) + 4} years of experience in the industry.
                    We take pride in delivering high-quality services tailored to each client's specific needs and preferences.
                  </p>
                  <p className="text-gray-700 dark:text-gray-300">
                    Our team consists of trained professionals who are passionate about their work. We use sustainable practices and premium materials to ensure long-lasting results.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold mb-4">Business Details</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <Briefcase className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">In business since</p>
                        <p className="font-medium">2018</p>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-green-50 flex items-center justify-center mr-3">
                        <Briefcase className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Projects completed</p>
                        {/*<p className="font-medium">{provider.completedJobs?.length || 78}+</p>*/}
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-amber-50 flex items-center justify-center mr-3">
                        <Clock className="h-5 w-5 text-amber-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Response time</p>
                        <p className="font-medium">&lt; 1 hour</p>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-purple-50 flex items-center justify-center mr-3">
                        <MapPin className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Location</p>
                        <p className="font-medium">{provider.distance || '3.2 miles away'}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-xl font-semibold mb-4">Business Hours</h3>
                  <div className="space-y-2">
                    {provider.hours && typeof provider.hours === 'object' && !Array.isArray(provider.hours) ? (
                      <>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Monday</span>
                          <span className="font-medium">{provider.hours.monday || '8:00 AM - 6:00 PM'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Tuesday</span>
                          <span className="font-medium">{provider.hours.tuesday || '8:00 AM - 6:00 PM'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Wednesday</span>
                          <span className="font-medium">{provider.hours.wednesday || '8:00 AM - 6:00 PM'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Thursday</span>
                          <span className="font-medium">{provider.hours.thursday || '8:00 AM - 6:00 PM'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Friday</span>
                          <span className="font-medium">{provider.hours.friday || '8:00 AM - 5:00 PM'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Saturday</span>
                          <span className="font-medium">{provider.hours.saturday || '9:00 AM - 2:00 PM'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Sunday</span>
                          <span className="font-medium">{provider.hours.sunday || 'Closed'}</span>
                        </div>
                      </>
                    ) : Array.isArray(provider.hours) ? (
                      provider.hours.map((hourString, index) => {
                        const { day, time } = parseHourString(hourString);
                        return (
                          <div key={index} className="flex justify-between">
                            <span className="text-gray-600">{day}</span>
                            <span className="font-medium">{time}</span>
                          </div>
                        );
                      })
                    ) : (
                      <>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Monday</span>
                          <span className="font-medium">8:00 AM - 6:00 PM</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Tuesday</span>
                          <span className="font-medium">8:00 AM - 6:00 PM</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Wednesday</span>
                          <span className="font-medium">8:00 AM - 6:00 PM</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Thursday</span>
                          <span className="font-medium">8:00 AM - 6:00 PM</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Friday</span>
                          <span className="font-medium">8:00 AM - 5:00 PM</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Saturday</span>
                          <span className="font-medium">9:00 AM - 2:00 PM</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Sunday</span>
                          <span className="font-medium">Closed</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="services">
                <h3 className="text-xl font-semibold mb-4">Services Offered</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="font-medium mb-2">Residential Services</h4>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>Leak detection and repair</li>
                      <li>Fixture installation and repair</li>
                      <li>Pipe installation and replacement</li>
                      <li>Drain cleaning and unclogging</li>
                      <li>Water heater services</li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="font-medium mb-2">Commercial Services</h4>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>Commercial plumbing maintenance</li>
                      <li>Backflow prevention</li>
                      <li>Commercial drain cleaning</li>
                      <li>Water line services</li>
                      <li>Commercial fixture installation</li>
                    </ul>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="portfolio">
                <h3 className="text-xl font-semibold mb-4">Photos</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {provider.photos?.map((photo, index) => (
                    <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                      <img 
                        src={photo} 
                        alt={`${provider.name} - photo ${index + 1}`} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )) || (
                    <>
                      <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center text-gray-400">
                        Portfolio image 1
                      </div>
                      <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center text-gray-400">
                        Portfolio image 2
                      </div>
                      <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center text-gray-400">
                        Portfolio image 3
                      </div>
                      <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center text-gray-400">
                        Portfolio image 4
                      </div>
                      <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center text-gray-400">
                        Portfolio image 5
                      </div>
                      <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center text-gray-400">
                        Portfolio image 6
                      </div>
                    </>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="reviews">
                <h3 className="text-xl font-semibold mb-4">Customer Reviews</h3>
                <div className="space-y-4">
                  {provider.reviews && provider.reviews.length > 0 ? (
                      provider.reviews.map((review, index) => (
                          <div key={index} className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                            <div className="flex items-center mb-2">
                              <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                                {(review.author || 'A').charAt(0)}
                              </div>
                              <div>
                                <p className="font-medium">{review.author || 'Anonymous User'}</p>
                                <div className="flex items-center">
                                  <StarRating rating={parseInt(review.rating) || 5} size="sm" />
                                  <span className="text-xs ml-1 text-gray-500">{review.date || '1 month ago'}</span>
                                </div>
                              </div>
                            </div>
                            <p className="text-sm">{review.text || 'Great service!'}</p>
                          </div>
                      ))
                  ) : (
                      <div className="text-center py-4 text-gray-500">No reviews yet</div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </DialogContent>

        {/* Request Bid Dialog */}
        <RequestBidDialog
          isOpen={isRequestBidOpen}
          onClose={() => setIsRequestBidOpen(false)}
          providerId={provider?.businessId || ""}
          providerName={provider?.name || "this provider"}
        />
      </Dialog>
  );
}

export default ProfileDialog;
