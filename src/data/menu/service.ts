// Tạo một file mới, ví dụ: services-data.ts
import {
    <PERSON>ch,
    Zap,
    Sparkles,
    Bug,
    Scissors,
    Hammer,
    Laptop,
    Thermometer,
    Construction,
    Sun,
    LucideProps
} from 'lucide-react';
import {FC, SVGProps} from "react";

export interface servicesDataType {
    id: string;
    name: string;
    icon: FC<SVGProps<SVGSVGElement>>;
    bgColor: string;
    textColor: string;
    hoverBg: string;
    darkHoverBg: string;
}

export const servicesData:servicesDataType[] = [
    {
        id: 'plumbing',
        name: 'Plumbing',
        icon: Wrench,
        bgColor: 'bg-blue-100',
        textColor: 'text-blue-600',
        hoverBg: 'hover:bg-blue-50',
        darkHoverBg: 'dark:hover:bg-blue-900/20',
    },
    {
        id: 'electrical',
        name: 'Electrical',
        icon: Zap,
        bgColor: 'bg-amber-100',
        textColor: 'text-amber-600',
        hoverBg: 'hover:bg-amber-50',
        darkHoverBg: 'dark:hover:bg-amber-900/20',
    },
    {
        id: 'cleaning',
        name: 'Cleaning',
        icon: Sparkles,
        bgColor: 'bg-cyan-100',
        textColor: 'text-cyan-600',
        hoverBg: 'hover:bg-cyan-50',
        darkHoverBg: 'dark:hover:bg-cyan-900/20',
    },
    {
        id: 'landscaping',
        name: 'Landscaping',
        icon: Scissors,
        bgColor: 'bg-green-100',
        textColor: 'text-green-600',
        hoverBg: 'hover:bg-green-50',
        darkHoverBg: 'dark:hover:bg-green-900/20',
    },
    // {
    //     id: 'pest-control',
    //     name: 'Pest Control',
    //     icon: Bug,
    //     bgColor: 'bg-red-100',
    //     textColor: 'text-red-600',
    //     hoverBg: 'hover:bg-red-50',
    //     darkHoverBg: 'dark:hover:bg-red-900/20',
    // },
    // {
    //     id: 'handyman',
    //     name: 'Handyman',
    //     icon: Hammer,
    //     bgColor: 'bg-orange-100',
    //     textColor: 'text-orange-600',
    //     hoverBg: 'hover:bg-orange-50',
    //     darkHoverBg: 'dark:hover:bg-orange-900/20',
    // },
    // {
    //     id: 'appliance-repair',
    //     name: 'Appliance Repair',
    //     icon: Laptop,
    //     bgColor: 'bg-purple-100',
    //     textColor: 'text-purple-600',
    //     hoverBg: 'hover:bg-purple-50',
    //     darkHoverBg: 'dark:hover:bg-purple-900/20',
    // },
    // {
    //     id: 'hvac',
    //     name: 'HVAC',
    //     icon: Thermometer,
    //     bgColor: 'bg-indigo-100',
    //     textColor: 'text-indigo-600',
    //     hoverBg: 'hover:bg-indigo-50',
    //     darkHoverBg: 'dark:hover:bg-indigo-900/20',
    // },
    // {
    //     id: 'roofing',
    //     name: 'Roofing',
    //     icon: Construction,
    //     bgColor: 'bg-stone-100',
    //     textColor: 'text-stone-600',
    //     hoverBg: 'hover:bg-stone-50',
    //     darkHoverBg: 'dark:hover:bg-stone-900/20',
    // },
    // {
    //     id: 'solar',
    //     name: 'Solar',
    //     icon: Sun,
    //     bgColor: 'bg-yellow-100',
    //     textColor: 'text-yellow-600',
    //     hoverBg: 'hover:bg-yellow-50',
    //     darkHoverBg: 'dark:hover:bg-yellow-900/20',
    // },
];