import { HelpCircle, Banknote, BookOpen } from 'lucide-react';
import {FC, SVGProps} from "react";
export interface ResourceItem {
    id: string;
    path: string;
    name: string;
    icon: FC<SVGProps<SVGSVGElement>>;
}


export const resourcesData: ResourceItem[] = [
    {
        id: 'how-it-works',
        path: '/how-it-works',
        name: 'How It Works',
        icon: HelpCircle,
    },
    {
        id: 'financing',
        path: '/financing',
        name: 'Financing',
        icon: Banknote,
    },
    {
        id: 'blog',
        path: '/blog',
        name: 'Blog',
        icon: BookOpen,
    },
];
