
import {ApiResponse, apiService} from "@/services/api.ts";
import {DataType} from "@/types/common.ts";

export interface Provider {
  id: string;
  name: string;
  avatar: string;
  rating: number;
  reviewCount: number;
  responseTime: string;
  badges: string[];
  specialty: string;
  fullAddress: string;
  phone: string;
  email: string;
  category: string;
}

export interface PaginationData {
  current_page: number;
  per_page: number;
  total: number;
  last_page: number;
}

export interface ProvidersResponse {
  data: DataType[];
  pagination: PaginationData;
}

interface QueryOptions {
  page?: number;
  [key: string]: string | number | boolean | undefined;
}

export const getMockProviders = async (serviceId?: string, options: QueryOptions = {}): Promise<ProvidersResponse> => {
  // Build query parameters
  const queryParams = new URLSearchParams();
  if (serviceId) {
    queryParams.append('category', serviceId);
  }

  // Add page parameter if provided
  if (options.page) {
    queryParams.append('page', options.page.toString());
  }

  // Add any other query parameters
  Object.entries(options).forEach(([key, value]) => {
    if (key !== 'page' && value !== undefined) {
      queryParams.append(key, value.toString());
    }
  });
  const url = `/api/businesses/?${queryParams.toString()}`;

  try {
    const response = await apiService<ProvidersResponse>(url);

    if (response.isSuccess && response.data) {
      return response.data;
    }

    // Return empty response if request fails
    return {
      data: [],
      pagination: {
        current_page: options.page || 1,
        per_page: 15,
        total: 0,
        last_page: 1
      }
    };
  } catch (e) {
    // Return empty response on error
    return {
      data: [],
      pagination: {
        current_page: options.page || 1,
        per_page: 15,
        total: 0,
        last_page: 1
      }
    };
  }
};
