import express from 'express';
import cors from 'cors';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;
const SITEMAP_PATH = path.join(__dirname, '../public/sitemap.xml');

// Middleware
app.use(cors());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'JobON Sitemap API'
  });
});

// Generate sitemap endpoint
app.get('/sitemap/generate', async (req, res) => {
  try {
    console.log('🔄 Generating sitemap via API...');
    
    // Run the sitemap generation script
    execSync('node scripts/generate-dynamic-sitemap-v2.cjs', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    res.json({ 
      success: true, 
      message: 'Sitemap generated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error generating sitemap:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to generate sitemap',
      details: error.message
    });
  }
});

// Serve sitemap.xml endpoint
app.get('/sitemap.xml', (req, res) => {
  try {
    // Check if sitemap exists
    if (!fs.existsSync(SITEMAP_PATH)) {
      return res.status(404).json({ 
        error: 'Sitemap not found. Please generate it first.' 
      });
    }
    
    // Read and serve the sitemap
    const sitemap = fs.readFileSync(SITEMAP_PATH, 'utf8');
    
    // Set appropriate headers
    res.set({
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
    });
    
    res.send(sitemap);
  } catch (error) {
    console.error('Error serving sitemap:', error);
    res.status(500).json({ 
      error: 'Failed to serve sitemap',
      details: error.message
    });
  }
});

// Get sitemap info endpoint
app.get('/sitemap/info', (req, res) => {
  try {
    if (!fs.existsSync(SITEMAP_PATH)) {
      return res.json({ 
        exists: false,
        message: 'Sitemap not found'
      });
    }
    
    const stats = fs.statSync(SITEMAP_PATH);
    const sitemap = fs.readFileSync(SITEMAP_PATH, 'utf8');
    
    // Count URLs in sitemap
    const urlMatches = sitemap.match(/<loc>/g);
    const urlCount = urlMatches ? urlMatches.length : 0;
    
    res.json({
      exists: true,
      size: stats.size,
      lastModified: stats.mtime,
      urlCount: urlCount,
      path: SITEMAP_PATH
    });
  } catch (error) {
    console.error('Error getting sitemap info:', error);
    res.status(500).json({ 
      error: 'Failed to get sitemap info',
      details: error.message
    });
  }
});

// Auto-regenerate sitemap endpoint (with optional force parameter)
app.post('/sitemap/auto-update', async (req, res) => {
  try {
    const force = req.query.force === 'true';
    
    if (!force && fs.existsSync(SITEMAP_PATH)) {
      const stats = fs.statSync(SITEMAP_PATH);
      const ageInHours = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60);
      
      // Only regenerate if sitemap is older than 6 hours
      if (ageInHours < 6) {
        return res.json({
          success: true,
          message: 'Sitemap is recent, no update needed',
          lastModified: stats.mtime,
          ageInHours: Math.round(ageInHours * 100) / 100
        });
      }
    }
    
    // Regenerate sitemap
    console.log('🔄 Auto-updating sitemap...');
    execSync('node scripts/generate-dynamic-sitemap-v2.cjs', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    res.json({ 
      success: true, 
      message: 'Sitemap auto-updated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error auto-updating sitemap:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to auto-update sitemap',
      details: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 JobON Sitemap API server running on port ${PORT}`);
  console.log(`📍 Endpoints:`);
  console.log(`   - Health: http://localhost:${PORT}/health`);
  console.log(`   - Sitemap: http://localhost:${PORT}/sitemap.xml`);
  console.log(`   - Generate: http://localhost:${PORT}/sitemap/generate`);
  console.log(`   - Info: http://localhost:${PORT}/sitemap/info`);
  console.log(`   - Auto-update: POST http://localhost:${PORT}/sitemap/auto-update`);
});

export default app;
