const fs = require('fs');
const axios = require('axios');
const path = require('path');

// Configuration
const BASE_URL = 'https://jobon.app';
const WORDPRESS_API_URL = 'https://blog.jobon.app/wp-json/wp/v2';
const SITEMAP_PATH = path.join(__dirname, '../public/sitemap.xml');

// Static pages configuration
const STATIC_PAGES = [
  {
    url: '/',
    priority: 1.0,
    changefreq: 'daily'
  },
  {
    url: '/about',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/how-it-works',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/create-job',
    priority: 0.9,
    changefreq: 'monthly'
  },
  {
    url: '/jobs',
    priority: 0.8,
    changefreq: 'daily'
  },
  {
    url: '/professionals',
    priority: 0.9,
    changefreq: 'daily'
  },
  {
    url: '/for-providers',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/blog',
    priority: 0.8,
    changefreq: 'daily'
  },
  {
    url: '/faq',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/careers',
    priority: 0.6,
    changefreq: 'monthly'
  },
  {
    url: '/financing',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/financing-waitlist',
    priority: 0.6,
    changefreq: 'monthly'
  },
  {
    url: '/provider-signup',
    priority: 0.8,
    changefreq: 'monthly'
  },
  // Service and Professionals Routes
  {
    url: '/services/plumbing',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/professionals/plumbing',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/services/electrical',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/professionals/electrical',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/services/landscaping',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/professionals/landscaping',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/services/cleaning',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/professionals/cleaning',
    priority: 0.8,
    changefreq: 'monthly'
  },
  // Legal Pages
  {
    url: '/terms',
    priority: 0.3,
    changefreq: 'yearly'
  },
  {
    url: '/privacy',
    priority: 0.3,
    changefreq: 'yearly'
  },
  {
    url: '/cookies',
    priority: 0.3,
    changefreq: 'yearly'
  }
];

// Helper functions
function formatDate(date) {
  return new Date(date).toISOString().split('T')[0];
}

function escapeXml(unsafe) {
  if (!unsafe) return '';
  return unsafe.toString()
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

function createUrlEntry(url, lastmod, changefreq, priority) {
  return `  <url>
    <loc>${escapeXml(BASE_URL + url)}</loc>
    ${lastmod ? `<lastmod>${lastmod}</lastmod>` : ''}
    ${changefreq ? `<changefreq>${changefreq}</changefreq>` : ''}
    ${priority ? `<priority>${priority}</priority>` : ''}
  </url>`;
}

// Convert WordPress post to our format (simplified version)
function convertWordPressPost(post) {
  return {
    id: post.id.toString(),
    title: post.title.rendered,
    slug: post.slug,
    date: post.date,
    modified: post.modified
  };
}

// Fetch posts using WordPress API (similar to the existing fetchPosts function)
async function fetchPosts(page = 1, perPage = 100) {
  try {
    const url = `${WORDPRESS_API_URL}/posts?_embed&page=${page}&per_page=${perPage}`;
    const response = await axios.get(url);
    const posts = response.data.map(convertWordPressPost);

    // Get total pages from headers
    const totalPages = parseInt(response.headers['x-wp-totalpages'] || '1', 10);

    return { posts, totalPages };
  } catch (error) {
    console.error('Error fetching posts:', error.message);
    return { posts: [], totalPages: 0 };
  }
}

// Fetch categories using WordPress API (similar to the existing fetchCategories function)
async function fetchCategories() {
  try {
    const response = await axios.get(`${WORDPRESS_API_URL}/categories?per_page=100`);
    return response.data;
  } catch (error) {
    console.error('Error fetching categories:', error.message);
    return [];
  }
}

// Fetch all posts using pagination
async function fetchAllPosts() {
  console.log('Fetching posts using WordPress API...');

  const allPosts = [];
  let page = 1;
  let hasMore = true;

  try {
    while (hasMore) {
      console.log(`Fetching page ${page}...`);
      const { posts, totalPages } = await fetchPosts(page, 100);

      if (posts.length === 0) {
        hasMore = false;
        break;
      }

      allPosts.push(...posts);

      if (page >= totalPages) {
        hasMore = false;
      } else {
        page++;
      }
    }

    console.log(`Total posts fetched: ${allPosts.length}`);
    return allPosts;
  } catch (error) {
    console.error('Error fetching posts:', error.message);
    return [];
  }
}

// Generate sitemap XML
async function generateSitemap() {
  console.log('Starting sitemap generation...');

  const urls = [];

  // Add static pages
  console.log('\nAdding static pages...');
  for (const page of STATIC_PAGES) {
    const lastmod = formatDate(new Date());
    urls.push(createUrlEntry(page.url, lastmod, page.changefreq, page.priority));
  }
  console.log(`Added ${STATIC_PAGES.length} static pages`);

  // Fetch and add blog posts
  console.log('\nFetching blog posts...');
  const posts = await fetchAllPosts();

  for (const post of posts) {
    const url = `/blog/${post.slug}`;
    const lastmod = formatDate(post.modified || post.date);
    const priority = 0.6;
    const changefreq = 'monthly';

    urls.push(createUrlEntry(url, lastmod, changefreq, priority));
  }
  console.log(`Added ${posts.length} blog posts`);

  // Fetch and add categories
  console.log('\nFetching categories...');
  const categories = await fetchCategories();

  // Filter to only include specific categories
  const allowedCategorySlugs = ['cleaning', 'plumber', 'electrician', 'landscaping'];
  const allowedCategories = categories.filter(cat => 
    cat.count > 0 && allowedCategorySlugs.includes(cat.slug.toLowerCase())
  );

  for (const category of allowedCategories) {
    const url = `/blog/category/${category.slug}`;
    const lastmod = formatDate(new Date());
    const priority = 0.5;
    const changefreq = 'weekly';

    urls.push(createUrlEntry(url, lastmod, changefreq, priority));
  }
  console.log(`Added ${allowedCategories.length} categories (${allowedCategories.map(c => c.name).join(', ')})`);

  // Generate XML
  console.log('\nGenerating XML...');
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls.join('\n')}
</urlset>`;

  // Write to file
  try {
    fs.writeFileSync(SITEMAP_PATH, xml);
    console.log(`\n✅ Sitemap generated successfully!`);
    console.log(`📍 Location: ${SITEMAP_PATH}`);
    console.log(`📊 Total URLs: ${urls.length}`);
    console.log(`   - Static pages: ${STATIC_PAGES.length}`);
    console.log(`   - Blog posts: ${posts.length}`);
    console.log(`   - Categories: ${allowedCategories.length}`);
  } catch (error) {
    console.error('\n❌ Error writing sitemap:', error.message);
    process.exit(1);
  }
}

// Run the sitemap generation
generateSitemap().catch(error => {
  console.error('\n❌ Fatal error:', error.message);
  process.exit(1);
});
