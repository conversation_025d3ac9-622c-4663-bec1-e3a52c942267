import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🔄 Updating sitemap...\n');

// Run sitemap generation
try {
  execSync('node scripts/generate-dynamic-sitemap-v2.cjs', { 
    stdio: 'inherit',
    cwd: join(__dirname, '..')
  });
  console.log('\n🎉 Sitemap updated successfully!');
} catch (error) {
  console.error('\n❌ Error updating sitemap:', error.message);
  process.exit(1);
}
