import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Starting build process with dynamic sitemap generation...\n');

// Step 1: Generate sitemap
console.log('📝 Step 1: Generating dynamic sitemap...');
try {
  execSync('node scripts/generate-dynamic-sitemap-v2.cjs', { 
    stdio: 'inherit',
    cwd: join(__dirname, '..')
  });
  console.log('✅ Sitemap generated successfully!\n');
} catch (error) {
  console.error('❌ Error generating sitemap:', error.message);
  process.exit(1);
}

// Step 2: Build the project
console.log('🔨 Step 2: Building the project...');
try {
  execSync('vite build', { 
    stdio: 'inherit',
    cwd: join(__dirname, '..')
  });
  console.log('✅ Build completed successfully!\n');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

console.log('🎉 Build process completed with dynamic sitemap!');
