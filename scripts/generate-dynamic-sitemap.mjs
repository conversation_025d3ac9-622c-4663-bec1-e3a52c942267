import fs from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuration
const BASE_URL = 'https://jobon.app';
const SITEMAP_PATH = join(__dirname, '../public/sitemap.xml');

// Import the WordPress API functions
import { fetchPosts, fetchCategories } from '../src/services/wordpressApi.ts';

// Static pages configuration
const STATIC_PAGES = [
  {
    url: '/',
    priority: 1.0,
    changefreq: 'daily'
  },
  {
    url: '/about-us',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/how-it-works',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/create-job',
    priority: 0.9,
    changefreq: 'monthly'
  },
  {
    url: '/jobs',
    priority: 0.8,
    changefreq: 'daily'
  },
  {
    url: '/professionals-search',
    priority: 0.9,
    changefreq: 'daily'
  },
  {
    url: '/for-providers',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/blog',
    priority: 0.8,
    changefreq: 'daily'
  },
  // Free Tools Pages
  {
    url: '/free-tools',
    priority: 0.9,
    changefreq: 'weekly'
  },
  {
    url: '/free-tools/pricing-calculator',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/profit-calculator',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/labor-cost-calculator',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/house-cleaning-calculator',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/lawn-care-calculator',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/roofing-calculator',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/scheduling-assistant',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/estimate-template',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/invoice-templates',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/receipt-generator',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/work-order-template',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/profit-forecast',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/image-editor',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/pricing-guide',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/marketing-guide',
    priority: 0.7,
    changefreq: 'monthly'
  },
  {
    url: '/free-tools/operations-guide',
    priority: 0.7,
    changefreq: 'monthly'
  },
  // Service Pages
  {
    url: '/cleaning-service',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/electrical-service',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/plumbing-service',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/handyman-service',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/hvac-service',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/roofing-service',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/landscaping-service',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/solar-service',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/appliance-repair-service',
    priority: 0.8,
    changefreq: 'monthly'
  },
  {
    url: '/pest-control-service',
    priority: 0.8,
    changefreq: 'monthly'
  },
  // Legal Pages
  {
    url: '/terms',
    priority: 0.3,
    changefreq: 'yearly'
  },
  {
    url: '/privacy',
    priority: 0.3,
    changefreq: 'yearly'
  },
  {
    url: '/cookies',
    priority: 0.3,
    changefreq: 'yearly'
  }
];

// Helper functions
function formatDate(date) {
  return new Date(date).toISOString().split('T')[0];
}

function escapeXml(unsafe) {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

function createUrlEntry(url, lastmod, changefreq, priority) {
  return `  <url>
    <loc>${escapeXml(BASE_URL + url)}</loc>
    ${lastmod ? `<lastmod>${lastmod}</lastmod>` : ''}
    ${changefreq ? `<changefreq>${changefreq}</changefreq>` : ''}
    ${priority ? `<priority>${priority}</priority>` : ''}
  </url>`;
}

// Fetch all posts using the existing API function
async function fetchAllPosts() {
  console.log('Fetching posts using existing WordPress API function...');
  
  const allPosts = [];
  let page = 1;
  let hasMore = true;

  try {
    while (hasMore) {
      console.log(`Fetching page ${page}...`);
      const { posts, totalPages } = await fetchPosts(page, 100);
      
      if (posts.length === 0) {
        hasMore = false;
        break;
      }
      
      allPosts.push(...posts);
      
      if (page >= totalPages) {
        hasMore = false;
      } else {
        page++;
      }
    }

    console.log(`Total posts fetched: ${allPosts.length}`);
    return allPosts;
  } catch (error) {
    console.error('Error fetching posts:', error.message);
    return [];
  }
}

// Generate sitemap XML
async function generateSitemap() {
  console.log('Starting sitemap generation...');

  const urls = [];

  // Add static pages
  console.log('\nAdding static pages...');
  for (const page of STATIC_PAGES) {
    const lastmod = formatDate(new Date());
    urls.push(createUrlEntry(page.url, lastmod, page.changefreq, page.priority));
  }
  console.log(`Added ${STATIC_PAGES.length} static pages`);

  // Fetch and add blog posts
  console.log('\nFetching blog posts...');
  const posts = await fetchAllPosts();
  
  for (const post of posts) {
    const url = `/blog/${post.slug}`;
    const lastmod = formatDate(post.date);
    const priority = 0.6;
    const changefreq = 'monthly';
    
    urls.push(createUrlEntry(url, lastmod, changefreq, priority));
  }
  console.log(`Added ${posts.length} blog posts`);

  // Fetch and add categories
  console.log('\nFetching categories...');
  const categories = await fetchCategories();
  
  const categoriesWithPosts = categories.filter(cat => cat.count > 0);
  
  for (const category of categoriesWithPosts) {
    const url = `/blog/category/${category.slug}`;
    const lastmod = formatDate(new Date());
    const priority = 0.5;
    const changefreq = 'weekly';
    
    urls.push(createUrlEntry(url, lastmod, changefreq, priority));
  }
  console.log(`Added ${categoriesWithPosts.length} categories`);

  // Generate XML
  console.log('\nGenerating XML...');
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls.join('\n')}
</urlset>`;

  // Write to file
  try {
    fs.writeFileSync(SITEMAP_PATH, xml);
    console.log(`\n✅ Sitemap generated successfully!`);
    console.log(`📍 Location: ${SITEMAP_PATH}`);
    console.log(`📊 Total URLs: ${urls.length}`);
    console.log(`   - Static pages: ${STATIC_PAGES.length}`);
    console.log(`   - Blog posts: ${posts.length}`);
    console.log(`   - Categories: ${categoriesWithPosts.length}`);
  } catch (error) {
    console.error('\n❌ Error writing sitemap:', error.message);
    process.exit(1);
  }
}

// Run the sitemap generation
generateSitemap().catch(error => {
  console.error('\n❌ Fatal error:', error.message);
  process.exit(1);
});
